# 本地环境
ENV = development

# 本地环境接口地址
VITE_API_URL = http://localhost:5005

# 登陆界面默认用户
VITE_DEFAULT_USER = superadmin

# 登陆界面默认密码
VITE_DEFAULT_USER_PASSWORD = 123456

# SignalR实时订阅服务配置
VITE_ENABLE_SIGNALR = true
VITE_SIGNALR_HUB_URL = /hubs/realtime-subscription

# MQTT配置
VITE_MQTT_DEFAULT_PORT = 1883
VITE_MQTT_DEFAULT_WS_PORT = 8083
VITE_MQTT_DEFAULT_SSL_PORT = 8883

# 调试配置
VITE_DEBUG_MODE = true
VITE_LOG_LEVEL = debug