/* tslint:disable */
 
/**
 * ApprovalFlow
 * <br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
/**
 * 审批流更新输入参数
 * @export
 * @interface UpdateApprovalFlowInput
 */
export interface UpdateApprovalFlowInput {
    /**
     * 创建时间
     * @type {Date}
     * @memberof UpdateApprovalFlowInput
     */
    createTime?: Date;
    /**
     * 更新时间
     * @type {Date}
     * @memberof UpdateApprovalFlowInput
     */
    updateTime?: Date | null;
    /**
     * 创建者Id
     * @type {number}
     * @memberof UpdateApprovalFlowInput
     */
    createUserId?: number | null;
    /**
     * 创建者姓名
     * @type {string}
     * @memberof UpdateApprovalFlowInput
     */
    createUserName?: string | null;
    /**
     * 修改者Id
     * @type {number}
     * @memberof UpdateApprovalFlowInput
     */
    updateUserId?: number | null;
    /**
     * 修改者姓名
     * @type {string}
     * @memberof UpdateApprovalFlowInput
     */
    updateUserName?: string | null;
    /**
     * 软删除
     * @type {boolean}
     * @memberof UpdateApprovalFlowInput
     */
    isDelete?: boolean;
    /**
     * 创建者部门Id
     * @type {number}
     * @memberof UpdateApprovalFlowInput
     */
    createOrgId?: number | null;
    /**
     * 创建者部门名称
     * @type {string}
     * @memberof UpdateApprovalFlowInput
     */
    createOrgName?: string | null;
    /**
     * 编号
     * @type {string}
     * @memberof UpdateApprovalFlowInput
     */
    code?: string | null;
    /**
     * 名称
     * @type {string}
     * @memberof UpdateApprovalFlowInput
     */
    name?: string | null;
    /**
     * 表单
     * @type {string}
     * @memberof UpdateApprovalFlowInput
     */
    formJson?: string | null;
    /**
     * 流程
     * @type {string}
     * @memberof UpdateApprovalFlowInput
     */
    flowJson?: string | null;
    /**
     * 状态
     * @type {number}
     * @memberof UpdateApprovalFlowInput
     */
    status?: number | null;
    /**
     * 备注
     * @type {string}
     * @memberof UpdateApprovalFlowInput
     */
    remark?: string | null;
    /**
     * 主键Id
     * @type {number}
     * @memberof UpdateApprovalFlowInput
     */
    id: number;
}
