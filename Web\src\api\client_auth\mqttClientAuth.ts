﻿import {useBaseA<PERSON>} from '/@/api/base';

// MQTT 客户端认证信息表接口服务
export const useMqttClientAuthApi = () => {
	const baseApi = useBaseApi("mqttClientAuth");
	return {
		// 分页查询MQTT 客户端认证信息表
		page: baseApi.page,
		// 查看MQTT 客户端认证信息表详细
		detail: baseApi.detail,
		// 新增MQTT 客户端认证信息表
		add: baseApi.add,
		// 更新MQTT 客户端认证信息表
		update: baseApi.update,
		// 删除MQTT 客户端认证信息表
		delete: baseApi.delete,
		// 批量删除MQTT 客户端认证信息表
		batchDelete: baseApi.batchDelete,
	}
}

// MQTT 客户端认证信息表实体
export interface MqttClientAuth {
	// 主键Id
	id: number;
	// 实例ID
	instanceId: number;
	// 客户端ID
	clientId: string;
	// 客户端引用ID
	clientRefId: number;
	// 认证模式（Username、AliyunSignature、JWT等）
	authMode: string;
	// 用户名
	username: string;
	// 密码
	password: string;
	// 密码哈希
	passwordHash: string;
	// 密码盐值
	passwordSalt: string;
	// AccessKey ID（阿里云认证）
	accessKeyId: string;
	// AccessKey Secret（加密存储）
	accessKeySecret: string;
	// 签名算法（hmacmd5、hmacsha1、hmacsha256）
	signMethod: string;
	// JWT密钥
	jwtSecret: string;
	// JWT过期时间（秒）
	jwtExpiry: number;
	// JWT令牌
	jwtToken: string;
	// 客户端证书
	certificate: string;
	// 私钥
	privateKey: string;
	// CA证书
	caCertificate: string;
	// 是否启用（0:否，1:是）
	isEnabled: boolean;
	// 允许的IP地址列表
	allowedIPs: string;
	// 拒绝的IP地址列表
	deniedIPs: string;
	// 速率限制（消息/秒）
	rateLimit: number;
	// 登录尝试次数
	loginAttempts: number;
	// 过期时间
	expireTime: string;
	// 最后认证时间
	lastAuthTime: string;
	// 最后登录时间
	lastLoginTime: string;
	// 最后登录IP
	lastLoginIP: string;
	// 认证成功次数
	successCount: number;
	// 认证成功总数
	authSuccessCount: number;
	// 认证失败次数
	failedCount: number;
	// 是否锁定（0:未锁定，1:已锁定）
	isLocked: boolean;
	// 锁定时间
	lockTime: string;
	// 解锁时间
	unlockTime: string;
	// 锁定原因
	lockReason: string;
	// 最大失败次数
	maxFailedCount: number;
	// 锁定持续时间（分钟）
	lockDuration: number;
	// IP白名单
	ipWhitelist: string;
	// IP黑名单
	ipBlacklist: string;
	// 允许的主题列表
	allowedTopics: string;
	// 拒绝的主题列表
	deniedTopics: string;
	// 最大连接数
	maxConnections: number;
	// 当前连接数
	currentConnections: number;
	// 连接超时时间（秒）
	connectionTimeout: number;
	// 最大消息大小
	maxMessageSize: number;
	// 最大订阅数
	maxSubscriptions: number;
	// 消息速率限制
	messageRateLimit: number;
	// 字节速率限制
	byteRateLimit: number;
	// 扩展属性（JSON）
	extendedProperties: string;
	// 
	key: string;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 修改者Id
	updateUserId: number;
	// 软删除
	isDelete?: boolean;
	// 创建者姓名
	createUserName: string;
	// 修改者姓名
	updateUserName: string;
}