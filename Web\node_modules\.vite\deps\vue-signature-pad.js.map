{"version": 3, "sources": ["../../signature_pad/src/point.ts", "../../signature_pad/src/bezier.ts", "../../signature_pad/src/throttle.ts", "../../signature_pad/src/signature_pad.ts", "../../merge-images/src/index.js", "../../vue-signature-pad/dist/vue-signature-pad.esm.js"], "sourcesContent": ["// Interface for point data structure used e.g. in SignaturePad#fromData method\nexport interface BasicPoint {\n  x: number;\n  y: number;\n  time: number;\n}\n\nexport class Point implements BasicPoint {\n  public time: number;\n\n  constructor(public x: number, public y: number, time?: number) {\n    this.time = time || Date.now();\n  }\n\n  public distanceTo(start: BasicPoint): number {\n    return Math.sqrt(\n      Math.pow(this.x - start.x, 2) + Math.pow(this.y - start.y, 2),\n    );\n  }\n\n  public equals(other: BasicPoint): boolean {\n    return this.x === other.x && this.y === other.y && this.time === other.time;\n  }\n\n  public velocityFrom(start: BasicPoint): number {\n    return this.time !== start.time\n      ? this.distanceTo(start) / (this.time - start.time)\n      : 0;\n  }\n}\n", "import { BasicPoint, Point } from './point';\n\nexport class Bezier {\n  public static fromPoints(\n    points: Point[],\n    widths: { start: number; end: number },\n  ): Bezier {\n    const c2 = this.calculateControlPoints(points[0], points[1], points[2]).c2;\n    const c3 = this.calculateControlPoints(points[1], points[2], points[3]).c1;\n\n    return new Bezier(points[1], c2, c3, points[2], widths.start, widths.end);\n  }\n\n  private static calculateControlPoints(\n    s1: BasicPoint,\n    s2: BasicPoint,\n    s3: BasicPoint,\n  ): {\n    c1: BasicPoint;\n    c2: BasicPoint;\n  } {\n    const dx1 = s1.x - s2.x;\n    const dy1 = s1.y - s2.y;\n    const dx2 = s2.x - s3.x;\n    const dy2 = s2.y - s3.y;\n\n    const m1 = { x: (s1.x + s2.x) / 2.0, y: (s1.y + s2.y) / 2.0 };\n    const m2 = { x: (s2.x + s3.x) / 2.0, y: (s2.y + s3.y) / 2.0 };\n\n    const l1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);\n    const l2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);\n\n    const dxm = m1.x - m2.x;\n    const dym = m1.y - m2.y;\n\n    const k = l2 / (l1 + l2);\n    const cm = { x: m2.x + dxm * k, y: m2.y + dym * k };\n\n    const tx = s2.x - cm.x;\n    const ty = s2.y - cm.y;\n\n    return {\n      c1: new Point(m1.x + tx, m1.y + ty),\n      c2: new Point(m2.x + tx, m2.y + ty),\n    };\n  }\n\n  constructor(\n    public startPoint: Point,\n    public control2: BasicPoint,\n    public control1: BasicPoint,\n    public endPoint: Point,\n    public startWidth: number,\n    public endWidth: number,\n  ) {}\n\n  // Returns approximated length. Code taken from https://www.lemoda.net/maths/bezier-length/index.html.\n  public length(): number {\n    const steps = 10;\n    let length = 0;\n    let px;\n    let py;\n\n    for (let i = 0; i <= steps; i += 1) {\n      const t = i / steps;\n      const cx = this.point(\n        t,\n        this.startPoint.x,\n        this.control1.x,\n        this.control2.x,\n        this.endPoint.x,\n      );\n      const cy = this.point(\n        t,\n        this.startPoint.y,\n        this.control1.y,\n        this.control2.y,\n        this.endPoint.y,\n      );\n\n      if (i > 0) {\n        const xdiff = cx - (px as number);\n        const ydiff = cy - (py as number);\n\n        length += Math.sqrt(xdiff * xdiff + ydiff * ydiff);\n      }\n\n      px = cx;\n      py = cy;\n    }\n\n    return length;\n  }\n\n  // Calculate parametric value of x or y given t and the four point coordinates of a cubic bezier curve.\n  private point(\n    t: number,\n    start: number,\n    c1: number,\n    c2: number,\n    end: number,\n  ): number {\n    // prettier-ignore\n    return (       start * (1.0 - t) * (1.0 - t)  * (1.0 - t))\n         + (3.0 *  c1    * (1.0 - t) * (1.0 - t)  * t)\n         + (3.0 *  c2    * (1.0 - t) * t          * t)\n         + (       end   * t         * t          * t);\n  }\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n// Slightly simplified version of http://stackoverflow.com/a/27078401/815507\n\nexport function throttle(\n  fn: (...args: any[]) => any,\n  wait = 250,\n): (this: any, ...args: any[]) => any {\n  let previous = 0;\n  let timeout: number | null = null;\n  let result: any;\n  let storedContext: any;\n  let storedArgs: any[];\n\n  const later = (): void => {\n    previous = Date.now();\n    timeout = null;\n    result = fn.apply(storedContext, storedArgs);\n\n    if (!timeout) {\n      storedContext = null;\n      storedArgs = [];\n    }\n  };\n\n  return function wrapper(this: any, ...args: any[]): any {\n    const now = Date.now();\n    const remaining = wait - (now - previous);\n\n    storedContext = this;\n    storedArgs = args;\n\n    if (remaining <= 0 || remaining > wait) {\n      if (timeout) {\n        clearTimeout(timeout);\n        timeout = null;\n      }\n\n      previous = now;\n      result = fn.apply(storedContext, storedArgs);\n\n      if (!timeout) {\n        storedContext = null;\n        storedArgs = [];\n      }\n    } else if (!timeout) {\n      timeout = window.setTimeout(later, remaining);\n    }\n\n    return result;\n  };\n}\n", "/**\n * The main idea and some parts of the code (e.g. drawing variable width Bézier curve) are taken from:\n * http://corner.squareup.com/2012/07/smoother-signatures.html\n *\n * Implementation of interpolation using cubic Bézier curves is taken from:\n * http://www.benknowscode.com/2012/09/path-interpolation-using-cubic-bezier_9742.html\n *\n * Algorithm for approximated length of a Bézier curve is taken from:\n * http://www.lemoda.net/maths/bezier-length/index.html\n */\n\nimport { Bezier } from './bezier';\nimport { BasicPoint, Point } from './point';\nimport { throttle } from './throttle';\n\ndeclare global {\n  interface CSSStyleDeclaration {\n    msTouchAction: string;\n  }\n}\n\nexport interface Options {\n  dotSize?: number | (() => number);\n  minWidth?: number;\n  maxWidth?: number;\n  minDistance?: number;\n  backgroundColor?: string;\n  penColor?: string;\n  throttle?: number;\n  velocityFilterWeight?: number;\n  onBegin?: (event: MouseEvent | Touch) => void;\n  onEnd?: (event: MouseEvent | Touch) => void;\n}\n\nexport interface PointGroup {\n  color: string;\n  points: BasicPoint[];\n}\n\nexport default class SignaturePad {\n  // Public stuff\n  public dotSize: number | (() => number);\n  public minWidth: number;\n  public maxWidth: number;\n  public minDistance: number;\n  public backgroundColor: string;\n  public penColor: string;\n  public throttle: number;\n  public velocityFilterWeight: number;\n  public onBegin?: (event: MouseEvent | Touch) => void;\n  public onEnd?: (event: MouseEvent | Touch) => void;\n\n  // Private stuff\n  /* tslint:disable: variable-name */\n  private _ctx: CanvasRenderingContext2D;\n  private _mouseButtonDown: boolean;\n  private _isEmpty: boolean;\n  private _lastPoints: Point[]; // Stores up to 4 most recent points; used to generate a new curve\n  private _data: PointGroup[]; // Stores all points in groups (one group per line or dot)\n  private _lastVelocity: number;\n  private _lastWidth: number;\n  private _strokeMoveUpdate: (event: MouseEvent | Touch) => void;\n  /* tslint:enable: variable-name */\n\n  constructor(\n    private canvas: HTMLCanvasElement,\n    private options: Options = {},\n  ) {\n    this.velocityFilterWeight = options.velocityFilterWeight || 0.7;\n    this.minWidth = options.minWidth || 0.5;\n    this.maxWidth = options.maxWidth || 2.5;\n    this.throttle = ('throttle' in options ? options.throttle : 16) as number; // in milisecondss\n    this.minDistance = ('minDistance' in options\n      ? options.minDistance\n      : 5) as number; // in pixels\n    this.dotSize =\n      options.dotSize ||\n      function dotSize(this: SignaturePad): number {\n        return (this.minWidth + this.maxWidth) / 2;\n      };\n    this.penColor = options.penColor || 'black';\n    this.backgroundColor = options.backgroundColor || 'rgba(0,0,0,0)';\n    this.onBegin = options.onBegin;\n    this.onEnd = options.onEnd;\n\n    this._strokeMoveUpdate = this.throttle\n      ? throttle(SignaturePad.prototype._strokeUpdate, this.throttle)\n      : SignaturePad.prototype._strokeUpdate;\n    this._ctx = canvas.getContext('2d') as CanvasRenderingContext2D;\n\n    this.clear();\n\n    // Enable mouse and touch event handlers\n    this.on();\n  }\n\n  public clear(): void {\n    const { _ctx: ctx, canvas } = this;\n\n    // Clear canvas using background color\n    ctx.fillStyle = this.backgroundColor;\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n    ctx.fillRect(0, 0, canvas.width, canvas.height);\n\n    this._data = [];\n    this._reset();\n    this._isEmpty = true;\n  }\n\n  public fromDataURL(\n    dataUrl: string,\n    options: { ratio?: number; width?: number; height?: number } = {},\n    callback?: (error?: string | Event) => void,\n  ): void {\n    const image = new Image();\n    const ratio = options.ratio || window.devicePixelRatio || 1;\n    const width = options.width || this.canvas.width / ratio;\n    const height = options.height || this.canvas.height / ratio;\n\n    this._reset();\n\n    image.onload = (): void => {\n      this._ctx.drawImage(image, 0, 0, width, height);\n      if (callback) {\n        callback();\n      }\n    };\n    image.onerror = (error): void => {\n      if (callback) {\n        callback(error);\n      }\n    };\n    image.src = dataUrl;\n\n    this._isEmpty = false;\n  }\n\n  public toDataURL(type = 'image/png', encoderOptions?: number): string {\n    switch (type) {\n      case 'image/svg+xml':\n        return this._toSVG();\n      default:\n        return this.canvas.toDataURL(type, encoderOptions);\n    }\n  }\n\n  public on(): void {\n    // Disable panning/zooming when touching canvas element\n    this.canvas.style.touchAction = 'none';\n    this.canvas.style.msTouchAction = 'none';\n\n    if (window.PointerEvent) {\n      this._handlePointerEvents();\n    } else {\n      this._handleMouseEvents();\n\n      if ('ontouchstart' in window) {\n        this._handleTouchEvents();\n      }\n    }\n  }\n\n  public off(): void {\n    // Enable panning/zooming when touching canvas element\n    this.canvas.style.touchAction = 'auto';\n    this.canvas.style.msTouchAction = 'auto';\n\n    this.canvas.removeEventListener('pointerdown', this._handleMouseDown);\n    this.canvas.removeEventListener('pointermove', this._handleMouseMove);\n    document.removeEventListener('pointerup', this._handleMouseUp);\n\n    this.canvas.removeEventListener('mousedown', this._handleMouseDown);\n    this.canvas.removeEventListener('mousemove', this._handleMouseMove);\n    document.removeEventListener('mouseup', this._handleMouseUp);\n\n    this.canvas.removeEventListener('touchstart', this._handleTouchStart);\n    this.canvas.removeEventListener('touchmove', this._handleTouchMove);\n    this.canvas.removeEventListener('touchend', this._handleTouchEnd);\n  }\n\n  public isEmpty(): boolean {\n    return this._isEmpty;\n  }\n\n  public fromData(pointGroups: PointGroup[]): void {\n    this.clear();\n\n    this._fromData(\n      pointGroups,\n      ({ color, curve }) => this._drawCurve({ color, curve }),\n      ({ color, point }) => this._drawDot({ color, point }),\n    );\n\n    this._data = pointGroups;\n  }\n\n  public toData(): PointGroup[] {\n    return this._data;\n  }\n\n  // Event handlers\n  private _handleMouseDown = (event: MouseEvent): void => {\n    if (event.which === 1) {\n      this._mouseButtonDown = true;\n      this._strokeBegin(event);\n    }\n  };\n\n  private _handleMouseMove = (event: MouseEvent): void => {\n    if (this._mouseButtonDown) {\n      this._strokeMoveUpdate(event);\n    }\n  };\n\n  private _handleMouseUp = (event: MouseEvent): void => {\n    if (event.which === 1 && this._mouseButtonDown) {\n      this._mouseButtonDown = false;\n      this._strokeEnd(event);\n    }\n  };\n\n  private _handleTouchStart = (event: TouchEvent): void => {\n    // Prevent scrolling.\n    event.preventDefault();\n\n    if (event.targetTouches.length === 1) {\n      const touch = event.changedTouches[0];\n      this._strokeBegin(touch);\n    }\n  };\n\n  private _handleTouchMove = (event: TouchEvent): void => {\n    // Prevent scrolling.\n    event.preventDefault();\n\n    const touch = event.targetTouches[0];\n    this._strokeMoveUpdate(touch);\n  };\n\n  private _handleTouchEnd = (event: TouchEvent): void => {\n    const wasCanvasTouched = event.target === this.canvas;\n    if (wasCanvasTouched) {\n      event.preventDefault();\n\n      const touch = event.changedTouches[0];\n      this._strokeEnd(touch);\n    }\n  };\n\n  // Private methods\n  private _strokeBegin(event: MouseEvent | Touch): void {\n    const newPointGroup = {\n      color: this.penColor,\n      points: [],\n    };\n\n    if (typeof this.onBegin === 'function') {\n      this.onBegin(event);\n    }\n\n    this._data.push(newPointGroup);\n    this._reset();\n    this._strokeUpdate(event);\n  }\n\n  private _strokeUpdate(event: MouseEvent | Touch): void {\n    if (this._data.length === 0) {\n      // This can happen if clear() was called while a signature is still in progress,\n      // or if there is a race condition between start/update events.\n      this._strokeBegin(event);\n      return;\n    }\n\n    const x = event.clientX;\n    const y = event.clientY;\n\n    const point = this._createPoint(x, y);\n    const lastPointGroup = this._data[this._data.length - 1];\n    const lastPoints = lastPointGroup.points;\n    const lastPoint =\n      lastPoints.length > 0 && lastPoints[lastPoints.length - 1];\n    const isLastPointTooClose = lastPoint\n      ? point.distanceTo(lastPoint) <= this.minDistance\n      : false;\n    const color = lastPointGroup.color;\n\n    // Skip this point if it's too close to the previous one\n    if (!lastPoint || !(lastPoint && isLastPointTooClose)) {\n      const curve = this._addPoint(point);\n\n      if (!lastPoint) {\n        this._drawDot({ color, point });\n      } else if (curve) {\n        this._drawCurve({ color, curve });\n      }\n\n      lastPoints.push({\n        time: point.time,\n        x: point.x,\n        y: point.y,\n      });\n    }\n  }\n\n  private _strokeEnd(event: MouseEvent | Touch): void {\n    this._strokeUpdate(event);\n\n    if (typeof this.onEnd === 'function') {\n      this.onEnd(event);\n    }\n  }\n\n  private _handlePointerEvents(): void {\n    this._mouseButtonDown = false;\n\n    this.canvas.addEventListener('pointerdown', this._handleMouseDown);\n    this.canvas.addEventListener('pointermove', this._handleMouseMove);\n    document.addEventListener('pointerup', this._handleMouseUp);\n  }\n\n  private _handleMouseEvents(): void {\n    this._mouseButtonDown = false;\n\n    this.canvas.addEventListener('mousedown', this._handleMouseDown);\n    this.canvas.addEventListener('mousemove', this._handleMouseMove);\n    document.addEventListener('mouseup', this._handleMouseUp);\n  }\n\n  private _handleTouchEvents(): void {\n    this.canvas.addEventListener('touchstart', this._handleTouchStart);\n    this.canvas.addEventListener('touchmove', this._handleTouchMove);\n    this.canvas.addEventListener('touchend', this._handleTouchEnd);\n  }\n\n  // Called when a new line is started\n  private _reset(): void {\n    this._lastPoints = [];\n    this._lastVelocity = 0;\n    this._lastWidth = (this.minWidth + this.maxWidth) / 2;\n    this._ctx.fillStyle = this.penColor;\n  }\n\n  private _createPoint(x: number, y: number): Point {\n    const rect = this.canvas.getBoundingClientRect();\n\n    return new Point(x - rect.left, y - rect.top, new Date().getTime());\n  }\n\n  // Add point to _lastPoints array and generate a new curve if there are enough points (i.e. 3)\n  private _addPoint(point: Point): Bezier | null {\n    const { _lastPoints } = this;\n\n    _lastPoints.push(point);\n\n    if (_lastPoints.length > 2) {\n      // To reduce the initial lag make it work with 3 points\n      // by copying the first point to the beginning.\n      if (_lastPoints.length === 3) {\n        _lastPoints.unshift(_lastPoints[0]);\n      }\n\n      // _points array will always have 4 points here.\n      const widths = this._calculateCurveWidths(_lastPoints[1], _lastPoints[2]);\n      const curve = Bezier.fromPoints(_lastPoints, widths);\n\n      // Remove the first element from the list, so that there are no more than 4 points at any time.\n      _lastPoints.shift();\n\n      return curve;\n    }\n\n    return null;\n  }\n\n  private _calculateCurveWidths(\n    startPoint: Point,\n    endPoint: Point,\n  ): { start: number; end: number } {\n    const velocity =\n      this.velocityFilterWeight * endPoint.velocityFrom(startPoint) +\n      (1 - this.velocityFilterWeight) * this._lastVelocity;\n\n    const newWidth = this._strokeWidth(velocity);\n\n    const widths = {\n      end: newWidth,\n      start: this._lastWidth,\n    };\n\n    this._lastVelocity = velocity;\n    this._lastWidth = newWidth;\n\n    return widths;\n  }\n\n  private _strokeWidth(velocity: number): number {\n    return Math.max(this.maxWidth / (velocity + 1), this.minWidth);\n  }\n\n  private _drawCurveSegment(x: number, y: number, width: number): void {\n    const ctx = this._ctx;\n\n    ctx.moveTo(x, y);\n    ctx.arc(x, y, width, 0, 2 * Math.PI, false);\n    this._isEmpty = false;\n  }\n\n  private _drawCurve({ color, curve }: { color: string; curve: Bezier }): void {\n    const ctx = this._ctx;\n    const widthDelta = curve.endWidth - curve.startWidth;\n    // '2' is just an arbitrary number here. If only lenght is used, then\n    // there are gaps between curve segments :/\n    const drawSteps = Math.floor(curve.length()) * 2;\n\n    ctx.beginPath();\n    ctx.fillStyle = color;\n\n    for (let i = 0; i < drawSteps; i += 1) {\n      // Calculate the Bezier (x, y) coordinate for this step.\n      const t = i / drawSteps;\n      const tt = t * t;\n      const ttt = tt * t;\n      const u = 1 - t;\n      const uu = u * u;\n      const uuu = uu * u;\n\n      let x = uuu * curve.startPoint.x;\n      x += 3 * uu * t * curve.control1.x;\n      x += 3 * u * tt * curve.control2.x;\n      x += ttt * curve.endPoint.x;\n\n      let y = uuu * curve.startPoint.y;\n      y += 3 * uu * t * curve.control1.y;\n      y += 3 * u * tt * curve.control2.y;\n      y += ttt * curve.endPoint.y;\n\n      const width = Math.min(\n        curve.startWidth + ttt * widthDelta,\n        this.maxWidth,\n      );\n      this._drawCurveSegment(x, y, width);\n    }\n\n    ctx.closePath();\n    ctx.fill();\n  }\n\n  private _drawDot({\n    color,\n    point,\n  }: {\n    color: string;\n    point: BasicPoint;\n  }): void {\n    const ctx = this._ctx;\n    const width =\n      typeof this.dotSize === 'function' ? this.dotSize() : this.dotSize;\n\n    ctx.beginPath();\n    this._drawCurveSegment(point.x, point.y, width);\n    ctx.closePath();\n    ctx.fillStyle = color;\n    ctx.fill();\n  }\n\n  private _fromData(\n    pointGroups: PointGroup[],\n    drawCurve: SignaturePad['_drawCurve'],\n    drawDot: SignaturePad['_drawDot'],\n  ): void {\n    for (const group of pointGroups) {\n      const { color, points } = group;\n\n      if (points.length > 1) {\n        for (let j = 0; j < points.length; j += 1) {\n          const basicPoint = points[j];\n          const point = new Point(basicPoint.x, basicPoint.y, basicPoint.time);\n\n          // All points in the group have the same color, so it's enough to set\n          // penColor just at the beginning.\n          this.penColor = color;\n\n          if (j === 0) {\n            this._reset();\n          }\n\n          const curve = this._addPoint(point);\n\n          if (curve) {\n            drawCurve({ color, curve });\n          }\n        }\n      } else {\n        this._reset();\n\n        drawDot({\n          color,\n          point: points[0],\n        });\n      }\n    }\n  }\n\n  private _toSVG(): string {\n    const pointGroups = this._data;\n    const ratio = Math.max(window.devicePixelRatio || 1, 1);\n    const minX = 0;\n    const minY = 0;\n    const maxX = this.canvas.width / ratio;\n    const maxY = this.canvas.height / ratio;\n    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n\n    svg.setAttribute('width', this.canvas.width.toString());\n    svg.setAttribute('height', this.canvas.height.toString());\n\n    this._fromData(\n      pointGroups,\n\n      ({ color, curve }: { color: string; curve: Bezier }) => {\n        const path = document.createElement('path');\n\n        // Need to check curve for NaN values, these pop up when drawing\n        // lines on the canvas that are not continuous. E.g. Sharp corners\n        // or stopping mid-stroke and than continuing without lifting mouse.\n        /* eslint-disable no-restricted-globals */\n        if (\n          !isNaN(curve.control1.x) &&\n          !isNaN(curve.control1.y) &&\n          !isNaN(curve.control2.x) &&\n          !isNaN(curve.control2.y)\n        ) {\n          const attr =\n            `M ${curve.startPoint.x.toFixed(3)},${curve.startPoint.y.toFixed(\n              3,\n            )} ` +\n            `C ${curve.control1.x.toFixed(3)},${curve.control1.y.toFixed(3)} ` +\n            `${curve.control2.x.toFixed(3)},${curve.control2.y.toFixed(3)} ` +\n            `${curve.endPoint.x.toFixed(3)},${curve.endPoint.y.toFixed(3)}`;\n          path.setAttribute('d', attr);\n          path.setAttribute('stroke-width', (curve.endWidth * 2.25).toFixed(3));\n          path.setAttribute('stroke', color);\n          path.setAttribute('fill', 'none');\n          path.setAttribute('stroke-linecap', 'round');\n\n          svg.appendChild(path);\n        }\n        /* eslint-enable no-restricted-globals */\n      },\n\n      ({ color, point }: { color: string; point: BasicPoint }) => {\n        const circle = document.createElement('circle');\n        const dotSize =\n          typeof this.dotSize === 'function' ? this.dotSize() : this.dotSize;\n        circle.setAttribute('r', dotSize.toString());\n        circle.setAttribute('cx', point.x.toString());\n        circle.setAttribute('cy', point.y.toString());\n        circle.setAttribute('fill', color);\n\n        svg.appendChild(circle);\n      },\n    );\n\n    const prefix = 'data:image/svg+xml;base64,';\n    const header =\n      '<svg' +\n      ' xmlns=\"http://www.w3.org/2000/svg\"' +\n      ' xmlns:xlink=\"http://www.w3.org/1999/xlink\"' +\n      ` viewBox=\"${minX} ${minY} ${maxX} ${maxY}\"` +\n      ` width=\"${maxX}\"` +\n      ` height=\"${maxY}\"` +\n      '>';\n    let body = svg.innerHTML;\n\n    // IE hack for missing innerHTML property on SVGElement\n    if (body === undefined) {\n      const dummy = document.createElement('dummy');\n      const nodes = svg.childNodes;\n      dummy.innerHTML = '';\n\n      // tslint:disable-next-line: prefer-for-of\n      for (let i = 0; i < nodes.length; i += 1) {\n        dummy.appendChild(nodes[i].cloneNode(true));\n      }\n\n      body = dummy.innerHTML;\n    }\n\n    const footer = '</svg>';\n    const data = header + body + footer;\n\n    return prefix + btoa(data);\n  }\n}\n", "// Defaults\nconst defaultOptions = {\n\tformat: 'image/png',\n\tquality: 0.92,\n\twidth: undefined,\n\theight: undefined,\n\tCanvas: undefined,\n\tcrossOrigin: undefined\n};\n\n// Return Promise\nconst mergeImages = (sources = [], options = {}) => new Promise(resolve => {\n\toptions = Object.assign({}, defaultOptions, options);\n\n\t// Setup browser/Node.js specific variables\n\tconst canvas = options.Canvas ? new options.Canvas() : window.document.createElement('canvas');\n\tconst Image = options.Canvas ? options.Canvas.Image : window.Image;\n\tif (options.Canvas) {\n\t\toptions.quality *= 100;\n\t}\n\n\t// Load sources\n\tconst images = sources.map(source => new Promise((resolve, reject) => {\n\t\t// Convert sources to objects\n\t\tif (source.constructor.name !== 'Object') {\n\t\t\tsource = { src: source };\n\t\t}\n\n\t\t// Resolve source and img when loaded\n\t\tconst img = new Image();\n\t\timg.crossOrigin = options.crossOrigin;\n\t\timg.onerror = () => reject(new Error('Couldn\\'t load image'));\n\t\timg.onload = () => resolve(Object.assign({}, source, { img }));\n\t\timg.src = source.src;\n\t}));\n\n\t// Get canvas context\n\tconst ctx = canvas.getContext('2d');\n\n\t// When sources have loaded\n\tresolve(Promise.all(images)\n\t\t.then(images => {\n\t\t\t// Set canvas dimensions\n\t\t\tconst getSize = dim => options[dim] || Math.max(...images.map(image => image.img[dim]));\n\t\t\tcanvas.width = getSize('width');\n\t\t\tcanvas.height = getSize('height');\n\n\t\t\t// Draw images to canvas\n\t\t\timages.forEach(image => {\n\t\t\t\tctx.globalAlpha = image.opacity ? image.opacity : 1;\n\t\t\t\treturn ctx.drawImage(image.img, image.x || 0, image.y || 0);\n\t\t\t});\n\n\t\t\tif (options.Canvas && options.format === 'image/jpeg') {\n\t\t\t\t// Resolve data URI for node-canvas jpeg async\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tcanvas.toDataURL(options.format, {\n\t\t\t\t\t\tquality: options.quality,\n\t\t\t\t\t\tprogressive: false\n\t\t\t\t\t}, (err, jpeg) => {\n\t\t\t\t\t\tif (err) {\n\t\t\t\t\t\t\tthrow err;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresolve(jpeg);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\n\t\t\t// Resolve all other data URIs sync\n\t\t\treturn canvas.toDataURL(options.format, options.quality);\n\t\t}));\n});\n\nexport default mergeImages;\n", "import { defineComponent, h } from 'vue';\nimport SignaturePad from 'signature_pad';\nimport mergeImages from 'merge-images';\n\nconst IMAGE_TYPES = ['image/png', 'image/jpeg', 'image/svg+xml'];\nconst checkSaveType = type => IMAGE_TYPES.includes(type);\nconst DEFAULT_OPTIONS = {\n  dotSize: (0.5 + 2.5) / 2,\n  minWidth: 0.5,\n  maxWidth: 2.5,\n  throttle: 16,\n  minDistance: 5,\n  backgroundColor: 'rgba(0,0,0,0)',\n  penColor: 'black',\n  velocityFilterWeight: 0.7,\n  onBegin: () => {},\n  onEnd: () => {}\n};\nconst convert2NonReactive = observerValue => JSON.parse(JSON.stringify(observerValue));\nconst TRANSPARENT_PNG = {\n  src: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=',\n  x: 0,\n  y: 0\n};\n\nvar script = defineComponent({\n  name: 'VueSignaturePad',\n  props: {\n    width: {\n      type: String,\n      default: '100%'\n    },\n    height: {\n      type: String,\n      default: '100%'\n    },\n    customStyle: {\n      type: Object,\n      default: () => ({})\n    },\n    options: {\n      type: Object,\n      default: () => ({})\n    },\n    images: {\n      type: Array,\n      default: () => []\n    },\n    scaleToDevicePixelRatio: {\n      type: Boolean,\n      default: () => true\n    }\n  },\n  data: () => ({\n    signaturePad: {},\n    cacheImages: [],\n    signatureData: TRANSPARENT_PNG,\n    onResizeHandler: null\n  }),\n  computed: {\n    propsImagesAndCustomImages() {\n      const nonReactiveProrpImages = convert2NonReactive(this.images);\n      const nonReactiveCachImages = convert2NonReactive(this.cacheImages);\n      return [...nonReactiveProrpImages, ...nonReactiveCachImages];\n    }\n\n  },\n  watch: {\n    options: function (nextOptions) {\n      Object.keys(nextOptions).forEach(option => {\n        if (this.signaturePad[option]) {\n          this.signaturePad[option] = nextOptions[option];\n        }\n      });\n    }\n  },\n\n  mounted() {\n    const {\n      options\n    } = this;\n    const canvas = this.$refs.signaturePadCanvas;\n    const signaturePad = new SignaturePad(canvas, { ...DEFAULT_OPTIONS,\n      ...options\n    });\n    this.signaturePad = signaturePad;\n\n    if (options.resizeHandler) {\n      this.resizeCanvas = options.resizeHandler.bind(this);\n    }\n\n    this.onResizeHandler = this.resizeCanvas.bind(this);\n    window.addEventListener('resize', this.onResizeHandler, false);\n    this.resizeCanvas();\n  },\n\n  beforeUnmount() {\n    if (this.onResizeHandler) {\n      window.removeEventListener('resize', this.onResizeHandler, false);\n    }\n  },\n\n  methods: {\n    resizeCanvas() {\n      const canvas = this.$refs.signaturePadCanvas;\n      const data = this.signaturePad.toData();\n      const ratio = this.scaleToDevicePixelRatio ? Math.max(window.devicePixelRatio || 1, 1) : 1;\n      canvas.width = canvas.offsetWidth * ratio;\n      canvas.height = canvas.offsetHeight * ratio;\n      canvas.getContext('2d').scale(ratio, ratio);\n      this.signaturePad.clear();\n      this.signatureData = TRANSPARENT_PNG;\n      this.signaturePad.fromData(data);\n    },\n\n    saveSignature() {\n      let type = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : IMAGE_TYPES[0];\n      let encoderOptions = arguments.length > 1 ? arguments[1] : undefined;\n      const {\n        signaturePad\n      } = this;\n      const status = {\n        isEmpty: false,\n        data: undefined\n      };\n\n      if (!checkSaveType(type)) {\n        const imageTypesString = IMAGE_TYPES.join(', ');\n        throw new Error(`The Image type is incorrect! We are support ${imageTypesString} types.`);\n      }\n\n      if (signaturePad.isEmpty()) {\n        return { ...status,\n          isEmpty: true\n        };\n      } else {\n        this.signatureData = signaturePad.toDataURL(type, encoderOptions);\n        return { ...status,\n          data: this.signatureData\n        };\n      }\n    },\n\n    undoSignature() {\n      const {\n        signaturePad\n      } = this;\n      const record = signaturePad.toData();\n\n      if (record) {\n        return signaturePad.fromData(record.slice(0, -1));\n      }\n    },\n\n    mergeImageAndSignature(customSignature) {\n      this.signatureData = customSignature;\n      return mergeImages([...this.images, ...this.cacheImages, this.signatureData]);\n    },\n\n    addImages() {\n      let images = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n      this.cacheImages = [...this.cacheImages, ...images];\n      return mergeImages([...this.images, ...this.cacheImages, this.signatureData]);\n    },\n\n    fromDataURL(data) {\n      let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      let callback = arguments.length > 2 ? arguments[2] : undefined;\n      return this.signaturePad.fromDataURL(data, options, callback);\n    },\n\n    fromData(data) {\n      return this.signaturePad.fromData(data);\n    },\n\n    toData() {\n      return this.signaturePad.toData();\n    },\n\n    lockSignaturePad() {\n      return this.signaturePad.off();\n    },\n\n    openSignaturePad() {\n      return this.signaturePad.on();\n    },\n\n    isEmpty() {\n      return this.signaturePad.isEmpty();\n    },\n\n    getPropImagesAndCacheImages() {\n      return this.propsImagesAndCustomImages;\n    },\n\n    clearCacheImages() {\n      this.cacheImages = [];\n      return this.cacheImages;\n    },\n\n    clearSignature() {\n      return this.signaturePad.clear();\n    }\n\n  },\n\n  render() {\n    const {\n      width,\n      height,\n      customStyle\n    } = this;\n    return h('div', {\n      style: {\n        width,\n        height,\n        ...customStyle\n      }\n    }, [h('canvas', {\n      style: {\n        width: width,\n        height: height\n      },\n      ref: 'signaturePadCanvas'\n    })]);\n  }\n\n});\n\nscript.__file = \"src/components/VueSignaturePad.vue\";\n\nvar components = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  VueSignaturePad: script\n});\n\nconst install = function installVSignature(app) {\n  Object.entries(components).forEach(_ref => {\n    let [componentName, component] = _ref;\n    app.component(componentName, component);\n  });\n};\n\nexport { script as VueSignaturePad, install as default };\n"], "mappings": ";;;;;;;;IAOa,cAAK;EAGhB,YAAmB,GAAkB,GAAW,MAAa;AAA1C,SAAA,IAAA;AAAkB,SAAA,IAAA;AACnC,SAAK,OAAO,QAAQ,KAAK,IAAG;;EAGvB,WAAW,OAAiB;AACjC,WAAO,KAAK,KACV,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC,CAAC;;EAI1D,OAAO,OAAiB;AAC7B,WAAO,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,SAAS,MAAM;;EAGlE,aAAa,OAAiB;AACnC,WAAO,KAAK,SAAS,MAAM,OACvB,KAAK,WAAW,KAAK,KAAK,KAAK,OAAO,MAAM,QAC5C;;;ICzBK,eAAA,QAAM;EA6CjB,YACS,YACA,UACA,UACA,UACA,YACA,UAAgB;AALhB,SAAA,aAAA;AACA,SAAA,WAAA;AACA,SAAA,WAAA;AACA,SAAA,WAAA;AACA,SAAA,aAAA;AACA,SAAA,WAAA;;EAlDF,OAAO,WACZ,QACA,QAAsC;AAEtC,UAAM,KAAK,KAAK,uBAAuB,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE;AACxE,UAAM,KAAK,KAAK,uBAAuB,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC,EAAE;AAExE,WAAO,IAAI,QAAO,OAAO,CAAC,GAAG,IAAI,IAAI,OAAO,CAAC,GAAG,OAAO,OAAO,OAAO,GAAG;;EAGlE,OAAO,uBACb,IACA,IACA,IAAc;AAKd,UAAM,MAAM,GAAG,IAAI,GAAG;AACtB,UAAM,MAAM,GAAG,IAAI,GAAG;AACtB,UAAM,MAAM,GAAG,IAAI,GAAG;AACtB,UAAM,MAAM,GAAG,IAAI,GAAG;AAEtB,UAAM,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,KAAK,GAAK,IAAI,GAAG,IAAI,GAAG,KAAK,EAAG;AAC3D,UAAM,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,KAAK,GAAK,IAAI,GAAG,IAAI,GAAG,KAAK,EAAG;AAE3D,UAAM,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAC1C,UAAM,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG;AAE1C,UAAM,MAAM,GAAG,IAAI,GAAG;AACtB,UAAM,MAAM,GAAG,IAAI,GAAG;AAEtB,UAAM,IAAI,MAAM,KAAK;AACrB,UAAM,KAAK,EAAE,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,GAAG,IAAI,MAAM,EAAC;AAEjD,UAAM,KAAK,GAAG,IAAI,GAAG;AACrB,UAAM,KAAK,GAAG,IAAI,GAAG;AAErB,WAAO;MACL,IAAI,IAAI,MAAM,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;MAClC,IAAI,IAAI,MAAM,GAAG,IAAI,IAAI,GAAG,IAAI,EAAE;;;EAc/B,SAAM;AACX,UAAM,QAAQ;AACd,QAAI,SAAS;AACb,QAAI;AACJ,QAAI;AAEJ,aAAS,IAAI,GAAG,KAAK,OAAO,KAAK,GAAG;AAClC,YAAM,IAAI,IAAI;AACd,YAAM,KAAK,KAAK,MACd,GACA,KAAK,WAAW,GAChB,KAAK,SAAS,GACd,KAAK,SAAS,GACd,KAAK,SAAS,CAAC;AAEjB,YAAM,KAAK,KAAK,MACd,GACA,KAAK,WAAW,GAChB,KAAK,SAAS,GACd,KAAK,SAAS,GACd,KAAK,SAAS,CAAC;AAGjB,UAAI,IAAI,GAAG;AACT,cAAM,QAAQ,KAAM;AACpB,cAAM,QAAQ,KAAM;AAEpB,kBAAU,KAAK,KAAK,QAAQ,QAAQ,QAAQ,KAAK;;AAGnD,WAAK;AACL,WAAK;;AAGP,WAAO;;EAID,MACN,GACA,OACA,IACA,IACA,KAAW;AAGX,WAAe,SAAS,IAAM,MAAM,IAAM,MAAO,IAAM,KAC/C,IAAO,MAAS,IAAM,MAAM,IAAM,KAAM,IACxC,IAAO,MAAS,IAAM,KAAK,IAAa,IACjC,MAAQ,IAAY,IAAa;;;SCvGpC,SACd,IACA,OAAO,KAAG;AAEV,MAAI,WAAW;AACf,MAAI,UAAyB;AAC7B,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,QAAM,QAAQ,MAAA;AACZ,eAAW,KAAK,IAAG;AACnB,cAAU;AACV,aAAS,GAAG,MAAM,eAAe,UAAU;AAE3C,QAAI,CAAC,SAAS;AACZ,sBAAgB;AAChB,mBAAa,CAAA;;;AAIjB,SAAO,SAAS,WAAsB,MAAW;AAC/C,UAAM,MAAM,KAAK,IAAG;AACpB,UAAM,YAAY,QAAQ,MAAM;AAEhC,oBAAgB;AAChB,iBAAa;AAEb,QAAI,aAAa,KAAK,YAAY,MAAM;AACtC,UAAI,SAAS;AACX,qBAAa,OAAO;AACpB,kBAAU;;AAGZ,iBAAW;AACX,eAAS,GAAG,MAAM,eAAe,UAAU;AAE3C,UAAI,CAAC,SAAS;AACZ,wBAAgB;AAChB,qBAAa,CAAA;;eAEN,CAAC,SAAS;AACnB,gBAAU,OAAO,WAAW,OAAO,SAAS;;AAG9C,WAAO;;AAEX;ICXqB,qBAAA,cAAY;EAyB/B,YACU,QACA,UAAmB,CAAA,GAAE;AADrB,SAAA,SAAA;AACA,SAAA,UAAA;AAuIF,SAAA,mBAAmB,CAAC,UAAiB;AAC3C,UAAI,MAAM,UAAU,GAAG;AACrB,aAAK,mBAAmB;AACxB,aAAK,aAAa,KAAK;;;AAInB,SAAA,mBAAmB,CAAC,UAAiB;AAC3C,UAAI,KAAK,kBAAkB;AACzB,aAAK,kBAAkB,KAAK;;;AAIxB,SAAA,iBAAiB,CAAC,UAAiB;AACzC,UAAI,MAAM,UAAU,KAAK,KAAK,kBAAkB;AAC9C,aAAK,mBAAmB;AACxB,aAAK,WAAW,KAAK;;;AAIjB,SAAA,oBAAoB,CAAC,UAAiB;AAE5C,YAAM,eAAc;AAEpB,UAAI,MAAM,cAAc,WAAW,GAAG;AACpC,cAAM,QAAQ,MAAM,eAAe,CAAC;AACpC,aAAK,aAAa,KAAK;;;AAInB,SAAA,mBAAmB,CAAC,UAAiB;AAE3C,YAAM,eAAc;AAEpB,YAAM,QAAQ,MAAM,cAAc,CAAC;AACnC,WAAK,kBAAkB,KAAK;;AAGtB,SAAA,kBAAkB,CAAC,UAAiB;AAC1C,YAAM,mBAAmB,MAAM,WAAW,KAAK;AAC/C,UAAI,kBAAkB;AACpB,cAAM,eAAc;AAEpB,cAAM,QAAQ,MAAM,eAAe,CAAC;AACpC,aAAK,WAAW,KAAK;;;AAjLvB,SAAK,uBAAuB,QAAQ,wBAAwB;AAC5D,SAAK,WAAW,QAAQ,YAAY;AACpC,SAAK,WAAW,QAAQ,YAAY;AACpC,SAAK,WAAY,cAAc,UAAU,QAAQ,WAAW;AAC5D,SAAK,cAAe,iBAAiB,UACjC,QAAQ,cACR;AACJ,SAAK,UACH,QAAQ,WACR,SAAS,UAAO;AACd,cAAQ,KAAK,WAAW,KAAK,YAAY;;AAE7C,SAAK,WAAW,QAAQ,YAAY;AACpC,SAAK,kBAAkB,QAAQ,mBAAmB;AAClD,SAAK,UAAU,QAAQ;AACvB,SAAK,QAAQ,QAAQ;AAErB,SAAK,oBAAoB,KAAK,WAC1B,SAAS,cAAa,UAAU,eAAe,KAAK,QAAQ,IAC5D,cAAa,UAAU;AAC3B,SAAK,OAAO,OAAO,WAAW,IAAI;AAElC,SAAK,MAAK;AAGV,SAAK,GAAE;;EAGF,QAAK;AACV,UAAM,EAAE,MAAM,KAAK,OAAM,IAAK;AAG9B,QAAI,YAAY,KAAK;AACrB,QAAI,UAAU,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAC/C,QAAI,SAAS,GAAG,GAAG,OAAO,OAAO,OAAO,MAAM;AAE9C,SAAK,QAAQ,CAAA;AACb,SAAK,OAAM;AACX,SAAK,WAAW;;EAGX,YACL,SACA,UAA+D,CAAA,GAC/D,UAA2C;AAE3C,UAAM,QAAQ,IAAI,MAAK;AACvB,UAAM,QAAQ,QAAQ,SAAS,OAAO,oBAAoB;AAC1D,UAAM,QAAQ,QAAQ,SAAS,KAAK,OAAO,QAAQ;AACnD,UAAM,SAAS,QAAQ,UAAU,KAAK,OAAO,SAAS;AAEtD,SAAK,OAAM;AAEX,UAAM,SAAS,MAAA;AACb,WAAK,KAAK,UAAU,OAAO,GAAG,GAAG,OAAO,MAAM;AAC9C,UAAI,UAAU;AACZ,iBAAQ;;;AAGZ,UAAM,UAAU,CAAC,UAAK;AACpB,UAAI,UAAU;AACZ,iBAAS,KAAK;;;AAGlB,UAAM,MAAM;AAEZ,SAAK,WAAW;;EAGX,UAAU,OAAO,aAAa,gBAAuB;AAC1D,YAAQ,MAAI;MACV,KAAK;AACH,eAAO,KAAK,OAAM;MACpB;AACE,eAAO,KAAK,OAAO,UAAU,MAAM,cAAc;;;EAIhD,KAAE;AAEP,SAAK,OAAO,MAAM,cAAc;AAChC,SAAK,OAAO,MAAM,gBAAgB;AAElC,QAAI,OAAO,cAAc;AACvB,WAAK,qBAAoB;WACpB;AACL,WAAK,mBAAkB;AAEvB,UAAI,kBAAkB,QAAQ;AAC5B,aAAK,mBAAkB;;;;EAKtB,MAAG;AAER,SAAK,OAAO,MAAM,cAAc;AAChC,SAAK,OAAO,MAAM,gBAAgB;AAElC,SAAK,OAAO,oBAAoB,eAAe,KAAK,gBAAgB;AACpE,SAAK,OAAO,oBAAoB,eAAe,KAAK,gBAAgB;AACpE,aAAS,oBAAoB,aAAa,KAAK,cAAc;AAE7D,SAAK,OAAO,oBAAoB,aAAa,KAAK,gBAAgB;AAClE,SAAK,OAAO,oBAAoB,aAAa,KAAK,gBAAgB;AAClE,aAAS,oBAAoB,WAAW,KAAK,cAAc;AAE3D,SAAK,OAAO,oBAAoB,cAAc,KAAK,iBAAiB;AACpE,SAAK,OAAO,oBAAoB,aAAa,KAAK,gBAAgB;AAClE,SAAK,OAAO,oBAAoB,YAAY,KAAK,eAAe;;EAG3D,UAAO;AACZ,WAAO,KAAK;;EAGP,SAAS,aAAyB;AACvC,SAAK,MAAK;AAEV,SAAK,UACH,aACA,CAAC,EAAE,OAAO,MAAK,MAAO,KAAK,WAAW,EAAE,OAAO,MAAK,CAAE,GACtD,CAAC,EAAE,OAAO,MAAK,MAAO,KAAK,SAAS,EAAE,OAAO,MAAK,CAAE,CAAC;AAGvD,SAAK,QAAQ;;EAGR,SAAM;AACX,WAAO,KAAK;;EAqDN,aAAa,OAAyB;AAC5C,UAAM,gBAAgB;MACpB,OAAO,KAAK;MACZ,QAAQ,CAAA;;AAGV,QAAI,OAAO,KAAK,YAAY,YAAY;AACtC,WAAK,QAAQ,KAAK;;AAGpB,SAAK,MAAM,KAAK,aAAa;AAC7B,SAAK,OAAM;AACX,SAAK,cAAc,KAAK;;EAGlB,cAAc,OAAyB;AAC7C,QAAI,KAAK,MAAM,WAAW,GAAG;AAG3B,WAAK,aAAa,KAAK;AACvB;;AAGF,UAAM,IAAI,MAAM;AAChB,UAAM,IAAI,MAAM;AAEhB,UAAM,QAAQ,KAAK,aAAa,GAAG,CAAC;AACpC,UAAM,iBAAiB,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AACvD,UAAM,aAAa,eAAe;AAClC,UAAM,YACJ,WAAW,SAAS,KAAK,WAAW,WAAW,SAAS,CAAC;AAC3D,UAAM,sBAAsB,YACxB,MAAM,WAAW,SAAS,KAAK,KAAK,cACpC;AACJ,UAAM,QAAQ,eAAe;AAG7B,QAAI,CAAC,aAAa,EAAE,aAAa,sBAAsB;AACrD,YAAM,QAAQ,KAAK,UAAU,KAAK;AAElC,UAAI,CAAC,WAAW;AACd,aAAK,SAAS,EAAE,OAAO,MAAK,CAAE;iBACrB,OAAO;AAChB,aAAK,WAAW,EAAE,OAAO,MAAK,CAAE;;AAGlC,iBAAW,KAAK;QACd,MAAM,MAAM;QACZ,GAAG,MAAM;QACT,GAAG,MAAM;OACV;;;EAIG,WAAW,OAAyB;AAC1C,SAAK,cAAc,KAAK;AAExB,QAAI,OAAO,KAAK,UAAU,YAAY;AACpC,WAAK,MAAM,KAAK;;;EAIZ,uBAAoB;AAC1B,SAAK,mBAAmB;AAExB,SAAK,OAAO,iBAAiB,eAAe,KAAK,gBAAgB;AACjE,SAAK,OAAO,iBAAiB,eAAe,KAAK,gBAAgB;AACjE,aAAS,iBAAiB,aAAa,KAAK,cAAc;;EAGpD,qBAAkB;AACxB,SAAK,mBAAmB;AAExB,SAAK,OAAO,iBAAiB,aAAa,KAAK,gBAAgB;AAC/D,SAAK,OAAO,iBAAiB,aAAa,KAAK,gBAAgB;AAC/D,aAAS,iBAAiB,WAAW,KAAK,cAAc;;EAGlD,qBAAkB;AACxB,SAAK,OAAO,iBAAiB,cAAc,KAAK,iBAAiB;AACjE,SAAK,OAAO,iBAAiB,aAAa,KAAK,gBAAgB;AAC/D,SAAK,OAAO,iBAAiB,YAAY,KAAK,eAAe;;EAIvD,SAAM;AACZ,SAAK,cAAc,CAAA;AACnB,SAAK,gBAAgB;AACrB,SAAK,cAAc,KAAK,WAAW,KAAK,YAAY;AACpD,SAAK,KAAK,YAAY,KAAK;;EAGrB,aAAa,GAAW,GAAS;AACvC,UAAM,OAAO,KAAK,OAAO,sBAAqB;AAE9C,WAAO,IAAI,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,MAAK,oBAAI,KAAI,GAAG,QAAO,CAAE;;EAI5D,UAAU,OAAY;AAC5B,UAAM,EAAE,YAAW,IAAK;AAExB,gBAAY,KAAK,KAAK;AAEtB,QAAI,YAAY,SAAS,GAAG;AAG1B,UAAI,YAAY,WAAW,GAAG;AAC5B,oBAAY,QAAQ,YAAY,CAAC,CAAC;;AAIpC,YAAM,SAAS,KAAK,sBAAsB,YAAY,CAAC,GAAG,YAAY,CAAC,CAAC;AACxE,YAAM,QAAQ,OAAO,WAAW,aAAa,MAAM;AAGnD,kBAAY,MAAK;AAEjB,aAAO;;AAGT,WAAO;;EAGD,sBACN,YACA,UAAe;AAEf,UAAM,WACJ,KAAK,uBAAuB,SAAS,aAAa,UAAU,KAC3D,IAAI,KAAK,wBAAwB,KAAK;AAEzC,UAAM,WAAW,KAAK,aAAa,QAAQ;AAE3C,UAAM,SAAS;MACb,KAAK;MACL,OAAO,KAAK;;AAGd,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAElB,WAAO;;EAGD,aAAa,UAAgB;AACnC,WAAO,KAAK,IAAI,KAAK,YAAY,WAAW,IAAI,KAAK,QAAQ;;EAGvD,kBAAkB,GAAW,GAAW,OAAa;AAC3D,UAAM,MAAM,KAAK;AAEjB,QAAI,OAAO,GAAG,CAAC;AACf,QAAI,IAAI,GAAG,GAAG,OAAO,GAAG,IAAI,KAAK,IAAI,KAAK;AAC1C,SAAK,WAAW;;EAGV,WAAW,EAAE,OAAO,MAAK,GAAoC;AACnE,UAAM,MAAM,KAAK;AACjB,UAAM,aAAa,MAAM,WAAW,MAAM;AAG1C,UAAM,YAAY,KAAK,MAAM,MAAM,OAAM,CAAE,IAAI;AAE/C,QAAI,UAAS;AACb,QAAI,YAAY;AAEhB,aAAS,IAAI,GAAG,IAAI,WAAW,KAAK,GAAG;AAErC,YAAM,IAAI,IAAI;AACd,YAAM,KAAK,IAAI;AACf,YAAM,MAAM,KAAK;AACjB,YAAM,IAAI,IAAI;AACd,YAAM,KAAK,IAAI;AACf,YAAM,MAAM,KAAK;AAEjB,UAAI,IAAI,MAAM,MAAM,WAAW;AAC/B,WAAK,IAAI,KAAK,IAAI,MAAM,SAAS;AACjC,WAAK,IAAI,IAAI,KAAK,MAAM,SAAS;AACjC,WAAK,MAAM,MAAM,SAAS;AAE1B,UAAI,IAAI,MAAM,MAAM,WAAW;AAC/B,WAAK,IAAI,KAAK,IAAI,MAAM,SAAS;AACjC,WAAK,IAAI,IAAI,KAAK,MAAM,SAAS;AACjC,WAAK,MAAM,MAAM,SAAS;AAE1B,YAAM,QAAQ,KAAK,IACjB,MAAM,aAAa,MAAM,YACzB,KAAK,QAAQ;AAEf,WAAK,kBAAkB,GAAG,GAAG,KAAK;;AAGpC,QAAI,UAAS;AACb,QAAI,KAAI;;EAGF,SAAS,EACf,OACA,MAAK,GAIN;AACC,UAAM,MAAM,KAAK;AACjB,UAAM,QACJ,OAAO,KAAK,YAAY,aAAa,KAAK,QAAO,IAAK,KAAK;AAE7D,QAAI,UAAS;AACb,SAAK,kBAAkB,MAAM,GAAG,MAAM,GAAG,KAAK;AAC9C,QAAI,UAAS;AACb,QAAI,YAAY;AAChB,QAAI,KAAI;;EAGF,UACN,aACA,WACA,SAAiC;AAEjC,eAAW,SAAS,aAAa;AAC/B,YAAM,EAAE,OAAO,OAAM,IAAK;AAE1B,UAAI,OAAO,SAAS,GAAG;AACrB,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,gBAAM,aAAa,OAAO,CAAC;AAC3B,gBAAM,QAAQ,IAAI,MAAM,WAAW,GAAG,WAAW,GAAG,WAAW,IAAI;AAInE,eAAK,WAAW;AAEhB,cAAI,MAAM,GAAG;AACX,iBAAK,OAAM;;AAGb,gBAAM,QAAQ,KAAK,UAAU,KAAK;AAElC,cAAI,OAAO;AACT,sBAAU,EAAE,OAAO,MAAK,CAAE;;;aAGzB;AACL,aAAK,OAAM;AAEX,gBAAQ;UACN;UACA,OAAO,OAAO,CAAC;SAChB;;;;EAKC,SAAM;AACZ,UAAM,cAAc,KAAK;AACzB,UAAM,QAAQ,KAAK,IAAI,OAAO,oBAAoB,GAAG,CAAC;AACtD,UAAM,OAAO;AACb,UAAM,OAAO;AACb,UAAM,OAAO,KAAK,OAAO,QAAQ;AACjC,UAAM,OAAO,KAAK,OAAO,SAAS;AAClC,UAAM,MAAM,SAAS,gBAAgB,8BAA8B,KAAK;AAExE,QAAI,aAAa,SAAS,KAAK,OAAO,MAAM,SAAQ,CAAE;AACtD,QAAI,aAAa,UAAU,KAAK,OAAO,OAAO,SAAQ,CAAE;AAExD,SAAK,UACH,aAEA,CAAC,EAAE,OAAO,MAAK,MAAoC;AACjD,YAAM,OAAO,SAAS,cAAc,MAAM;AAM1C,UACE,CAAC,MAAM,MAAM,SAAS,CAAC,KACvB,CAAC,MAAM,MAAM,SAAS,CAAC,KACvB,CAAC,MAAM,MAAM,SAAS,CAAC,KACvB,CAAC,MAAM,MAAM,SAAS,CAAC,GACvB;AACA,cAAM,OACJ,KAAK,MAAM,WAAW,EAAE,QAAQ,CAAC,CAAC,IAAI,MAAM,WAAW,EAAE,QACvD,CAAC,CACF,MACI,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAI,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAC5D,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAI,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAC1D,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC,IAAI,MAAM,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC/D,aAAK,aAAa,KAAK,IAAI;AAC3B,aAAK,aAAa,iBAAiB,MAAM,WAAW,MAAM,QAAQ,CAAC,CAAC;AACpE,aAAK,aAAa,UAAU,KAAK;AACjC,aAAK,aAAa,QAAQ,MAAM;AAChC,aAAK,aAAa,kBAAkB,OAAO;AAE3C,YAAI,YAAY,IAAI;;OAKxB,CAAC,EAAE,OAAO,MAAK,MAAwC;AACrD,YAAM,SAAS,SAAS,cAAc,QAAQ;AAC9C,YAAM,UACJ,OAAO,KAAK,YAAY,aAAa,KAAK,QAAO,IAAK,KAAK;AAC7D,aAAO,aAAa,KAAK,QAAQ,SAAQ,CAAE;AAC3C,aAAO,aAAa,MAAM,MAAM,EAAE,SAAQ,CAAE;AAC5C,aAAO,aAAa,MAAM,MAAM,EAAE,SAAQ,CAAE;AAC5C,aAAO,aAAa,QAAQ,KAAK;AAEjC,UAAI,YAAY,MAAM;KACvB;AAGH,UAAM,SAAS;AACf,UAAM,SACJ,+FAGa,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,YAC9B,IAAI,aACH,IAAI;AAElB,QAAI,OAAO,IAAI;AAGf,QAAI,SAAS,QAAW;AACtB,YAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,YAAM,QAAQ,IAAI;AAClB,YAAM,YAAY;AAGlB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,cAAM,YAAY,MAAM,CAAC,EAAE,UAAU,IAAI,CAAC;;AAG5C,aAAO,MAAM;;AAGf,UAAM,SAAS;AACf,UAAM,OAAO,SAAS,OAAO;AAE7B,WAAO,SAAS,KAAK,IAAI;;;;;;AC7kB7BA,IAAM,iBAAiB;EACtB,QAAQ;EACR,SAAS;EACT,OAAO;EACP,QAAQ;EACR,QAAQ;EACR,aAAa;;AAIdA,IAAM,cAAW,SAAI,SAAc,SAAY;oCAAhB,CAAA;oCAAc,CAAA;SAAO,IAAI,QAAO,SAAC,SAAW;AAC1E,cAAU,OAAO,OAAO,CAAA,GAAI,gBAAgB,OAAO;AAGnDA,QAAM,SAAS,QAAQ,SAAS,IAAI,QAAQ,OAAM,IAAK,OAAO,SAAS,cAAc,QAAQ;AAC7FA,QAAMC,SAAQ,QAAQ,SAAS,QAAQ,OAAO,QAAQ,OAAO;AAC7D,QAAI,QAAQ,QAAQ;AACnB,cAAQ,WAAW;;AAIpBD,QAAM,SAAS,QAAQ,IAAG,SAAC,QAAA;AAAA,aAAU,IAAI,QAAO,SAAEE,UAAS,QAAW;AAErE,YAAI,OAAO,YAAY,SAAS,UAAU;AACzC,mBAAS,EAAE,KAAK,OAAM;;AAIvBF,YAAM,MAAM,IAAIC,OAAK;AACrB,YAAI,cAAc,QAAQ;AAC1B,YAAI,UAAO,WAAA;AAAA,iBAAS,OAAO,IAAI,MAAM,qBAAsB,CAAC;QAAA;AAC5D,YAAI,SAAM,WAAA;AAAA,iBAASC,SAAQ,OAAO,OAAO,CAAA,GAAI,QAAQ,EAAA,IAAK,CAAE,CAAC;QAAA;AAC7D,YAAI,MAAM,OAAO;OACjB;IAAA,CAAC;AAGFF,QAAM,MAAM,OAAO,WAAW,IAAI;AAGlC,YAAQ,QAAQ,IAAI,MAAM,EACxB,KAAI,SAACG,SAAU;AAEfH,UAAM,UAAO,SAAG,KAAA;AAAA,eAAO,QAAQ,GAAG,KAAK,KAAK,IAAA,MAAG,MAAIG,QAAO,IAAG,SAAC,OAAA;AAAA,iBAAS,MAAM,IAAI,GAAG;QAAA,CAAC,CAAC;MAAA;AACtF,aAAO,QAAQ,QAAQ,OAAO;AAC9B,aAAO,SAAS,QAAQ,QAAQ;AAGhC,MAAAA,QAAO,QAAO,SAAC,OAAS;AACvB,YAAI,cAAc,MAAM,UAAU,MAAM,UAAU;AAClD,eAAO,IAAI,UAAU,MAAM,KAAK,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC;OAC1D;AAED,UAAI,QAAQ,UAAU,QAAQ,WAAW,cAAc;AAEtD,eAAO,IAAI,QAAO,SAACD,UAAW;AAC7B,iBAAO,UAAU,QAAQ,QAAQ;YAChC,SAAS,QAAQ;YACjB,aAAa;aACb,SAAG,KAAK,MAAS;AACjB,gBAAI,KAAK;AACR,oBAAM;;AAEP,YAAAA,SAAQ,IAAI;WACZ;SACD;;AAIF,aAAO,OAAO,UAAU,QAAQ,QAAQ,QAAQ,OAAO;KACvD,CAAC;GACH;;;;;ACnED,IAAM,cAAc,CAAC,aAAa,cAAc,eAAe;AAC/D,IAAM,gBAAgB,UAAQ,YAAY,SAAS,IAAI;AACvD,IAAM,kBAAkB;AAAA,EACtB,UAAU,MAAM,OAAO;AAAA,EACvB,UAAU;AAAA,EACV,UAAU;AAAA,EACV,UAAU;AAAA,EACV,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,UAAU;AAAA,EACV,sBAAsB;AAAA,EACtB,SAAS,MAAM;AAAA,EAAC;AAAA,EAChB,OAAO,MAAM;AAAA,EAAC;AAChB;AACA,IAAM,sBAAsB,mBAAiB,KAAK,MAAM,KAAK,UAAU,aAAa,CAAC;AACrF,IAAM,kBAAkB;AAAA,EACtB,KAAK;AAAA,EACL,GAAG;AAAA,EACH,GAAG;AACL;AAEA,IAAI,SAAS,gBAAgB;AAAA,EAC3B,MAAM;AAAA,EACN,OAAO;AAAA,IACL,OAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,IACA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS,OAAO,CAAC;AAAA,IACnB;AAAA,IACA,SAAS;AAAA,MACP,MAAM;AAAA,MACN,SAAS,OAAO,CAAC;AAAA,IACnB;AAAA,IACA,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAM,CAAC;AAAA,IAClB;AAAA,IACA,yBAAyB;AAAA,MACvB,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,IACjB;AAAA,EACF;AAAA,EACA,MAAM,OAAO;AAAA,IACX,cAAc,CAAC;AAAA,IACf,aAAa,CAAC;AAAA,IACd,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB;AAAA,EACA,UAAU;AAAA,IACR,6BAA6B;AAC3B,YAAM,yBAAyB,oBAAoB,KAAK,MAAM;AAC9D,YAAM,wBAAwB,oBAAoB,KAAK,WAAW;AAClE,aAAO,CAAC,GAAG,wBAAwB,GAAG,qBAAqB;AAAA,IAC7D;AAAA,EAEF;AAAA,EACA,OAAO;AAAA,IACL,SAAS,SAAU,aAAa;AAC9B,aAAO,KAAK,WAAW,EAAE,QAAQ,YAAU;AACzC,YAAI,KAAK,aAAa,MAAM,GAAG;AAC7B,eAAK,aAAa,MAAM,IAAI,YAAY,MAAM;AAAA,QAChD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,UAAU;AACR,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,KAAK,MAAM;AAC1B,UAAM,eAAe,IAAI,sBAAa,QAAQ;AAAA,MAAE,GAAG;AAAA,MACjD,GAAG;AAAA,IACL,CAAC;AACD,SAAK,eAAe;AAEpB,QAAI,QAAQ,eAAe;AACzB,WAAK,eAAe,QAAQ,cAAc,KAAK,IAAI;AAAA,IACrD;AAEA,SAAK,kBAAkB,KAAK,aAAa,KAAK,IAAI;AAClD,WAAO,iBAAiB,UAAU,KAAK,iBAAiB,KAAK;AAC7D,SAAK,aAAa;AAAA,EACpB;AAAA,EAEA,gBAAgB;AACd,QAAI,KAAK,iBAAiB;AACxB,aAAO,oBAAoB,UAAU,KAAK,iBAAiB,KAAK;AAAA,IAClE;AAAA,EACF;AAAA,EAEA,SAAS;AAAA,IACP,eAAe;AACb,YAAM,SAAS,KAAK,MAAM;AAC1B,YAAM,OAAO,KAAK,aAAa,OAAO;AACtC,YAAM,QAAQ,KAAK,0BAA0B,KAAK,IAAI,OAAO,oBAAoB,GAAG,CAAC,IAAI;AACzF,aAAO,QAAQ,OAAO,cAAc;AACpC,aAAO,SAAS,OAAO,eAAe;AACtC,aAAO,WAAW,IAAI,EAAE,MAAM,OAAO,KAAK;AAC1C,WAAK,aAAa,MAAM;AACxB,WAAK,gBAAgB;AACrB,WAAK,aAAa,SAAS,IAAI;AAAA,IACjC;AAAA,IAEA,gBAAgB;AACd,UAAI,OAAO,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,YAAY,CAAC;AAC5F,UAAI,iBAAiB,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AAC3D,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM,SAAS;AAAA,QACb,SAAS;AAAA,QACT,MAAM;AAAA,MACR;AAEA,UAAI,CAAC,cAAc,IAAI,GAAG;AACxB,cAAM,mBAAmB,YAAY,KAAK,IAAI;AAC9C,cAAM,IAAI,MAAM,+CAA+C,gBAAgB,SAAS;AAAA,MAC1F;AAEA,UAAI,aAAa,QAAQ,GAAG;AAC1B,eAAO;AAAA,UAAE,GAAG;AAAA,UACV,SAAS;AAAA,QACX;AAAA,MACF,OAAO;AACL,aAAK,gBAAgB,aAAa,UAAU,MAAM,cAAc;AAChE,eAAO;AAAA,UAAE,GAAG;AAAA,UACV,MAAM,KAAK;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA,IAEA,gBAAgB;AACd,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM,SAAS,aAAa,OAAO;AAEnC,UAAI,QAAQ;AACV,eAAO,aAAa,SAAS,OAAO,MAAM,GAAG,EAAE,CAAC;AAAA,MAClD;AAAA,IACF;AAAA,IAEA,uBAAuB,iBAAiB;AACtC,WAAK,gBAAgB;AACrB,aAAO,qBAAY,CAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,aAAa,KAAK,aAAa,CAAC;AAAA,IAC9E;AAAA,IAEA,YAAY;AACV,UAAI,SAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAClF,WAAK,cAAc,CAAC,GAAG,KAAK,aAAa,GAAG,MAAM;AAClD,aAAO,qBAAY,CAAC,GAAG,KAAK,QAAQ,GAAG,KAAK,aAAa,KAAK,aAAa,CAAC;AAAA,IAC9E;AAAA,IAEA,YAAY,MAAM;AAChB,UAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACnF,UAAI,WAAW,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACrD,aAAO,KAAK,aAAa,YAAY,MAAM,SAAS,QAAQ;AAAA,IAC9D;AAAA,IAEA,SAAS,MAAM;AACb,aAAO,KAAK,aAAa,SAAS,IAAI;AAAA,IACxC;AAAA,IAEA,SAAS;AACP,aAAO,KAAK,aAAa,OAAO;AAAA,IAClC;AAAA,IAEA,mBAAmB;AACjB,aAAO,KAAK,aAAa,IAAI;AAAA,IAC/B;AAAA,IAEA,mBAAmB;AACjB,aAAO,KAAK,aAAa,GAAG;AAAA,IAC9B;AAAA,IAEA,UAAU;AACR,aAAO,KAAK,aAAa,QAAQ;AAAA,IACnC;AAAA,IAEA,8BAA8B;AAC5B,aAAO,KAAK;AAAA,IACd;AAAA,IAEA,mBAAmB;AACjB,WAAK,cAAc,CAAC;AACpB,aAAO,KAAK;AAAA,IACd;AAAA,IAEA,iBAAiB;AACf,aAAO,KAAK,aAAa,MAAM;AAAA,IACjC;AAAA,EAEF;AAAA,EAEA,SAAS;AACP,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,EAAE,OAAO;AAAA,MACd,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA,GAAG;AAAA,MACL;AAAA,IACF,GAAG,CAAC,EAAE,UAAU;AAAA,MACd,OAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,MACA,KAAK;AAAA,IACP,CAAC,CAAC,CAAC;AAAA,EACL;AAEF,CAAC;AAED,OAAO,SAAS;AAEhB,IAAI,aAA0B,OAAO,OAAO;AAAA,EAC1C,WAAW;AAAA,EACX,iBAAiB;AACnB,CAAC;AAED,IAAM,UAAU,SAAS,kBAAkB,KAAK;AAC9C,SAAO,QAAQ,UAAU,EAAE,QAAQ,UAAQ;AACzC,QAAI,CAAC,eAAe,SAAS,IAAI;AACjC,QAAI,UAAU,eAAe,SAAS;AAAA,EACxC,CAAC;AACH;", "names": ["const", "Image", "resolve", "images"]}