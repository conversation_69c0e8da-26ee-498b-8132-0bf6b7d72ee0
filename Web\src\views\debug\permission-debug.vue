<template>
  <div class="permission-debug-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>权限调试页面</span>
        </div>
      </template>
      
      <div class="debug-section">
        <h3>当前用户权限列表</h3>
        <el-tag v-for="permission in userPermissions" :key="permission" class="permission-tag">
          {{ permission }}
        </el-tag>
        <div v-if="userPermissions.length === 0" class="no-permissions">
          <el-alert title="警告" type="warning" description="当前用户没有任何权限，这可能是导致页面无法正常显示的原因。" show-icon />
        </div>
      </div>

      <div class="debug-section">
        <h3>设备组页面所需权限检查</h3>
        <div class="permission-check">
          <el-tag :type="hasPermission('mqttDeviceGroup:update') ? 'success' : 'danger'">
            mqttDeviceGroup:update {{ hasPermission('mqttDeviceGroup:update') ? '✓' : '✗' }}
          </el-tag>
          <el-tag :type="hasPermission('mqttDeviceGroup:delete') ? 'success' : 'danger'">
            mqttDeviceGroup:delete {{ hasPermission('mqttDeviceGroup:delete') ? '✓' : '✗' }}
          </el-tag>
          <el-tag :type="hasPermission('mqttDeviceGroup:add') ? 'success' : 'danger'">
            mqttDeviceGroup:add {{ hasPermission('mqttDeviceGroup:add') ? '✓' : '✗' }}
          </el-tag>
          <el-tag :type="hasPermission('mqttDeviceGroup:page') ? 'success' : 'danger'">
            mqttDeviceGroup:page {{ hasPermission('mqttDeviceGroup:page') ? '✓' : '✗' }}
          </el-tag>
        </div>
      </div>

      <div class="debug-section">
        <h3>MQTT客户端认证页面所需权限检查</h3>
        <div class="permission-check">
          <el-tag :type="hasPermission('mqttClientAuth:update') ? 'success' : 'danger'">
            mqttClientAuth:update {{ hasPermission('mqttClientAuth:update') ? '✓' : '✗' }}
          </el-tag>
          <el-tag :type="hasPermission('mqttClientAuth:delete') ? 'success' : 'danger'">
            mqttClientAuth:delete {{ hasPermission('mqttClientAuth:delete') ? '✓' : '✗' }}
          </el-tag>
          <el-tag :type="hasPermission('mqttClientAuth:add') ? 'success' : 'danger'">
            mqttClientAuth:add {{ hasPermission('mqttClientAuth:add') ? '✓' : '✗' }}
          </el-tag>
          <el-tag :type="hasPermission('mqttClientAuth:page') ? 'success' : 'danger'">
            mqttClientAuth:page {{ hasPermission('mqttClientAuth:page') ? '✓' : '✗' }}
          </el-tag>
        </div>
      </div>

      <div class="debug-section">
        <h3>用户信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ userInfo.id }}</el-descriptions-item>
          <el-descriptions-item label="账号">{{ userInfo.account }}</el-descriptions-item>
          <el-descriptions-item label="真实姓名">{{ userInfo.realName }}</el-descriptions-item>
          <el-descriptions-item label="角色">{{ userInfo.roles?.join(', ') }}</el-descriptions-item>
          <el-descriptions-item label="权限数量">{{ userPermissions.length }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="debug-section">
        <h3>解决方案</h3>
        <el-alert 
          title="权限问题解决方案" 
          type="info" 
          description="如果上述权限检查显示缺少权限，请联系系统管理员为您的账号分配相应的权限。需要的权限包括：mqttDeviceGroup:page, mqttDeviceGroup:update, mqttDeviceGroup:delete, mqttClientAuth:page, mqttClientAuth:update, mqttClientAuth:delete 等。" 
          show-icon 
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts" name="PermissionDebug">
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';
import { auth } from '/@/utils/authFunction';

const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

const userInfo = computed(() => userInfos.value);
const userPermissions = computed(() => userInfos.value.authBtnList || []);

const hasPermission = (permission: string) => {
  return auth(permission);
};
</script>

<style scoped>
.permission-debug-container {
  padding: 20px;
}

.debug-section {
  margin-bottom: 20px;
}

.debug-section h3 {
  margin-bottom: 10px;
  color: #409eff;
}

.permission-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.permission-check {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.no-permissions {
  margin-top: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>