# MQTT管理中台技术架构文档

## 1. 架构设计

```mermaid
graph TD
    A[用户浏览器] --> B[Vue3前端应用]
    B --> C[Axios HTTP客户端]
    C --> D[.NET后端API服务]
    B --> E[MQTT.js客户端]
    E --> F[MQTT Broker服务]
    D --> G[PostgreSQL数据库]
    D --> H[Redis缓存]
    D --> I[EMQX管理API]
    
    subgraph "前端层"
        B
        E
    end
    
    subgraph "后端层"
        D
        H
    end
    
    subgraph "数据层"
        G
    end
    
    subgraph "MQTT服务层"
        F
        I
    end
```

## 2. 技术描述

* **前端**: Vue3\@3.5.18 + Element Plus\@2.10.4 + TypeScript\@5.8.3 + Vite\@7.0.6

* **后端**: .NET Core + Admin.NET框架

* **数据库**: PostgreSQL (通过Admin.NET框架支持)

* **缓存**: Redis

* **MQTT客户端**: mqtt\@5.13.3

* **图表库**: <PERSON><PERSON>s\@5.6.0

* **状态管理**: Pinia\@3.0.3

* **HTTP客户端**: Axios\@1.11.0

## 3. 路由定义

| 路由                | 用途                   |
| ----------------- | -------------------- |
| /home             | 仪表板页面，显示系统概览和关键指标    |
| /mqtt-instance    | MQTT实例管理页面，实例配置和监控   |
| /mqtt-client      | MQTT客户端管理页面，设备连接管理   |
| /mqtt-topic       | MQTT主题管理页面，主题配置和权限控制 |
| /device-group     | 设备组管理页面，设备分组和批量操作    |
| /client-auth      | 客户端认证管理页面，认证配置和权限管理  |
| /realtime-monitor | 实时监控页面，连接和消息监控       |
| /mqtt-test        | MQTTX测试页面，在线消息测试工具   |
| /audit-log        | 日志审计页面，操作和连接日志查询     |
| /system-config    | 系统配置页面，平台参数和告警配置     |
| /login            | 登录页面，用户身份验证          |

## 4. API定义

### 4.1 核心API

#### MQTT实例管理API

```
GET /api/mqttInstance/page
```

请求参数:

| 参数名称         | 参数类型   | 是否必需  | 描述        |
| ------------ | ------ | ----- | --------- |
| page         | number | false | 页码，默认1    |
| pageSize     | number | false | 每页大小，默认20 |
| keyword      | string | false | 关键字搜索     |
| instanceType | string | false | 实例类型筛选    |

响应数据:

| 参数名称  | 参数类型            | 描述   |
| ----- | --------------- | ---- |
| total | number          | 总记录数 |
| items | MqttInstance\[] | 实例列表 |

#### EMQX实时操作API

```
POST /api/emqxRealtime/connect
```

请求参数:

| 参数名称       | 参数类型   | 是否必需 | 描述       |
| ---------- | ------ | ---- | -------- |
| instanceId | number | true | MQTT实例ID |

响应数据:

| 参数名称    | 参数类型    | 描述     |
| ------- | ------- | ------ |
| success | boolean | 连接是否成功 |
| message | string  | 响应消息   |

```
POST /api/emqxRealtime/publish
```

请求参数:

| 参数名称       | 参数类型    | 是否必需  | 描述         |
| ---------- | ------- | ----- | ---------- |
| instanceId | number  | true  | MQTT实例ID   |
| topic      | string  | true  | 发布主题       |
| payload    | string  | true  | 消息内容       |
| qos        | number  | false | QoS等级(0-2) |
| retain     | boolean | false | 是否保留消息     |

```
POST /api/emqxRealtime/subscribe
```

请求参数:

| 参数名称       | 参数类型   | 是否必需  | 描述         |
| ---------- | ------ | ----- | ---------- |
| instanceId | number | true  | MQTT实例ID   |
| topic      | string | true  | 订阅主题       |
| qos        | number | false | QoS等级(0-2) |

```
GET /api/emqxRealtime/getConnectionStatus
```

请求参数:

| 参数名称       | 参数类型   | 是否必需 | 描述       |
| ---------- | ------ | ---- | -------- |
| instanceId | number | true | MQTT实例ID |

响应数据:

| 参数名称        | 参数类型    | 描述    |
| ----------- | ------- | ----- |
| isConnected | boolean | 是否已连接 |
| status      | string  | 连接状态  |
| checkTime   | string  | 检查时间  |

#### 客户端管理API

```
GET /api/mqttClient/page
POST /api/mqttClient/add
PUT /api/mqttClient/update
DELETE /api/mqttClient/delete
```

#### 主题管理API

```
GET /api/mqttTopic/page
POST /api/mqttTopic/add
PUT /api/mqttTopic/update
DELETE /api/mqttTopic/delete
```

## 5. 服务器架构图

```mermaid
graph TD
    A[客户端/前端] --> B[控制器层 Controller]
    B --> C[服务层 Service]
    C --> D[仓储层 Repository]
    D --> E[(数据库)]
    C --> F[EMQX API客户端]
    F --> G[EMQX Broker]
    C --> H[缓存层 Redis]
    
    subgraph 服务器
        B
        C
        D
        F
        H
    end
```

## 6. 数据模型

### 6.1 数据模型定义

```mermaid
erDiagram
    MQTT_INSTANCE ||--o{ MQTT_CLIENT : contains
    MQTT_INSTANCE ||--o{ MQTT_TOPIC : manages
    MQTT_CLIENT ||--o{ MQTT_CLIENT_AUTH : authenticates
    MQTT_CLIENT }o--|| DEVICE_GROUP : belongs_to
    MQTT_TOPIC ||--o{ MQTT_SUBSCRIPTION : has
    
    MQTT_INSTANCE {
        bigint id PK
        varchar instance_name
        varchar instance_code
        varchar instance_type
        varchar server_host
        int server_port
        varchar api_host
        int api_port
        varchar api_username
        varchar api_password
        varchar status
        boolean enable_ssl
        int ssl_port
        boolean enable_websocket
        int ws_port
        int wss_port
        int max_connections
        int current_connections
        varchar aliyun_product_key
        varchar aliyun_region_id
        varchar aliyun_instance_id
        varchar device_id_prefix
        varchar group_id_prefix
        boolean aliyun_compatible
        boolean is_enabled
        text config_json
        datetime last_heartbeat
        bigint total_messages
        bigint total_bytes
        text remark
        datetime create_time
        datetime update_time
        bigint create_user_id
        bigint update_user_id
        boolean is_delete
        datetime last_activity
    }
    
    MQTT_CLIENT {
        bigint id PK
        bigint instance_id FK
        varchar client_id
        varchar device_name
        varchar group_id
        varchar product_key
        varchar device_secret
        varchar ip_address
        int port
        varchar status
        varchar protocol_version
        int keep_alive
        boolean clean_session
        datetime connected_at
        datetime disconnected_at
        datetime last_activity
        bigint messages_sent
        bigint messages_received
        bigint bytes_sent
        bigint bytes_received
        int subscription_count
        int max_subscriptions
        varchar device_type
        varchar device_version
        text device_tags
        text location_info
        boolean is_online
        boolean is_enabled
        varchar auth_mode
        varchar client_type
        text last_error
        text extended_properties
        text remark
        datetime create_time
        datetime update_time
        bigint create_user_id
        bigint update_user_id
        boolean is_delete
    }
    
    MQTT_TOPIC {
        bigint id PK
        bigint instance_id FK
        varchar topic_name
        varchar topic_type
        int qos_level
        boolean retain_message
        int subscription_count
        bigint message_count
        bigint byte_count
        datetime last_message_time
        text last_message_content
        int max_message_size
        int message_expiry
        boolean allow_publish
        boolean allow_subscribe
        text publish_permissions
        text subscribe_permissions
        boolean is_retained
        text description
        text topic_tags
        boolean is_enabled
        boolean is_system_topic
        text monitor_config
        text alert_rules
        text forward_rules
        text extended_properties
        text remark
        datetime create_time
        datetime update_time
        bigint create_user_id
        bigint update_user_id
        boolean is_delete
    }
    
    MQTT_CLIENT_AUTH {
        bigint id PK
        bigint instance_id FK
        varchar client_id
        varchar username
        varchar password_hash
        varchar auth_mode
        text permissions
        boolean is_enabled
        boolean is_locked
        datetime last_login_time
        varchar last_login_ip
        int login_attempts
        datetime lock_time
        text auth_data
        text remark
        datetime create_time
        datetime update_time
        bigint create_user_id
        bigint update_user_id
        boolean is_delete
    }
    
    DEVICE_GROUP {
        bigint id PK
        bigint instance_id FK
        varchar group_name
        varchar group_code
        varchar group_type
        bigint parent_id
        varchar group_path
        int group_level
        int sort_order
        text description
        text group_tags
        boolean is_enabled
        text extended_properties
        text remark
        datetime create_time
        datetime update_time
        bigint create_user_id
        bigint update_user_id
        boolean is_delete
    }
```

### 6.2 数据定义语言

#### MQTT实例表 (mqtt\_instance)

```sql
-- 创建表
CREATE TABLE mqtt_instance (
    id BIGSERIAL PRIMARY KEY,
    instance_name VARCHAR(100) NOT NULL,
    instance_code VARCHAR(50) UNIQUE NOT NULL,
    instance_type VARCHAR(20) NOT NULL,
    server_host VARCHAR(255) NOT NULL,
    server_port INTEGER NOT NULL,
    api_host VARCHAR(255),
    api_port INTEGER,
    api_username VARCHAR(100),
    api_password VARCHAR(255),
    status VARCHAR(20) DEFAULT 'OFFLINE',
    enable_ssl BOOLEAN DEFAULT FALSE,
    ssl_port INTEGER,
    enable_websocket BOOLEAN DEFAULT FALSE,
    ws_port INTEGER,
    wss_port INTEGER,
    max_connections INTEGER DEFAULT 1000,
    current_connections INTEGER DEFAULT 0,
    aliyun_product_key VARCHAR(100),
    aliyun_region_id VARCHAR(50),
    aliyun_instance_id VARCHAR(100),
    device_id_prefix VARCHAR(50),
    group_id_prefix VARCHAR(50),
    aliyun_compatible BOOLEAN DEFAULT FALSE,
    is_enabled BOOLEAN DEFAULT TRUE,
    config_json TEXT,
    last_heartbeat TIMESTAMP,
    total_messages BIGINT DEFAULT 0,
    total_bytes BIGINT DEFAULT 0,
    remark TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    update_user_id BIGINT,
    is_delete BOOLEAN DEFAULT FALSE,
    last_activity TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_mqtt_instance_type ON mqtt_instance(instance_type);
CREATE INDEX idx_mqtt_instance_status ON mqtt_instance(status);
CREATE INDEX idx_mqtt_instance_enabled ON mqtt_instance(is_enabled);
CREATE INDEX idx_mqtt_instance_create_time ON mqtt_instance(create_time DESC);

-- 初始化数据
INSERT INTO mqtt_instance (instance_name, instance_code, instance_type, server_host, server_port, api_host, api_port, api_username, api_password, remark)
VALUES 
('本地EMQX实例', 'LOCAL_EMQX', 'EMQX', 'localhost', 1883, 'localhost', 18083, 'admin', 'public', '本地开发测试实例'),
('生产EMQX集群', 'PROD_EMQX_CLUSTER', 'EMQX', 'emqx.example.com', 1883, 'emqx.example.com', 18083, 'admin', 'your_password', '生产环境EMQX集群');
```

#### MQTT客户端表 (mqtt\_client)

```sql
-- 创建表
CREATE TABLE mqtt_client (
    id BIGSERIAL PRIMARY KEY,
    instance_id BIGINT NOT NULL,
    client_id VARCHAR(255) NOT NULL,
    device_name VARCHAR(100),
    group_id VARCHAR(100),
    product_key VARCHAR(100),
    device_secret VARCHAR(255),
    ip_address VARCHAR(45),
    port INTEGER,
    status VARCHAR(20) DEFAULT 'OFFLINE',
    protocol_version VARCHAR(10) DEFAULT '3.1.1',
    keep_alive INTEGER DEFAULT 60,
    clean_session BOOLEAN DEFAULT TRUE,
    connected_at TIMESTAMP,
    disconnected_at TIMESTAMP,
    last_activity TIMESTAMP,
    messages_sent BIGINT DEFAULT 0,
    messages_received BIGINT DEFAULT 0,
    bytes_sent BIGINT DEFAULT 0,
    bytes_received BIGINT DEFAULT 0,
    subscription_count INTEGER DEFAULT 0,
    max_subscriptions INTEGER DEFAULT 100,
    device_type VARCHAR(50),
    device_version VARCHAR(50),
    device_tags TEXT,
    location_info TEXT,
    is_online BOOLEAN DEFAULT FALSE,
    is_enabled BOOLEAN DEFAULT TRUE,
    auth_mode VARCHAR(20) DEFAULT 'USERNAME',
    client_type VARCHAR(20) DEFAULT 'DEVICE',
    last_error TEXT,
    extended_properties TEXT,
    remark TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    update_user_id BIGINT,
    is_delete BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (instance_id) REFERENCES mqtt_instance(id)
);

-- 创建索引
CREATE INDEX idx_mqtt_client_instance_id ON mqtt_client(instance_id);
CREATE INDEX idx_mqtt_client_client_id ON mqtt_client(client_id);
CREATE INDEX idx_mqtt_client_status ON mqtt_client(status);
CREATE INDEX idx_mqtt_client_online ON mqtt_client(is_online);
CREATE INDEX idx_mqtt_client_last_activity ON mqtt_client(last_activity DESC);
```

#### MQTT主题表 (mqtt\_topic)

```sql
-- 创建表
CREATE TABLE mqtt_topic (
    id BIGSERIAL PRIMARY KEY,
    instance_id BIGINT NOT NULL,
    topic_name VARCHAR(255) NOT NULL,
    topic_type VARCHAR(20) DEFAULT 'NORMAL',
    qos_level INTEGER DEFAULT 0 CHECK (qos_level IN (0, 1, 2)),
    retain_message BOOLEAN DEFAULT FALSE,
    subscription_count INTEGER DEFAULT 0,
    message_count BIGINT DEFAULT 0,
    byte_count BIGINT DEFAULT 0,
    last_message_time TIMESTAMP,
    last_message_content TEXT,
    max_message_size INTEGER DEFAULT 1048576,
    message_expiry INTEGER DEFAULT 0,
    allow_publish BOOLEAN DEFAULT TRUE,
    allow_subscribe BOOLEAN DEFAULT TRUE,
    publish_permissions TEXT,
    subscribe_permissions TEXT,
    is_retained BOOLEAN DEFAULT FALSE,
    description TEXT,
    topic_tags TEXT,
    is_enabled BOOLEAN DEFAULT TRUE,
    is_system_topic BOOLEAN DEFAULT FALSE,
    monitor_config TEXT,
    alert_rules TEXT,
    forward_rules TEXT,
    extended_properties TEXT,
    remark TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_user_id BIGINT,
    update_user_id BIGINT,
    is_delete BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (instance_id) REFERENCES mqtt_instance(id)
);

-- 创建索引
CREATE INDEX idx_mqtt_topic_instance_id ON mqtt_topic(instance_id);
CREATE INDEX idx_mqtt_topic_name ON mqtt_topic(topic_name);
CREATE INDEX idx_mqtt_topic_type ON mqtt_topic(topic_type);
CREATE INDEX idx_mqtt_topic_enabled ON mqtt_topic(is_enabled);
CREATE INDEX idx_mqtt_topic_last_message ON mqtt_topic(last_message_time DESC);
```

