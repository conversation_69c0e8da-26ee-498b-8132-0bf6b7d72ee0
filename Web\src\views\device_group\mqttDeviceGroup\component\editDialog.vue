﻿<script lang="ts" name="mqttDeviceGroup" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useMqttDeviceGroupApi } from '/@/api/device_group/mqttDeviceGroup';

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const mqttDeviceGroupApi = useMqttDeviceGroupApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
});

// 自行添加其他规则
const rules = ref<FormRules>({
  instanceId: [{required: true, message: '请选择实例ID！', trigger: 'blur',},],
  groupId: [{required: true, message: '请选择设备组标识（如：GID_TestGroup）！', trigger: 'blur',},],
  groupName: [{required: true, message: '请选择设备组名称！', trigger: 'blur',},],
  groupType: [{required: true, message: '请选择设备组类型（Production、Test、Development等）！', trigger: 'blur',},],
  productKey: [{required: true, message: '请选择产品Key（阿里云IoT产品标识）！', trigger: 'blur',},],
  maxDeviceCount: [{required: true, message: '请选择最大设备数量！', trigger: 'blur',},],
  currentDeviceCount: [{required: true, message: '请选择当前设备数量！', trigger: 'blur',},],
  defaultQosLevel: [{required: true, message: '请选择默认QoS等级（0,1,2）！', trigger: 'blur',},],
  maxMessageSize: [{required: true, message: '请选择最大消息大小（字节）！', trigger: 'blur',},],
  messageRateLimit: [{required: true, message: '请选择消息速率限制（消息/秒）！', trigger: 'blur',},],
  byteRateLimit: [{required: true, message: '请选择字节速率限制（字节/秒）！', trigger: 'blur',},],
  maxConnections: [{required: true, message: '请选择最大连接数！', trigger: 'blur',},],
  connectionTimeout: [{required: true, message: '请选择连接超时时间（秒）！', trigger: 'blur',},],
  keepAliveTimeout: [{required: true, message: '请选择心跳超时时间（秒）！', trigger: 'blur',},],
  enableRetainMessage: [{required: true, message: '请选择是否允许保留消息！', trigger: 'blur',},],
  enableWildcardSubscription: [{required: true, message: '请选择是否允许通配符订阅！', trigger: 'blur',},],
  enableSharedSubscription: [{required: true, message: '请选择是否允许共享订阅！', trigger: 'blur',},],
  securityLevel: [{required: true, message: '请选择安全级别（Low、Medium、High）！', trigger: 'blur',},],
  encryptionRequired: [{required: true, message: '请选择是否要求加密连接！', trigger: 'blur',},],
  certificateRequired: [{required: true, message: '请选择是否要求客户端证书！', trigger: 'blur',},],
  authMode: [{required: true, message: '请选择认证模式（Signature、Username、JWT、Certificate）！', trigger: 'blur',},],
  signMethod: [{required: true, message: '请选择签名算法（hmacsha1、hmacsha256、hmacmd5）！', trigger: 'blur',},],
  accessKeyId: [{required: true, message: '请选择AccessKey ID（阿里云认证）！', trigger: 'blur',},],
  accessKeySecret: [{required: true, message: '请选择AccessKey Secret（加密存储）！', trigger: 'blur',},],
  isEnabled: [{required: true, message: '请选择是否启用！', trigger: 'blur',},],
  createdDeviceCount: [{required: true, message: '请选择已创建设备数！', trigger: 'blur',},],
  onlineDeviceCount: [{required: true, message: '请选择在线设备数！', trigger: 'blur',},],
  totalMessageCount: [{required: true, message: '请选择总消息数！', trigger: 'blur',},],
  totalByteCount: [{required: true, message: '请选择总字节数！', trigger: 'blur',},],
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? { currentDeviceCount: 0,defaultQosLevel: false,enableRetainMessage: false,enableWildcardSubscription: false,enableSharedSubscription: false,encryptionRequired: false,certificateRequired: false,isEnabled: true,createdDeviceCount: 0,onlineDeviceCount: 0,totalMessageCount: '0',totalByteCount: '0', };
	state.ruleForm = row.id ? await mqttDeviceGroupApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));
	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await mqttDeviceGroupApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="mqttDeviceGroup-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="实例ID" prop="instanceId">
							<el-input v-model="state.ruleForm.instanceId" placeholder="请输入实例ID" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="设备组标识（如：GID_TestGroup）" prop="groupId">
							<el-input v-model="state.ruleForm.groupId" placeholder="请输入设备组标识（如：GID_TestGroup）" maxlength="64" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="设备组名称" prop="groupName">
							<el-input v-model="state.ruleForm.groupName" placeholder="请输入设备组名称" maxlength="128" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="设备组类型（Production、Test、Development等）" prop="groupType">
							<el-input v-model="state.ruleForm.groupType" placeholder="请输入设备组类型（Production、Test、Development等）" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="产品Key（阿里云IoT产品标识）" prop="productKey">
							<el-input v-model="state.ruleForm.productKey" placeholder="请输入产品Key（阿里云IoT产品标识）" maxlength="64" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="设备组描述" prop="description">
							<el-input v-model="state.ruleForm.description" placeholder="请输入设备组描述" maxlength="500" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最大设备数量" prop="maxDeviceCount">
							<el-input-number v-model="state.ruleForm.maxDeviceCount" placeholder="请输入最大设备数量" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="当前设备数量" prop="currentDeviceCount">
							<el-input-number v-model="state.ruleForm.currentDeviceCount" placeholder="请输入当前设备数量" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="允许的主题模式（JSON数组）" prop="allowedTopicPatterns">
							<el-input v-model="state.ruleForm.allowedTopicPatterns" placeholder="请输入允许的主题模式（JSON数组）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="禁止的主题模式（JSON数组）" prop="deniedTopicPatterns">
							<el-input v-model="state.ruleForm.deniedTopicPatterns" placeholder="请输入禁止的主题模式（JSON数组）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="默认QoS等级（0,1,2）" prop="defaultQosLevel">
							<el-switch v-model="state.ruleForm.defaultQosLevel" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最大消息大小（字节）" prop="maxMessageSize">
							<el-input-number v-model="state.ruleForm.maxMessageSize" placeholder="请输入最大消息大小（字节）" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="消息速率限制（消息/秒）" prop="messageRateLimit">
							<el-input-number v-model="state.ruleForm.messageRateLimit" placeholder="请输入消息速率限制（消息/秒）" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="字节速率限制（字节/秒）" prop="byteRateLimit">
							<el-input-number v-model="state.ruleForm.byteRateLimit" placeholder="请输入字节速率限制（字节/秒）" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最大连接数" prop="maxConnections">
							<el-input-number v-model="state.ruleForm.maxConnections" placeholder="请输入最大连接数" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="连接超时时间（秒）" prop="connectionTimeout">
							<el-input-number v-model="state.ruleForm.connectionTimeout" placeholder="请输入连接超时时间（秒）" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="心跳超时时间（秒）" prop="keepAliveTimeout">
							<el-input-number v-model="state.ruleForm.keepAliveTimeout" placeholder="请输入心跳超时时间（秒）" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否允许保留消息" prop="enableRetainMessage">
							<el-switch v-model="state.ruleForm.enableRetainMessage" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否允许通配符订阅" prop="enableWildcardSubscription">
							<el-switch v-model="state.ruleForm.enableWildcardSubscription" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否允许共享订阅" prop="enableSharedSubscription">
							<el-switch v-model="state.ruleForm.enableSharedSubscription" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="IP白名单（JSON数组）" prop="ipWhitelist">
							<el-input v-model="state.ruleForm.ipWhitelist" placeholder="请输入IP白名单（JSON数组）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="IP黑名单（JSON数组）" prop="ipBlacklist">
							<el-input v-model="state.ruleForm.ipBlacklist" placeholder="请输入IP黑名单（JSON数组）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="允许连接的时间范围（JSON）" prop="allowedTimeRanges">
							<el-input v-model="state.ruleForm.allowedTimeRanges" placeholder="请输入允许连接的时间范围（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="安全级别（Low、Medium、High）" prop="securityLevel">
							<el-input v-model="state.ruleForm.securityLevel" placeholder="请输入安全级别（Low、Medium、High）" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否要求加密连接" prop="encryptionRequired">
							<el-switch v-model="state.ruleForm.encryptionRequired" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否要求客户端证书" prop="certificateRequired">
							<el-switch v-model="state.ruleForm.certificateRequired" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="认证模式（Signature、Username、JWT、Certificate）" prop="authMode">
							<el-input v-model="state.ruleForm.authMode" placeholder="请输入认证模式（Signature、Username、JWT、Certificate）" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="签名算法（hmacsha1、hmacsha256、hmacmd5）" prop="signMethod">
							<el-input v-model="state.ruleForm.signMethod" placeholder="请输入签名算法（hmacsha1、hmacsha256、hmacmd5）" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="AccessKey ID（阿里云认证）" prop="accessKeyId">
							<el-input v-model="state.ruleForm.accessKeyId" placeholder="请输入AccessKey ID（阿里云认证）" maxlength="64" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="AccessKey Secret（加密存储）" prop="accessKeySecret">
							<el-input v-model="state.ruleForm.accessKeySecret" placeholder="请输入AccessKey Secret（加密存储）" maxlength="128" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="JWT密钥" prop="jwtSecret">
							<el-input v-model="state.ruleForm.jwtSecret" placeholder="请输入JWT密钥" maxlength="256" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="JWT过期时间（秒）" prop="jwtExpiry">
							<el-input-number v-model="state.ruleForm.jwtExpiry" placeholder="请输入JWT过期时间（秒）" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="CA证书" prop="caCertificate">
							<el-input v-model="state.ruleForm.caCertificate" placeholder="请输入CA证书" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否启用" prop="isEnabled">
							<el-switch v-model="state.ruleForm.isEnabled" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="过期时间" prop="expireTime">
							<el-date-picker v-model="state.ruleForm.expireTime" type="date" placeholder="过期时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最后活动时间" prop="lastActivity">
							<el-date-picker v-model="state.ruleForm.lastActivity" type="date" placeholder="最后活动时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="已创建设备数" prop="createdDeviceCount">
							<el-input-number v-model="state.ruleForm.createdDeviceCount" placeholder="请输入已创建设备数" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="在线设备数" prop="onlineDeviceCount">
							<el-input-number v-model="state.ruleForm.onlineDeviceCount" placeholder="请输入在线设备数" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="总消息数" prop="totalMessageCount">
							<el-input v-model="state.ruleForm.totalMessageCount" placeholder="请输入总消息数" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="总字节数" prop="totalByteCount">
							<el-input v-model="state.ruleForm.totalByteCount" placeholder="请输入总字节数" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最后消息时间" prop="lastMessageTime">
							<el-date-picker v-model="state.ruleForm.lastMessageTime" type="date" placeholder="最后消息时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="告警规则（JSON）" prop="alertRules">
							<el-input v-model="state.ruleForm.alertRules" placeholder="请输入告警规则（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="监控配置（JSON）" prop="monitorConfig">
							<el-input v-model="state.ruleForm.monitorConfig" placeholder="请输入监控配置（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="扩展属性（JSON）" prop="extendedProperties">
							<el-input v-model="state.ruleForm.extendedProperties" placeholder="请输入扩展属性（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="标签（JSON）" prop="tags">
							<el-input v-model="state.ruleForm.tags" placeholder="请输入标签（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="备注" prop="remark">
							<el-input v-model="state.ruleForm.remark" placeholder="请输入备注" maxlength="500" show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>