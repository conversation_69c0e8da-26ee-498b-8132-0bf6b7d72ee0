/* tslint:disable */
 
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 
 *
 * @export
 * @interface CreateSeedDataInput
 */
export interface CreateSeedDataInput {

    /**
     * 库标识
     *
     * @type {string}
     * @memberof CreateSeedDataInput
     */
    configId?: string | null;

    /**
     * 表名
     *
     * @type {string}
     * @memberof CreateSeedDataInput
     * @example student
     */
    tableName?: string | null;

    /**
     * 实体名称
     *
     * @type {string}
     * @memberof CreateSeedDataInput
     * @example Student
     */
    entityName?: string | null;

    /**
     * 种子名称
     *
     * @type {string}
     * @memberof CreateSeedDataInput
     * @example Student
     */
    seedDataName?: string | null;

    /**
     * 导出位置
     *
     * @type {string}
     * @memberof CreateSeedDataInput
     * @example Web.Application
     */
    position?: string | null;

    /**
     * 后缀
     *
     * @type {string}
     * @memberof CreateSeedDataInput
     * @example Web.Application
     */
    suffix?: string | null;

    /**
     * 过滤已有数据
     *
     * @type {boolean}
     * @memberof CreateSeedDataInput
     */
    filterExistingData?: boolean;
}
