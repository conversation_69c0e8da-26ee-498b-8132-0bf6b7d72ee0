# MQTT管理中台 - 路由路径导出清单

## 项目概述
- 项目名称：MQTT管理中台
- 路由模式：Hash模式 (createWebHashHistory)
- 路由控制：支持前端控制和后端控制两种模式

## 静态路由 (Static Routes)

### 1. 登录相关
- **登录页面**
  - 路径：`/login`
  - 组件：`/@/views/login/index.vue`
  - 说明：用户登录界面

- **拨号组件**
  - 路径：`/$callTel`
  - 组件：`/@/components/callTel/index.vue`
  - 说明：拨号功能组件

### 2. 错误页面
- **404页面**
  - 路径：`/:path(.*)*`
  - 组件：`/@/views/error/404.vue`
  - 说明：页面未找到

- **401页面**
  - 路径：`/401`
  - 组件：`/@/views/error/401.vue`
  - 说明：无权限访问

## 动态路由 (Dynamic Routes)

### 主布局路由 (`/`)
基础路径：`/`  
布局组件：`/@/layout/index.vue`  
重定向：`/dashboard`

#### 1. 核心功能模块

##### 1.1 仪表板
- **路径**：`/dashboard`
- **组件**：`/@/views/dashboard/index.vue`
- **标题**：MQTT管理中台
- **图标**：ele-Odometer
- **说明**：系统主页面，显示整体数据概览

##### 1.2 MQTTX测试工具
- **路径**：`/mqttx`
- **组件**：`/@/views/mqttx/index.vue`
- **标题**：MQTTX测试工具
- **图标**：ele-Connection
- **说明**：MQTT连接测试工具

##### 1.3 实时监控
- **路径**：`/monitor`
- **组件**：`/@/views/monitor/index.vue`
- **标题**：实时监控
- **图标**：ele-Monitor
- **说明**：系统实时监控界面

#### 2. MQTT业务模块

##### 2.1 MQTT实例管理
- **路径**：`/mqttInstace/mqttInstance`
- **组件**：`/@/views/mqttInstace/mqttInstance/index.vue`
- **说明**：MQTT实例的创建、配置和管理
- **子组件目录**：`/@/views/mqttInstace/mqttInstance/component/`

##### 2.2 MQTT客户端管理
- **路径**：`/mqtt_client/mqttClient`
- **组件**：`/@/views/mqtt_client/mqttClient/index.vue`
- **说明**：MQTT客户端连接管理
- **子组件目录**：`/@/views/mqtt_client/mqttClient/component/`

##### 2.3 MQTT主题管理
- **路径**：`/mqtt_topic/mqttTopic`
- **组件**：`/@/views/mqtt_topic/mqttTopic/index.vue`
- **说明**：MQTT主题订阅和发布管理
- **子组件目录**：`/@/views/mqtt_topic/mqttTopic/component/`

##### 2.4 客户端认证
- **路径**：`/client_auth/mqttClientAuth`
- **组件**：`/@/views/client_auth/mqttClientAuth/index.vue`
- **说明**：MQTT客户端认证管理
- **子组件目录**：`/@/views/client_auth/mqttClientAuth/component/`

##### 2.5 设备分组
- **路径**：`/device_group/mqttDeviceGroup`
- **组件**：`/@/views/device_group/mqttDeviceGroup/index.vue`
- **说明**：MQTT设备分组管理
- **子组件目录**：`/@/views/device_group/mqttDeviceGroup/component/`

#### 3. 系统管理模块

##### 3.1 用户管理
- **路径**：`/system/user`
- **组件**：`/@/views/system/user/index.vue`
- **说明**：系统用户管理
- **子组件目录**：`/@/views/system/user/component/`

##### 3.2 角色管理
- **路径**：`/system/role`
- **组件**：`/@/views/system/role/index.vue`
- **说明**：系统角色权限管理
- **子组件目录**：`/@/views/system/role/component/`

##### 3.3 菜单管理
- **路径**：`/system/menu`
- **组件**：`/@/views/system/menu/index.vue`
- **说明**：系统菜单配置管理
- **子组件目录**：`/@/views/system/menu/component/`

##### 3.4 组织机构
- **路径**：`/system/org`
- **组件**：`/@/views/system/org/index.vue`
- **说明**：组织机构管理
- **子组件目录**：`/@/views/system/org/component/`

##### 3.5 职位管理
- **路径**：`/system/pos`
- **组件**：`/@/views/system/pos/index.vue`
- **说明**：职位管理
- **子组件目录**：`/@/views/system/pos/component/`

##### 3.6 字典管理
- **路径**：`/system/dict`
- **组件**：`/@/views/system/dict/index.vue`
- **说明**：数据字典管理
- **子组件目录**：`/@/views/system/dict/component/`

##### 3.7 配置管理
- **路径**：`/system/config`
- **组件**：`/@/views/system/config/index.vue`
- **说明**：系统配置管理
- **子组件目录**：`/@/views/system/config/component/`

##### 3.8 缓存管理
- **路径**：`/system/cache`
- **组件**：`/@/views/system/cache/index.vue`
- **说明**：系统缓存管理

##### 3.9 在线用户
- **路径**：`/system/onlineUser`
- **组件**：`/@/views/system/onlineUser/index.vue`
- **说明**：在线用户监控

##### 3.10 服务监控
- **路径**：`/system/server`
- **组件**：`/@/views/system/server/index.vue`
- **说明**：服务器监控
- **额外页面**：`/@/views/system/server/new.vue`

#### 4. 开发工具模块

##### 4.1 代码生成
- **路径**：`/system/codeGen`
- **组件**：`/@/views/system/codeGen/index.vue`
- **说明**：代码生成工具
- **子组件目录**：`/@/views/system/codeGen/component/`

##### 4.2 数据库管理
- **路径**：`/system/database`
- **组件**：`/@/views/system/database/index.vue`
- **说明**：数据库管理
- **可视化页面**：`/develop/database/visual` → `/@/views/system/database/component/visualTable.vue`
- **子组件目录**：`/@/views/system/database/component/`

##### 4.3 表单设计
- **路径**：`/system/formDes`
- **组件**：`/@/views/system/formDes/index.vue`
- **说明**：表单设计器

##### 4.4 打印模板
- **路径**：`/system/print`
- **组件**：`/@/views/system/print/index.vue`
- **说明**：打印模板管理
- **子组件目录**：`/@/views/system/print/component/`

#### 5. 任务调度模块

##### 5.1 定时任务
- **路径**：`/system/job`
- **组件**：`/@/views/system/job/index.vue`
- **说明**：定时任务管理
- **任务看板**：`/platform/job/dashboard` → `/@/views/system/job/dashboard.vue`
- **子组件目录**：`/@/views/system/job/component/`

#### 6. 日志管理模块

##### 6.1 操作日志
- **路径**：`/system/log/oplog`
- **组件**：`/@/views/system/log/oplog/index.vue`
- **说明**：操作日志查看

##### 6.2 异常日志
- **路径**：`/system/log/exlog`
- **组件**：`/@/views/system/log/exlog/index.vue`
- **说明**：异常日志查看

##### 6.3 访问日志
- **路径**：`/system/log/vislog`
- **组件**：`/@/views/system/log/vislog/index.vue`
- **说明**：访问日志查看

##### 6.4 差异日志
- **路径**：`/system/log/difflog`
- **组件**：`/@/views/system/log/difflog/index.vue`
- **说明**：数据差异日志

#### 7. 文件管理模块

##### 7.1 文件管理
- **路径**：`/system/file`
- **组件**：`/@/views/system/file/index.vue`
- **说明**：文件上传下载管理
- **子组件目录**：`/@/views/system/file/component/`

#### 8. 通知公告模块

##### 8.1 通知公告
- **路径**：`/system/notice`
- **组件**：`/@/views/system/notice/index.vue`
- **说明**：系统通知公告管理
- **子组件目录**：`/@/views/system/notice/component/`

#### 9. 多租户模块

##### 9.1 租户管理
- **路径**：`/system/tenant`
- **组件**：`/@/views/system/tenant/index.vue`
- **说明**：多租户管理
- **子组件目录**：`/@/views/system/tenant/component/`

##### 9.2 租户配置
- **路径**：`/system/tenantConfig`
- **组件**：`/@/views/system/tenantConfig/index.vue`
- **说明**：租户配置管理
- **子组件目录**：`/@/views/system/tenantConfig/component/`

#### 10. 其他功能模块

##### 10.1 关于系统
- **路径**：`/about`
- **组件**：`/@/views/about/index.vue`
- **说明**：系统信息页面

##### 10.2 首页
- **路径**：`/home`
- **组件**：`/@/views/home/<USER>
- **说明**：首页
- **通知页面**：`/@/views/home/<USER>/index.vue`
- **小部件**：`/@/views/home/<USER>/index.vue`

##### 10.3 实时通讯
- **路径**：`/elive`
- **组件**：`/@/views/elive/index.vue`
- **说明**：实时通讯功能

##### 10.4 审批流程
- **路径**：`/approvalFlow`
- **组件**：`/@/views/approvalFlow/index.vue`
- **说明**：审批流程管理
- **子组件目录**：`/@/views/approvalFlow/component/`

##### 10.5 插件管理
- **路径**：`/system/plugin`
- **组件**：`/@/views/system/plugin/index.vue`
- **说明**：插件管理
- **子组件目录**：`/@/views/system/plugin/component/`

##### 10.6 开放接口
- **路径**：`/system/openAccess`
- **组件**：`/@/views/system/openAccess/index.vue`
- **说明**：开放接口管理
- **子组件目录**：`/@/views/system/openAccess/component/`

##### 10.7 LDAP配置
- **路径**：`/system/ldap`
- **组件**：`/@/views/system/ldap/index.vue`
- **说明**：LDAP认证配置
- **子组件目录**：`/@/views/system/ldap/component/`

##### 10.8 地区管理
- **路径**：`/system/region`
- **组件**：`/@/views/system/region/index.vue`
- **说明**：地区数据管理
- **子组件目录**：`/@/views/system/region/component/`

##### 10.9 压力测试
- **路径**：`/system/stressTest`
- **组件**：`/@/views/system/stressTest/index.vue`
- **说明**：系统压力测试
- **子组件目录**：`/@/views/system/stressTest/component/`

##### 10.10 模板管理
- **路径**：`/system/template`
- **组件**：`/@/views/system/template/index.vue`
- **说明**：模板管理
- **子组件目录**：`/@/views/system/template/component/`

##### 10.11 系统更新
- **路径**：`/system/update`
- **组件**：`/@/views/system/update/index.vue`
- **说明**：系统更新管理

##### 10.12 用户注册方式
- **路径**：`/system/userRegWay`
- **组件**：`/@/views/system/userRegWay/index.vue`
- **说明**：用户注册方式配置
- **子组件目录**：`/@/views/system/userRegWay/component/`

##### 10.13 微信支付
- **路径**：`/system/weChatPay`
- **组件**：`/@/views/system/weChatPay/index.vue`
- **说明**：微信支付配置

##### 10.14 微信用户
- **路径**：`/system/weChatUser`
- **组件**：`/@/views/system/weChatUser/index.vue`
- **说明**：微信用户管理
- **子组件目录**：`/@/views/system/weChatUser/component/`

##### 10.15 信息设置
- **路径**：`/system/infoSetting`
- **组件**：`/@/views/system/infoSetting/index.vue`
- **说明**：系统信息设置

## 路由配置说明

### 路由元信息 (Meta)
- `title`: 菜单标题和标签页标题
- `isLink`: 是否为外链菜单
- `isHide`: 是否隐藏该路由
- `isKeepAlive`: 是否缓存组件状态
- `isAffix`: 是否固定在标签页上
- `isIframe`: 是否为内嵌窗口
- `roles`: 路由权限标识
- `icon`: 菜单图标
- `isPublic`: 是否为公开路由（无需登录）

### 路由控制模式
1. **前端控制路由** (`isRequestRoutes: false`)
   - 路由配置写在前端代码中
   - 需要配置 `roles` 权限
   - 通过 `setFilterRoute` 方法过滤

2. **后端控制路由** (`isRequestRoutes: true`)
   - 路由配置由后端API返回
   - 调用 `/api/sysMenu/loginMenuTree` 接口获取菜单树
   - 动态生成路由配置

### API接口
- **获取登录菜单树**: `GET /api/sysMenu/loginMenuTree`
- **获取菜单列表**: `GET /api/sysMenu/list`
- **获取角色菜单**: `GET /api/sysRole/ownMenuList/{id}`
- **获取用户收藏菜单**: `GET /api/sysUserMenu/userMenuList`

## 文件结构说明

### 路由相关文件
- `src/router/index.ts` - 路由主配置文件
- `src/router/route.ts` - 静态路由和动态路由定义
- `src/router/backEnd.ts` - 后端控制路由逻辑
- `src/router/frontEnd.ts` - 前端控制路由逻辑

### 视图组件目录
- `src/views/` - 所有页面组件
- `src/layout/` - 布局组件
- `src/components/` - 公共组件

---

**导出时间**: " + new Date().toLocaleString() + "  
**项目路径**: d:\Project\欧信MQTT中台\mqtt-middle-platform\Web  
**总路由数量**: 约60+个路由页面