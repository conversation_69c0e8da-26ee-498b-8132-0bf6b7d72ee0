/* tslint:disable */
 
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 系统用户域配置表
 *
 * @export
 * @interface SysUserLdap
 */
export interface SysUserLdap {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof SysUserLdap
     */
    id?: number;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof SysUserLdap
     */
    tenantId?: number | null;

    /**
     * 用户Id
     *
     * @type {number}
     * @memberof SysUserLdap
     */
    userId?: number;

    /**
     * 域账号  AD域对应sAMAccountName  Ldap对应uid
     *
     * @type {string}
     * @memberof SysUserLdap
     */
    account: string;

    /**
     * 域用户名
     *
     * @type {string}
     * @memberof SysUserLdap
     */
    userName?: string | null;

    /**
     * 对应EmployeeId(用于数据导入对照)
     *
     * @type {string}
     * @memberof SysUserLdap
     */
    employeeId?: string | null;

    /**
     * 组织代码
     *
     * @type {string}
     * @memberof SysUserLdap
     */
    deptCode?: string | null;

    /**
     * 最后设置密码时间
     *
     * @type {Date}
     * @memberof SysUserLdap
     */
    pwdLastSetTime?: Date | null;

    /**
     * 邮箱
     *
     * @type {string}
     * @memberof SysUserLdap
     */
    mail?: string | null;

    /**
     * 检查账户是否已过期
     *
     * @type {boolean}
     * @memberof SysUserLdap
     */
    accountExpiresFlag?: boolean;

    /**
     * 密码设置是否永不过期
     *
     * @type {boolean}
     * @memberof SysUserLdap
     */
    dontExpiresFlag?: boolean;

    /**
     * DN
     *
     * @type {string}
     * @memberof SysUserLdap
     */
    dn?: string | null;
}
