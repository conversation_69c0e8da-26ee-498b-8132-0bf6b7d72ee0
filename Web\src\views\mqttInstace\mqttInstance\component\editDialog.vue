﻿<script lang="ts" name="mqttInstance" setup>
import { ref, reactive, onMounted, onUnmounted, watch } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { InfoFilled, Key, Lock, Platform, Connection } from '@element-plus/icons-vue';
import { formatDate } from '/@/utils/formatTime';
import { useMqttInstanceApi } from '/@/api/mqttInstace/mqttInstance';

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const mqttInstanceApi = useMqttInstanceApi();
const ruleFormRef = ref();

// 实时数据状态
const realtimeData = reactive({
	currentConnections: 0,
	totalMessages: 0,
	totalBytes: 0,
	lastHeartbeat: null as Date | null,
	lastActivity: null as Date | null,
	status: 'offline',
	connectionTrend: [] as number[],
	messageRate: 0,
	byteRate: 0
});

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
	isEdit: false
});

// 表单验证规则
const rules = ref<FormRules>({
	instanceName: [
		{ required: true, message: '请输入实例名称', trigger: 'blur' },
		{ min: 2, max: 128, message: '长度在 2 到 128 个字符', trigger: 'blur' }
	],
	instanceCode: [
		{ required: true, message: '请输入实例编码', trigger: 'blur' },
		{ pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线和横线', trigger: 'blur' }
	],
	instanceType: [
		{ required: true, message: '请选择实例类型', trigger: 'change' }
	],
	serverHost: [
		{ required: true, message: '请输入服务器地址', trigger: 'blur' }
	],
	serverPort: [
		{ required: true, message: '请输入服务器端口', trigger: 'blur' },
		{ type: 'number', min: 1, max: 65535, message: '端口范围 1-65535', trigger: 'blur' }
	],
	apiHost: [
		{ required: true, message: '请输入API地址', trigger: 'blur' }
	],
	apiPort: [
		{ required: true, message: '请输入API端口', trigger: 'blur' },
		{ type: 'number', min: 1, max: 65535, message: '端口范围 1-65535', trigger: 'blur' }
	],
	apiUsername: [
		{ required: true, message: '请输入API用户名', trigger: 'blur' }
	],
	apiPassword: [
		{ required: true, message: '请输入API密码', trigger: 'blur' }
	]
});

// 实例类型选项
const instanceTypeOptions = [
	{ label: 'EMQX', value: 'EMQX' },
	{ label: '阿里云IoT', value: 'ALIYUN' },
	{ label: 'AWS IoT', value: 'AWS' },
	{ label: '腾讯云IoT', value: 'TENCENT' },
	{ label: '华为云IoT', value: 'HUAWEI' }
];

// 导入拼音转换库
import { pinyin } from 'pinyin-pro';

// 特殊词汇映射表（用于特定业务术语的优化）
const specialTermsMap: { [key: string]: string } = {
	'测试': 'test', '开发': 'dev', '生产': 'prod', '预发': 'pre',
	'实例': 'instance', '服务': 'service', '平台': 'platform',
	'系统': 'system', '管理': 'manage', '监控': 'monitor',
	'数据': 'data', '消息': 'message', '设备': 'device',
	'客户端': 'client', '主题': 'topic', '连接': 'connection'
};

// 中文转英文函数（使用自动拼音转换）
const convertChineseToEnglish = (text: string): string => {
	if (!text) return '';
	
	// 移除特殊字符，只保留中文、英文、数字
	let cleanText = text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '');
	
	// 优先使用特殊词汇映射表进行替换
	for (const [chinese, english] of Object.entries(specialTermsMap)) {
		cleanText = cleanText.replace(new RegExp(chinese, 'g'), english);
	}
	
	// 使用pinyin-pro自动转换剩余的中文字符
	try {
		// 转换为拼音，不带音调，去除空格
		const pinyinResult = pinyin(cleanText, { 
			toneType: 'none',  // 不带音调
			type: 'array'      // 返回数组格式
		});
		
		// 将拼音数组连接成字符串，去除空格
		const result = pinyinResult.join('').replace(/\s+/g, '');
		
		// 转换为小写并限制长度
		return result.toLowerCase().substring(0, 32);
	} catch (error) {
		console.warn('拼音转换失败，使用原文本:', error);
		// 如果拼音转换失败，返回清理后的文本
		return cleanText.toLowerCase().substring(0, 32);
	}
};

// 生成随机后缀
const generateRandomSuffix = (): string => {
	return Math.random().toString(36).substring(2, 6);
};

// 检查实例编码是否存在
const checkInstanceCodeExists = async (code: string): Promise<boolean> => {
	try {
		const response = await mqttInstanceApi.page({
			pageNum: 1,
			pageSize: 1,
			instanceCode: code
		});
		return response.data.result.total > 0;
	} catch (error) {
		console.error('检查实例编码失败:', error);
		return false;
	}
};

// 生成唯一的实例编码
const generateUniqueInstanceCode = async (baseName: string): Promise<string> => {
	let baseCode = convertChineseToEnglish(baseName);
	if (!baseCode) {
		baseCode = 'instance';
	}
	
	// 首先检查基础编码是否可用
	const baseExists = await checkInstanceCodeExists(baseCode);
	if (!baseExists) {
		return baseCode;
	}
	
	// 如果基础编码已存在，添加随机后缀
	let attempts = 0;
	const maxAttempts = 10;
	
	while (attempts < maxAttempts) {
		const suffix = generateRandomSuffix();
		const codeWithSuffix = `${baseCode}_${suffix}`;
		
		const exists = await checkInstanceCodeExists(codeWithSuffix);
		if (!exists) {
			return codeWithSuffix;
		}
		
		attempts++;
	}
	
	// 如果多次尝试都失败，使用时间戳
	const timestamp = Date.now().toString().slice(-6);
	return `${baseCode}_${timestamp}`;
};

// WebSocket连接状态
const wsState = reactive({
	webSocket: null as WebSocket | null,
	connectionStatus: 'disconnected', // disconnected, connecting, connected, error
	reconnectAttempts: 0,
	maxReconnectAttempts: 5,
	reconnectInterval: 3000
});

// WebSocket连接管理
const connectWebSocket = (instanceId: string) => {
	if (wsState.webSocket && wsState.webSocket.readyState === WebSocket.OPEN) {
		return;
	}

	try {
		// 构建WebSocket URL，实际项目中应该从配置文件获取
		const wsUrl = `ws://localhost:8080/ws/mqtt-instance/${instanceId}`;
		wsState.connectionStatus = 'connecting';
		
		wsState.webSocket = new WebSocket(wsUrl);
		
		wsState.webSocket.onopen = () => {
			console.log('WebSocket连接已建立');
			wsState.connectionStatus = 'connected';
			wsState.reconnectAttempts = 0;
			
			// 发送订阅消息
			const subscribeMessage = {
				type: 'subscribe',
				topics: ['instance.metrics', 'instance.status', 'instance.connections']
			};
			wsState.webSocket?.send(JSON.stringify(subscribeMessage));
		};
		
		wsState.webSocket.onmessage = (event) => {
			try {
				const data = JSON.parse(event.data);
				handleWebSocketMessage(data);
			} catch (error) {
				console.error('解析WebSocket消息失败:', error);
			}
		};
		
		wsState.webSocket.onclose = (event) => {
			console.log('WebSocket连接已关闭:', event.code, event.reason);
			wsState.connectionStatus = 'disconnected';
			
			// 如果不是主动关闭，尝试重连
			if (event.code !== 1000 && wsState.reconnectAttempts < wsState.maxReconnectAttempts) {
				setTimeout(() => {
					wsState.reconnectAttempts++;
					console.log(`尝试重连 (${wsState.reconnectAttempts}/${wsState.maxReconnectAttempts})`);
					connectWebSocket(instanceId);
				}, wsState.reconnectInterval);
			}
		};
		
		wsState.webSocket.onerror = (error) => {
			console.error('WebSocket连接错误:', error);
			wsState.connectionStatus = 'error';
		};
		
	} catch (error) {
		console.error('创建WebSocket连接失败:', error);
		wsState.connectionStatus = 'error';
	}
};

// 处理WebSocket消息
const handleWebSocketMessage = (data: any) => {
	switch (data.type) {
		case 'metrics':
			// 更新实时指标数据
			if (data.payload) {
				realtimeData.currentConnections = data.payload.currentConnections || 0;
				realtimeData.totalMessages = data.payload.totalMessages || 0;
				realtimeData.totalBytes = data.payload.totalBytes || 0;
				realtimeData.messageRate = data.payload.messageRate || 0;
				realtimeData.byteRate = data.payload.byteRate || 0;
				
				// 更新连接趋势数据
				realtimeData.connectionTrend.push(realtimeData.currentConnections);
				if (realtimeData.connectionTrend.length > 20) {
					realtimeData.connectionTrend.shift();
				}
			}
			break;
			
		case 'status':
			// 更新实例状态
			if (data.payload) {
				realtimeData.status = data.payload.status || 'offline';
				realtimeData.lastHeartbeat = data.payload.lastHeartbeat ? new Date(data.payload.lastHeartbeat) : null;
				realtimeData.lastActivity = data.payload.lastActivity ? new Date(data.payload.lastActivity) : null;
			}
			break;
			
		case 'connections':
			// 更新连接信息
			if (data.payload) {
				realtimeData.currentConnections = data.payload.count || 0;
			}
			break;
			
		case 'error':
			console.error('WebSocket服务器错误:', data.message);
			ElMessage.error(`实时数据更新错误: ${data.message}`);
			break;
			
		default:
			console.log('未知的WebSocket消息类型:', data.type);
	}
};

// 断开WebSocket连接
const disconnectWebSocket = () => {
	if (wsState.webSocket) {
		wsState.webSocket.close(1000, '用户主动关闭');
		wsState.webSocket = null;
		wsState.connectionStatus = 'disconnected';
		wsState.reconnectAttempts = 0;
	}
};

// 开始实时数据更新（使用WebSocket）
const startRealtimeUpdates = (instanceId?: string) => {
	if (!instanceId && state.ruleForm.id) {
		instanceId = state.ruleForm.id;
	}
	
	if (instanceId) {
		connectWebSocket(instanceId);
	} else {
		console.warn('无法启动实时更新：缺少实例ID');
	}
};

// 停止实时数据更新
const stopRealtimeUpdates = () => {
	disconnectWebSocket();
};

// 监听实例名称变化，自动生成实例编码
watch(
	() => state.ruleForm.instanceName,
	async (newName: string, oldName: string) => {
		// 只在新建模式下且实例编码为空或者是自动生成的编码时才自动更新
		if (!state.isEdit && newName && newName.trim()) {
			// 检查当前编码是否是基于旧名称自动生成的
			const shouldUpdate = !state.ruleForm.instanceCode || 
				(oldName && state.ruleForm.instanceCode === convertChineseToEnglish(oldName)) ||
				(oldName && state.ruleForm.instanceCode.startsWith(convertChineseToEnglish(oldName) + '_'));
			
			if (shouldUpdate) {
				try {
					const newCode = await generateUniqueInstanceCode(newName.trim());
					state.ruleForm.instanceCode = newCode;
				} catch (error) {
					console.error('生成实例编码失败:', error);
					ElMessage.warning('自动生成实例编码失败，请手动输入');
				}
			}
		}
	},
	{ immediate: false }
);

// 页面加载时
onMounted(async () => {
	// 初始化实时数据
});

// 页面卸载时
onUnmounted(() => {
	stopRealtimeUpdates();
});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? {};
	state.isEdit = !!row.id;
	
	// 初始化表单数据，只包含用户可编辑的字段
	if (row.id) {
		const detail = await mqttInstanceApi.detail(row.id).then(res => res.data.result);
		state.ruleForm = {
			id: detail.id,
			instanceName: detail.instanceName,
			instanceCode: detail.instanceCode,
			instanceType: detail.instanceType,
			serverHost: detail.serverHost,
			serverPort: detail.serverPort,
			apiHost: detail.apiHost,
			apiPort: detail.apiPort,
			apiUsername: detail.apiUsername,
			apiPassword: detail.apiPassword,
			enableSsl: detail.enableSsl,
			sslPort: detail.sslPort,
			enableWebSocket: detail.enableWebSocket,
			wsPort: detail.wsPort,
			wssPort: detail.wssPort,
			maxConnections: detail.maxConnections,
			aliyunProductKey: detail.aliyunProductKey,
			aliyunRegionId: detail.aliyunRegionId,
			aliyunInstanceId: detail.aliyunInstanceId,
			deviceIdPrefix: detail.deviceIdPrefix,
			groupIdPrefix: detail.groupIdPrefix,
			aliyunCompatible: detail.aliyunCompatible,
			isEnabled: detail.isEnabled,
			configJson: detail.configJson,
			remark: detail.remark
		};
		
		// 初始化实时数据
		realtimeData.currentConnections = detail.currentConnections || 0;
		realtimeData.totalMessages = detail.totalMessages || 0;
		realtimeData.totalBytes = detail.totalBytes || 0;
		realtimeData.lastHeartbeat = detail.lastHeartbeat ? new Date(detail.lastHeartbeat) : null;
		realtimeData.lastActivity = detail.lastActivity ? new Date(detail.lastActivity) : null;
		realtimeData.status = detail.status || 'offline';
		
		// 开始实时数据更新
		startRealtimeUpdates(detail.id);
	} else {
		state.ruleForm = {
			instanceName: '',
			instanceCode: '',
			instanceType: 'EMQX',
			serverHost: '',
			serverPort: 1883,
			apiHost: '',
			apiPort: 18083,
			apiUsername: '',
			apiPassword: '',
			enableSsl: false,
			sslPort: 8883,
			enableWebSocket: false,
			wsPort: 8083,
			wssPort: 8084,
			maxConnections: 1000,
			aliyunProductKey: '',
			aliyunRegionId: '',
			aliyunInstanceId: '',
			deviceIdPrefix: '',
			groupIdPrefix: '',
			aliyunCompatible: false,
			isEnabled: true,
			configJson: '',
			remark: ''
		};
	}
	
	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	stopRealtimeUpdates();
	emit("reloadTable");
	state.showDialog = false;
};

// 测试连接
const testConnection = async () => {
	state.loading = true;
	try {
		// 这里应该调用测试连接的API
		await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用
		ElMessage.success('连接测试成功');
	} catch (error) {
		ElMessage.error('连接测试失败');
	} finally {
		state.loading = false;
	}
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			state.loading = true;
			try {
				let values = { ...state.ruleForm };
				await mqttInstanceApi[state.ruleForm.id ? 'update' : 'add'](values);
				ElMessage.success(state.ruleForm.id ? '更新成功' : '创建成功');
				closeDialog();
			} catch (error) {
				ElMessage.error('操作失败，请重试');
			} finally {
				state.loading = false;
			}
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

// 格式化字节数
const formatBytes = (bytes: number) => {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 格式化数字
const formatNumber = (num: number) => {
	return num.toLocaleString();
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="mqttInstance-container">
		<el-dialog v-model="state.showDialog" :width="1200" draggable :close-on-click-modal="false"
			class="modern-dialog">
			<template #header>
				<div class="dialog-header">
					<div class="header-left">
						<el-icon class="header-icon">
							<Setting />
						</el-icon>
						<span class="header-title">{{ state.title }}</span>
						<el-tag v-if="state.isEdit" :type="realtimeData.status === 'online' ? 'success' : 'danger'"
							size="small">
							{{ realtimeData.status === 'online' ? '在线' : '离线' }}
						</el-tag>
					</div>
					<div class="header-right" v-if="state.isEdit">
						<el-button size="small" @click="testConnection" :loading="state.loading">
							<el-icon>
								<Connection />
							</el-icon>
							测试连接
						</el-button>
					</div>
				</div>
			</template>

			<div class="dialog-content">
				<!-- 实时状态面板 -->
				<div v-if="state.isEdit" class="realtime-panel">
						<div class="panel-title">
						<div class="title-left">
							<el-icon>
								<Monitor />
							</el-icon>
							实时监控
						</div>
						<div class="connection-status">
							<el-tag 
								:type="wsState.connectionStatus === 'connected' ? 'success' : 
									   wsState.connectionStatus === 'connecting' ? 'warning' : 'danger'"
								size="small"
								effect="dark"
							>
								<el-icon style="margin-right: 4px;">
									<Connection v-if="wsState.connectionStatus === 'connected'" />
									<Loading v-else-if="wsState.connectionStatus === 'connecting'" />
									<Close v-else />
								</el-icon>
								{{ wsState.connectionStatus === 'connected' ? 'WebSocket已连接' : 
								   wsState.connectionStatus === 'connecting' ? 'WebSocket连接中' : 
								   wsState.connectionStatus === 'error' ? 'WebSocket连接错误' : 'WebSocket未连接' }}
							</el-tag>
						</div>
					</div>
					<el-row :gutter="16" class="metrics-row">
						<el-col :span="6">
							<div class="metric-card">
								<div class="metric-icon connections">
									<el-icon>
										<Link />
									</el-icon>
								</div>
								<div class="metric-content">
									<div class="metric-value">{{ formatNumber(realtimeData.currentConnections) }}</div>
									<div class="metric-label">当前连接数</div>
								</div>
							</div>
						</el-col>
						<el-col :span="6">
							<div class="metric-card">
								<div class="metric-icon messages">
									<el-icon>
										<ChatDotRound />
									</el-icon>
								</div>
								<div class="metric-content">
									<div class="metric-value">{{ formatNumber(realtimeData.totalMessages) }}</div>
									<div class="metric-label">总消息数</div>
									<div class="metric-rate">{{ realtimeData.messageRate }}/s</div>
								</div>
							</div>
						</el-col>
						<el-col :span="6">
							<div class="metric-card">
								<div class="metric-icon bytes">
									<el-icon>
										<DataBoard />
									</el-icon>
								</div>
								<div class="metric-content">
									<div class="metric-value">{{ formatBytes(realtimeData.totalBytes) }}</div>
									<div class="metric-label">总字节数</div>
									<div class="metric-rate">{{ formatBytes(realtimeData.byteRate) }}/s</div>
								</div>
							</div>
						</el-col>
						<el-col :span="6">
							<div class="metric-card">
								<div class="metric-icon heartbeat">
									<el-icon>
										<Timer />
									</el-icon>
								</div>
								<div class="metric-content">
									<div class="metric-value">{{ realtimeData.lastHeartbeat ?
										formatDate(realtimeData.lastHeartbeat, 'HH:mm:ss') : '--' }}</div>
									<div class="metric-label">最后心跳</div>
								</div>
							</div>
						</el-col>
					</el-row>
				</div>

				<!-- 配置表单 -->
				<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="120px" :rules="rules"
					class="config-form">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>

					<!-- 基础信息 -->
					<div class="config-section">
						<div class="section-title">
							<el-icon>
								<InfoFilled />
							</el-icon>
							基础信息
						</div>
						<el-row :gutter="24">
							<el-col :span="12" class="mb10">
								<el-form-item label="实例名称" prop="instanceName">
									<el-input v-model="state.ruleForm.instanceName" placeholder="请输入实例名称"
										maxlength="128" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :span="12" class="mb10">
								<el-form-item label="实例编码" prop="instanceCode">
									<el-input v-model="state.ruleForm.instanceCode" 
										:placeholder="state.isEdit ? '请输入实例编码' : '将根据实例名称自动生成'"
										maxlength="64" show-word-limit clearable>
										<template #suffix v-if="!state.isEdit">
											<el-tooltip content="编码将根据实例名称自动生成，支持手动修改" placement="top">
												<el-icon class="auto-generate-tip"><InfoFilled /></el-icon>
											</el-tooltip>
										</template>
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12" class="mb10">
								<el-form-item label="实例类型" prop="instanceType">
									<el-select v-model="state.ruleForm.instanceType" placeholder="请选择实例类型" clearable>
										<el-option v-for="item in instanceTypeOptions" :key="item.value"
											:label="item.label" :value="item.value" />
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="12" class="mb10">
								<el-form-item label="最大连接数" prop="maxConnections">
									<el-input-number v-model="state.ruleForm.maxConnections" placeholder="请输入最大连接数"
										:min="1" :max="100000" clearable />
								</el-form-item>
							</el-col>
						</el-row>
					</div>

					<!-- MQTT连接配置 -->
					<div class="config-section">
						<div class="section-title">
							<el-icon>
								<Connection />
							</el-icon>
							MQTT连接配置
						</div>
						<el-row :gutter="24">
							<el-col :span="12" class="mb10">
								<el-form-item label="服务器地址" prop="serverHost">
									<el-input v-model="state.ruleForm.serverHost" placeholder="请输入服务器地址" maxlength="128"
										clearable />
								</el-form-item>
							</el-col>
							<el-col :span="12" class="mb10">
								<el-form-item label="服务器端口" prop="serverPort">
									<el-input-number v-model="state.ruleForm.serverPort" placeholder="请输入服务器端口" :min="1"
										:max="65535" clearable />
								</el-form-item>
							</el-col>
						</el-row>
					</div>

					<!-- API管理配置 -->
					<div class="config-section">
						<div class="section-title">
							<el-icon>
								<Key />
							</el-icon>
							API管理配置
						</div>
						<el-row :gutter="24">
							<el-col :span="12" class="mb10">
								<el-form-item label="API地址" prop="apiHost">
									<el-input v-model="state.ruleForm.apiHost" placeholder="请输入API地址" maxlength="128"
										clearable />
								</el-form-item>
							</el-col>
							<el-col :span="12" class="mb10">
								<el-form-item label="API端口" prop="apiPort">
									<el-input-number v-model="state.ruleForm.apiPort" placeholder="请输入API端口" :min="1"
										:max="65535" clearable />
								</el-form-item>
							</el-col>
							<el-col :span="12" class="mb10">
								<el-form-item label="API用户名" prop="apiUsername">
									<el-input v-model="state.ruleForm.apiUsername" placeholder="请输入API用户名"
										maxlength="64" clearable />
								</el-form-item>
							</el-col>
							<el-col :span="12" class="mb10">
								<el-form-item label="API密码" prop="apiPassword">
									<el-input v-model="state.ruleForm.apiPassword" type="password" show-password
										placeholder="请输入API密码" maxlength="128" clearable />
								</el-form-item>
							</el-col>
						</el-row>
					</div>

					<!-- SSL/WebSocket配置 -->
					<div class="config-section">
						<div class="section-title">
							<el-icon>
								<Lock />
							</el-icon>
							SSL/WebSocket配置
						</div>
						<el-row :gutter="24">
							<el-col :span="6" class="mb10">
								<el-form-item label="启用SSL" prop="enableSsl">
									<el-switch v-model="state.ruleForm.enableSsl" />
								</el-form-item>
							</el-col>
							<el-col :span="6" class="mb10">
								<el-form-item label="SSL端口" prop="sslPort">
									<el-input-number v-model="state.ruleForm.sslPort" placeholder="SSL端口" :min="1"
										:max="65535" :disabled="!state.ruleForm.enableSsl" clearable />
								</el-form-item>
							</el-col>
							<el-col :span="6" class="mb10">
								<el-form-item label="启用WebSocket" prop="enableWebSocket">
									<el-switch v-model="state.ruleForm.enableWebSocket" />
								</el-form-item>
							</el-col>
							<el-col :span="6" class="mb10">
								<el-form-item label="WebSocket端口" prop="wsPort">
									<el-input-number v-model="state.ruleForm.wsPort" placeholder="WS端口" :min="1"
										:max="65535" :disabled="!state.ruleForm.enableWebSocket" clearable />
								</el-form-item>
							</el-col>
							<el-col :span="12" class="mb10"
								v-if="state.ruleForm.enableWebSocket && state.ruleForm.enableSsl">
								<el-form-item label="WebSocket SSL端口" prop="wssPort">
									<el-input-number v-model="state.ruleForm.wssPort" placeholder="请输入WebSocket SSL端口"
										:min="1" :max="65535" clearable />
								</el-form-item>
							</el-col>
						</el-row>
					</div>

					<!-- 云平台配置 -->
					<div class="config-section" v-if="state.ruleForm.instanceType === 'ALIYUN'">
						<div class="section-title">
							<el-icon>
								<Platform />
							</el-icon>
							阿里云配置
						</div>
						<el-row :gutter="24">
							<el-col :span="8" class="mb10">
								<el-form-item label="产品Key" prop="aliyunProductKey">
									<el-input v-model="state.ruleForm.aliyunProductKey" placeholder="请输入阿里云产品Key"
										maxlength="64" clearable />
								</el-form-item>
							</el-col>
							<el-col :span="8" class="mb10">
								<el-form-item label="区域ID" prop="aliyunRegionId">
									<el-input v-model="state.ruleForm.aliyunRegionId" placeholder="请输入阿里云区域ID"
										maxlength="32" clearable />
								</el-form-item>
							</el-col>
							<el-col :span="8" class="mb10">
								<el-form-item label="实例ID" prop="aliyunInstanceId">
									<el-input v-model="state.ruleForm.aliyunInstanceId" placeholder="请输入阿里云实例ID"
										maxlength="64" clearable />
								</el-form-item>
							</el-col>
							<el-col :span="12" class="mb10">
								<el-form-item label="设备ID前缀" prop="deviceIdPrefix">
									<el-input v-model="state.ruleForm.deviceIdPrefix" placeholder="请输入设备ID前缀"
										maxlength="32" clearable />
								</el-form-item>
							</el-col>
							<el-col :span="12" class="mb10">
								<el-form-item label="组ID前缀" prop="groupIdPrefix">
									<el-input v-model="state.ruleForm.groupIdPrefix" placeholder="请输入组ID前缀"
										maxlength="32" clearable />
								</el-form-item>
							</el-col>
							<el-col :span="12" class="mb10">
								<el-form-item label="阿里云兼容模式" prop="aliyunCompatible">
									<el-switch v-model="state.ruleForm.aliyunCompatible" />
								</el-form-item>
							</el-col>
						</el-row>
					</div>

					<!-- 其他配置 -->
					<div class="config-section">
						<div class="section-title">
							<el-icon>
								<Setting />
							</el-icon>
							其他配置
						</div>
						<el-row :gutter="24">
							<el-col :span="12" class="mb10">
								<el-form-item label="启用状态" prop="isEnabled">
									<el-switch v-model="state.ruleForm.isEnabled" />
								</el-form-item>
							</el-col>
							<el-col :span="24" class="mb10">
								<el-form-item label="扩展配置" prop="configJson">
									<el-input v-model="state.ruleForm.configJson" type="textarea" :rows="3"
										placeholder="请输入JSON格式的扩展配置" maxlength="1000" show-word-limit clearable />
								</el-form-item>
							</el-col>
							<el-col :span="24" class="mb10">
								<el-form-item label="备注" prop="remark">
									<el-input v-model="state.ruleForm.remark" type="textarea" :rows="2"
										placeholder="请输入备注信息" maxlength="500" show-word-limit clearable />
								</el-form-item>
							</el-col>
						</el-row>
					</div>
				</el-form>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closeDialog" size="large">取消</el-button>
					<el-button @click="testConnection" size="large" :loading="state.loading"
						v-if="state.ruleForm.serverHost && state.ruleForm.apiHost">
						测试连接
					</el-button>
					<el-button @click="submit" type="primary" size="large" :loading="state.loading">
						{{ state.ruleForm.id ? '更新实例' : '创建实例' }}
					</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
// 现代化对话框样式
.modern-dialog {
	:deep(.el-dialog) {
		border-radius: 12px;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
		overflow: hidden;
	}
	
	:deep(.el-dialog__header) {
		padding: 0;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-bottom: none;
	}
	
	:deep(.el-dialog__body) {
		padding: 0;
		max-height: 70vh;
		overflow-y: auto;
	}
	
	:deep(.el-dialog__footer) {
		padding: 20px 24px;
		background: #f8f9fa;
		border-top: 1px solid #e9ecef;
	}
}

// 对话框头部样式
.dialog-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20px 24px;
	color: white;
	
	.header-left {
		display: flex;
		align-items: center;
		gap: 12px;
		
		.header-icon {
			font-size: 20px;
		}
		
		.header-title {
			font-size: 18px;
			font-weight: 600;
		}
	}
	
	.header-right {
		:deep(.el-button) {
			background: rgba(255, 255, 255, 0.2);
			border: 1px solid rgba(255, 255, 255, 0.3);
			color: white;
			
			&:hover {
				background: rgba(255, 255, 255, 0.3);
			}
		}
	}
}

// 对话框内容区域
.dialog-content {
	padding: 24px;
}

// 实时监控面板
.realtime-panel {
	margin-bottom: 32px;
	padding: 20px;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	border-radius: 12px;
	border: 1px solid #e1e8ed;
	
	.panel-title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 8px;
		font-size: 16px;
		font-weight: 600;
		color: #2c3e50;
		margin-bottom: 16px;
		
		.el-icon {
			font-size: 18px;
			color: #3498db;
		}
		
		.title-left {
			display: flex;
			align-items: center;
			gap: 8px;
			
			.el-icon {
				font-size: 18px;
				color: #3498db;
			}
		}
		
		.connection-status {
			.el-tag {
				border-radius: 12px;
				font-size: 12px;
				padding: 4px 8px;
				
				.el-icon {
					font-size: 12px;
					color: inherit;
				}
			}
		}
	}
	
	.metrics-row {
		margin: 0 -8px;
	}
}

// 指标卡片
.metric-card {
	display: flex;
	align-items: center;
	padding: 16px;
	background: white;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	border: 1px solid #f0f0f0;
	transition: all 0.3s ease;
	height: 80px;
	
	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
	}
	
	.metric-icon {
		width: 48px;
		height: 48px;
		border-radius: 8px;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 12px;
		
		.el-icon {
			font-size: 24px;
			color: white;
		}
		
		&.connections {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		}
		
		&.messages {
			background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
		}
		
		&.bytes {
			background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
		}
		
		&.heartbeat {
			background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
		}
	}
	
	.metric-content {
		flex: 1;
		
		.metric-value {
			font-size: 18px;
			font-weight: 700;
			color: #2c3e50;
			line-height: 1.2;
		}
		
		.metric-label {
			font-size: 12px;
			color: #7f8c8d;
			margin-top: 2px;
		}
		
		.metric-rate {
			font-size: 11px;
			color: #27ae60;
			font-weight: 500;
			margin-top: 2px;
		}
	}
}

// 配置表单
.config-form {
	:deep(.el-select), :deep(.el-input-number) {
		width: 100%;
	}
	
	:deep(.el-form-item__label) {
		font-weight: 500;
		color: #2c3e50;
	}
	
	:deep(.el-input__wrapper) {
		border-radius: 6px;
		transition: all 0.3s ease;
		
		&:hover {
			box-shadow: 0 0 0 1px #409eff;
		}
	}
	
	:deep(.el-textarea__inner) {
		border-radius: 6px;
	}
}

// 配置区块
.config-section {
	margin-bottom: 32px;
	padding: 24px;
	background: white;
	border-radius: 8px;
	border: 1px solid #e9ecef;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
	
	.section-title {
		display: flex;
		align-items: center;
		gap: 8px;
		font-size: 16px;
		font-weight: 600;
		color: #2c3e50;
		margin-bottom: 20px;
		padding-bottom: 12px;
		border-bottom: 2px solid #f8f9fa;
		
		.el-icon {
			font-size: 18px;
			color: #3498db;
		}
	}
}

// 对话框底部
.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
	
	:deep(.el-button) {
		border-radius: 6px;
		padding: 12px 24px;
		font-weight: 500;
		transition: all 0.3s ease;
		
		&.el-button--primary {
			background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
			border: none;
			
			&:hover {
				background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
				transform: translateY(-1px);
				box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
			}
		}
		
		&:not(.el-button--primary) {
			&:hover {
				transform: translateY(-1px);
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
			}
		}
	}
}

// 响应式设计
@media (max-width: 768px) {
	.modern-dialog {
		:deep(.el-dialog) {
			width: 95% !important;
			margin: 5vh auto;
		}
	}
	
	.dialog-content {
		padding: 16px;
	}
	
	.realtime-panel {
		padding: 16px;
		
		.metrics-row {
			:deep(.el-col) {
				margin-bottom: 12px;
			}
		}
	}
	
	.config-section {
		padding: 16px;
		margin-bottom: 20px;
	}
	
	.metric-card {
		height: auto;
		min-height: 70px;
	}
}

// 动画效果
@keyframes pulse {
	0% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.05);
	}
	100% {
		transform: scale(1);
	}
}

.metric-card:hover .metric-icon {
	animation: pulse 0.6s ease-in-out;
}

// 滚动条样式
.dialog-content {
	&::-webkit-scrollbar {
		width: 6px;
	}
	
	&::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 3px;
	}
	
	&::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 3px;
		
		&:hover {
			background: #a8a8a8;
		}
	}
}

// 自动生成提示图标样式
.auto-generate-tip {
	color: #909399;
	font-size: 14px;
	cursor: help;
	transition: color 0.3s ease;
	
	&:hover {
		color: #409eff;
	}
}
</style>