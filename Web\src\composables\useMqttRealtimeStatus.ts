import { ref, reactive, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { mqttRealtimeClient } from '@/utils/signalr-client';
import { useEmqxRealtimeApi } from '@/api/emqxRealtime/emqxRealtime';
import type { MqttInstance } from '@/api/mqttInstance/mqttInstance';

// 连接状态映射
const CONNECTION_STATUS_MAP = {
  0: { text: '离线', color: 'danger', icon: 'CircleCloseFilled' },
  1: { text: '在线', color: 'success', icon: 'CircleCheckFilled' },
  2: { text: '连接中', color: 'warning', icon: 'Loading' },
  3: { text: '断开中', color: 'warning', icon: 'Loading' },
  4: { text: '错误', color: 'danger', icon: 'CircleCloseFilled' },
} as const;

/**
 * MQTT实例实时状态管理
 */
export function useMqttRealtimeStatus() {
  // API实例
  const emqxRealtimeApi = useEmqxRealtimeApi();
  
  // 连接状态映射
  const connectionStatus = reactive(new Map<number, any>());
  
  // SignalR连接状态
  const signalrConnected = ref(false);
  const signalrConnecting = ref(false);
  
  // 统计信息
  const statistics = ref({
    activeConnections: 0,
    activeInstances: 0,
    subscribedTopics: 0,
    cachedMessages: 0,
    totalMessages: 0,
    lastUpdated: new Date().toISOString()
  });
  
  // 订阅的实例ID列表
  const subscribedInstanceIds = ref<number[]>([]);
  
  // 心跳定时器
  let heartbeatTimer: NodeJS.Timeout | null = null;
  
  /**
   * 初始化SignalR连接
   */
  const initializeSignalR = async () => {
    if (signalrConnecting.value) {
      return;
    }
    
    signalrConnecting.value = true;
    
    try {
      // 设置事件监听器
      setupEventHandlers();
      
      // 连接到SignalR Hub
      const connected = await mqttRealtimeClient.connect();
      
      if (connected) {
        signalrConnected.value = true;
        ElMessage.success('实时订阅服务已连接');
        
        // 启动心跳检测
        startHeartbeat();
        
        // 获取初始统计信息
        await updateStatistics();
      } else {
        console.log('SignalR连接失败，使用降级模式');
      }
    } catch (error) {
      console.error('SignalR连接失败:', error);
      // 不显示错误消息，静默降级
    } finally {
      signalrConnecting.value = false;
    }
  };
  
  /**
   * 设置事件处理器
   */
  const setupEventHandlers = () => {
    // 连接状态事件
    mqttRealtimeClient.on('connected', () => {
      signalrConnected.value = true;
      console.log('SignalR连接成功');
    });
    
    mqttRealtimeClient.on('disconnected', () => {
      signalrConnected.value = false;
      stopHeartbeat();
      console.log('SignalR连接断开');
    });
    
    mqttRealtimeClient.on('reconnecting', () => {
      signalrConnecting.value = true;
      console.log('SignalR重连中...');
    });
    
    mqttRealtimeClient.on('reconnected', () => {
      signalrConnected.value = true;
      signalrConnecting.value = false;
      startHeartbeat();
      
      // 重新订阅实例状态
      if (subscribedInstanceIds.value.length > 0) {
        subscribeToInstanceUpdates(subscribedInstanceIds.value);
      }
      
      ElMessage.success('实时订阅服务已重连');
    });
    
    // 实例状态更新事件
    mqttRealtimeClient.on('instanceStatusUpdate', (statusUpdate: any) => {
      updateInstanceStatus(statusUpdate);
    });
    
    // 统计信息更新事件
    mqttRealtimeClient.on('statistics', (stats: any) => {
      statistics.value = { ...stats };
    });
    
    // 错误事件
    mqttRealtimeClient.on('error', (error: any) => {
      console.error('SignalR错误:', error);
    });
  };
  
  /**
   * 更新实例状态
   */
  const updateInstanceStatus = (statusUpdate: any) => {
    const { instanceId, isConnected, status, connectionInfo, statistics: instanceStats } = statusUpdate;
    
    connectionStatus.set(instanceId, {
      isConnected,
      status,
      checkTime: new Date().toLocaleTimeString(),
      connectionInfo,
      statistics: instanceStats
    });
    
    console.log(`实例 ${instanceId} 状态更新:`, statusUpdate);
  };
  
  /**
   * 订阅实例状态更新
   */
  const subscribeToInstanceUpdates = async (instanceIds: number[]) => {
    if (!signalrConnected.value) {
      console.log('SignalR未连接，跳过订阅实例状态');
      return;
    }
    
    try {
      await mqttRealtimeClient.subscribeToInstanceUpdates(instanceIds);
      subscribedInstanceIds.value = [...instanceIds];
      console.log('已订阅实例状态更新:', instanceIds);
    } catch (error) {
      console.error('订阅实例状态失败:', error);
      // 不显示错误消息，静默处理
    }
  };
  
  /**
   * 取消订阅实例状态更新
   */
  const unsubscribeFromInstanceUpdates = async () => {
    if (!signalrConnected.value) {
      return;
    }
    
    try {
      await mqttRealtimeClient.unsubscribeFromInstanceUpdates(subscribedInstanceIds.value);
      subscribedInstanceIds.value = [];
      console.log('已取消订阅实例状态更新');
    } catch (error) {
      console.error('取消订阅失败:', error);
    }
  };
  
  /**
   * 更新统计信息
   */
  const updateStatistics = async () => {
    try {
      if (signalrConnected.value) {
        const stats = await mqttRealtimeClient.getStatistics();
        statistics.value = { ...stats };
      } else {
        await updateStatisticsFromApi();
      }
    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  };

  /**
   * 从API获取统计信息
   */
  const updateStatisticsFromApi = async () => {
    try {
      const response = await emqxRealtimeApi.getRealtimeStatistics();
      if (response) {
        statistics.value = {
          activeConnections: response.activeConnections,
          activeInstances: response.activeInstances,
          subscribedTopics: response.subscribedTopics,
          cachedMessages: response.cachedMessages,
          totalMessages: response.totalMessages,
          lastUpdated: response.lastUpdated
        };
      }
    } catch (error) {
      console.error('从API获取统计信息失败:', error);
    }
  };
  
  /**
   * 启动心跳检测
   */
  const startHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer);
    }
    
    heartbeatTimer = setInterval(async () => {
      try {
        await mqttRealtimeClient.ping();
        // 定期更新统计信息
        await updateStatistics();
      } catch (error) {
        console.error('心跳检测失败:', error);
      }
    }, 30000); // 30秒心跳
  };
  
  /**
   * 停止心跳检测
   */
  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer);
      heartbeatTimer = null;
    }
  };
  
  /**
   * 获取实例连接状态
   */
  const getInstanceStatus = (instanceId: number) => {
    return connectionStatus.get(instanceId) || {
      isConnected: false,
      status: '未检测',
      checkTime: ''
    };
  };
  
  /**
   * 获取在线实例数量
   */
  const getOnlineInstancesCount = () => {
    let count = 0;
    connectionStatus.forEach(status => {
      if (status.isConnected) {
        count++;
      }
    });
    return count;
  };
  
  /**
   * 获取离线实例数量
   */
  const getOfflineInstancesCount = () => {
    let count = 0;
    connectionStatus.forEach(status => {
      if (!status.isConnected) {
        count++;
      }
    });
    return count;
  };
  
  /**
   * 手动刷新实例状态
   * @param instanceIds 实例ID列表
   */
  const refreshInstanceStatus = async (instanceIds: number[]) => {
    try {
      // 优先使用SignalR实时刷新
      if (signalrConnected.value) {
        await subscribeToInstanceUpdates(instanceIds);
        await updateStatistics();
        console.log('通过SignalR刷新实例状态:', instanceIds);
        ElMessage.success('实例状态刷新成功');
      } else {
        // 降级到API调用
        console.log('SignalR未连接，使用API刷新实例状态:', instanceIds);
        const response = await emqxRealtimeApi.refreshInstanceStatus({ instanceIds });
        if (response) {
          ElMessage.success('实例状态刷新成功');
        }
      }
    } catch (error) {
      console.error('刷新实例状态失败:', error);
      ElMessage.error('刷新实例状态失败');
      
      // 模拟刷新作为最后的降级方案
      instanceIds.forEach(id => {
        if (connectionStatus.has(id)) {
          const currentStatus = connectionStatus.get(id)!;
          updateInstanceStatus({
            instanceId: id,
            isConnected: currentStatus.isConnected,
            status: currentStatus.status,
            connectionInfo: currentStatus.connectionInfo,
            statistics: currentStatus.statistics
          });
        }
      });
    }
  };
  
  /**
   * 断开SignalR连接
   */
  const disconnectSignalR = async () => {
    stopHeartbeat();
    await mqttRealtimeClient.disconnect();
    signalrConnected.value = false;
    connectionStatus.clear();
    subscribedInstanceIds.value = [];
  };
  
  // 组件挂载时初始化
  onMounted(() => {
    initializeSignalR();
  });
  
  // 组件卸载时清理
  onUnmounted(() => {
    disconnectSignalR();
  });
  
  return {
    // 状态
    connectionStatus,
    signalrConnected,
    signalrConnecting,
    statistics,
    subscribedInstanceIds,
    
    // 方法
    initializeSignalR,
    subscribeToInstanceUpdates,
    unsubscribeFromInstanceUpdates,
    updateStatistics,
    updateStatisticsFromApi,
    getInstanceStatus,
    getOnlineInstancesCount,
    getOfflineInstancesCount,
    refreshInstanceStatus,
    disconnectSignalR
  };
}