/* tslint:disable */
 
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { MenuOutput } from './menu-output';
import { MenuTypeEnum } from './menu-type-enum';
import { StatusEnum } from './status-enum';
import { SysMenuMeta } from './sys-menu-meta';
 /**
 * 系统菜单返回结果
 *
 * @export
 * @interface MenuOutput
 */
export interface MenuOutput {

    /**
     * Id
     *
     * @type {number}
     * @memberof MenuOutput
     */
    id?: number;

    /**
     * 父Id
     *
     * @type {number}
     * @memberof MenuOutput
     */
    pid?: number;

    /**
     * @type {MenuTypeEnum}
     * @memberof MenuOutput
     */
    type?: MenuTypeEnum;

    /**
     * 名称
     *
     * @type {string}
     * @memberof MenuOutput
     */
    name?: string | null;

    /**
     * 路由地址
     *
     * @type {string}
     * @memberof MenuOutput
     */
    path?: string | null;

    /**
     * 组件路径
     *
     * @type {string}
     * @memberof MenuOutput
     */
    component?: string | null;

    /**
     * 权限标识
     *
     * @type {string}
     * @memberof MenuOutput
     */
    permission?: string | null;

    /**
     * 重定向
     *
     * @type {string}
     * @memberof MenuOutput
     */
    redirect?: string | null;

    /**
     * 排序
     *
     * @type {number}
     * @memberof MenuOutput
     */
    orderNo?: number;

    /**
     * @type {StatusEnum}
     * @memberof MenuOutput
     */
    status?: StatusEnum;

    /**
     * 备注
     *
     * @type {string}
     * @memberof MenuOutput
     */
    remark?: string | null;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof MenuOutput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof MenuOutput
     */
    updateTime?: Date;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof MenuOutput
     */
    createUserName?: string | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof MenuOutput
     */
    updateUserName?: string | null;

    /**
     * @type {SysMenuMeta}
     * @memberof MenuOutput
     */
    meta?: SysMenuMeta;

    /**
     * 菜单子项
     *
     * @type {Array<MenuOutput>}
     * @memberof MenuOutput
     */
    children?: Array<MenuOutput> | null;
}
