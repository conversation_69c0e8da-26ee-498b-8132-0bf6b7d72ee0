/* tslint:disable */
/* eslint-disable */
/**
 * DingTalk
 * 集成钉钉开放平台<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */
import globalAxios, { AxiosResponse, AxiosInstance, AxiosRequestConfig } from 'axios';
import { Configuration } from '../configuration';
// Some imports not used depending on template conditions
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, RequestArgs, BaseAPI, RequiredError } from '../base';
import { AdminResultDingTalkSendInteractiveCardsOutput } from '../models';
import { AdminResultEmployeeQueryOnJobResponse } from '../models';
import { AdminResultGetAccessTokenResponse } from '../models';
import { AdminResultRosterListsQueryResponse } from '../models';
import { DingTalkSendInteractiveCardsInput } from '../models';
/**
 * DingTalkApi - axios parameter creator
 * @export
 */
export const DingTalkApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取在职员工列表 🔖
         * @param {string} accessToken 
         * @param {number} size 
         * @param {number} offset 
         * @param {Array<string>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDingTalkDingTalkCurrentEmployeesListAccessTokenSizeOffsetPost: async (accessToken: string, size: number, offset: number, body?: Array<string>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'accessToken' is not null or undefined
            if (accessToken === null || accessToken === undefined) {
                throw new RequiredError('accessToken','Required parameter accessToken was null or undefined when calling apiDingTalkDingTalkCurrentEmployeesListAccessTokenSizeOffsetPost.');
            }
            // verify required parameter 'size' is not null or undefined
            if (size === null || size === undefined) {
                throw new RequiredError('size','Required parameter size was null or undefined when calling apiDingTalkDingTalkCurrentEmployeesListAccessTokenSizeOffsetPost.');
            }
            // verify required parameter 'offset' is not null or undefined
            if (offset === null || offset === undefined) {
                throw new RequiredError('offset','Required parameter offset was null or undefined when calling apiDingTalkDingTalkCurrentEmployeesListAccessTokenSizeOffsetPost.');
            }
            const localVarPath = `/api/dingTalk/dingTalkCurrentEmployeesList/{accessToken}/{size}/{offset}`
                .replace(`{${"accessToken"}}`, encodeURIComponent(String(accessToken)))
                .replace(`{${"size"}}`, encodeURIComponent(String(size)))
                .replace(`{${"offset"}}`, encodeURIComponent(String(offset)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取员工花名册字段信息 🔖
         * @param {string} accessToken 
         * @param {number} appAgentId 
         * @param {Array<string>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenAppAgentIdPost: async (accessToken: string, appAgentId: number, body?: Array<string>, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'accessToken' is not null or undefined
            if (accessToken === null || accessToken === undefined) {
                throw new RequiredError('accessToken','Required parameter accessToken was null or undefined when calling apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenAppAgentIdPost.');
            }
            // verify required parameter 'appAgentId' is not null or undefined
            if (appAgentId === null || appAgentId === undefined) {
                throw new RequiredError('appAgentId','Required parameter appAgentId was null or undefined when calling apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenAppAgentIdPost.');
            }
            const localVarPath = `/api/dingTalk/dingTalkCurrentEmployeesRosterList/{accessToken}/{appAgentId}`
                .replace(`{${"accessToken"}}`, encodeURIComponent(String(accessToken)))
                .replace(`{${"appAgentId"}}`, encodeURIComponent(String(appAgentId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 发送钉钉互动卡片 🔖
         * @param {string} accessToken 
         * @param {DingTalkSendInteractiveCardsInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDingTalkDingTalkSendInteractiveCardsAccessTokenPost: async (accessToken: string, body?: DingTalkSendInteractiveCardsInput, options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'accessToken' is not null or undefined
            if (accessToken === null || accessToken === undefined) {
                throw new RequiredError('accessToken','Required parameter accessToken was null or undefined when calling apiDingTalkDingTalkSendInteractiveCardsAccessTokenPost.');
            }
            const localVarPath = `/api/dingTalk/dingTalkSendInteractiveCards/{accessToken}`
                .replace(`{${"accessToken"}}`, encodeURIComponent(String(accessToken)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json-patch+json';

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            const needsSerialization = (typeof body !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.data =  needsSerialization ? JSON.stringify(body !== undefined ? body : {}) : (body || "");

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取企业内部应用的access_token
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiDingTalkDingTalkTokenGet: async (options: AxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/dingTalk/dingTalkToken`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, 'https://example.com');
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }
            const localVarRequestOptions :AxiosRequestConfig = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication Bearer required
            // http bearer authentication required
            if (configuration && configuration.accessToken) {
                const accessToken = typeof configuration.accessToken === 'function'
                    ? await configuration.accessToken()
                    : await configuration.accessToken;
                localVarHeaderParameter["Authorization"] = "Bearer " + accessToken;
            }

            const query = new URLSearchParams(localVarUrlObj.search);
            for (const key in localVarQueryParameter) {
                query.set(key, localVarQueryParameter[key]);
            }
            for (const key in options.params) {
                query.set(key, options.params[key]);
            }
            localVarUrlObj.search = (new URLSearchParams(query)).toString();
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: localVarUrlObj.pathname + localVarUrlObj.search + localVarUrlObj.hash,
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DingTalkApi - functional programming interface
 * @export
 */
export const DingTalkApiFp = function(configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取在职员工列表 🔖
         * @param {string} accessToken 
         * @param {number} size 
         * @param {number} offset 
         * @param {Array<string>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkCurrentEmployeesListAccessTokenSizeOffsetPost(accessToken: string, size: number, offset: number, body?: Array<string>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultEmployeeQueryOnJobResponse>>> {
            const localVarAxiosArgs = await DingTalkApiAxiosParamCreator(configuration).apiDingTalkDingTalkCurrentEmployeesListAccessTokenSizeOffsetPost(accessToken, size, offset, body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取员工花名册字段信息 🔖
         * @param {string} accessToken 
         * @param {number} appAgentId 
         * @param {Array<string>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenAppAgentIdPost(accessToken: string, appAgentId: number, body?: Array<string>, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultRosterListsQueryResponse>>> {
            const localVarAxiosArgs = await DingTalkApiAxiosParamCreator(configuration).apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenAppAgentIdPost(accessToken, appAgentId, body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 发送钉钉互动卡片 🔖
         * @param {string} accessToken 
         * @param {DingTalkSendInteractiveCardsInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkSendInteractiveCardsAccessTokenPost(accessToken: string, body?: DingTalkSendInteractiveCardsInput, options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultDingTalkSendInteractiveCardsOutput>>> {
            const localVarAxiosArgs = await DingTalkApiAxiosParamCreator(configuration).apiDingTalkDingTalkSendInteractiveCardsAccessTokenPost(accessToken, body, options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
        /**
         * 
         * @summary 获取企业内部应用的access_token
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkTokenGet(options?: AxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => Promise<AxiosResponse<AdminResultGetAccessTokenResponse>>> {
            const localVarAxiosArgs = await DingTalkApiAxiosParamCreator(configuration).apiDingTalkDingTalkTokenGet(options);
            return (axios: AxiosInstance = globalAxios, basePath: string = BASE_PATH) => {
                const axiosRequestArgs :AxiosRequestConfig = {...localVarAxiosArgs.options, url: basePath + localVarAxiosArgs.url};
                return axios.request(axiosRequestArgs);
            };
        },
    }
};

/**
 * DingTalkApi - factory interface
 * @export
 */
export const DingTalkApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    return {
        /**
         * 
         * @summary 获取在职员工列表 🔖
         * @param {string} accessToken 
         * @param {number} size 
         * @param {number} offset 
         * @param {Array<string>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkCurrentEmployeesListAccessTokenSizeOffsetPost(accessToken: string, size: number, offset: number, body?: Array<string>, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultEmployeeQueryOnJobResponse>> {
            return DingTalkApiFp(configuration).apiDingTalkDingTalkCurrentEmployeesListAccessTokenSizeOffsetPost(accessToken, size, offset, body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取员工花名册字段信息 🔖
         * @param {string} accessToken 
         * @param {number} appAgentId 
         * @param {Array<string>} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenAppAgentIdPost(accessToken: string, appAgentId: number, body?: Array<string>, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultRosterListsQueryResponse>> {
            return DingTalkApiFp(configuration).apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenAppAgentIdPost(accessToken, appAgentId, body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 发送钉钉互动卡片 🔖
         * @param {string} accessToken 
         * @param {DingTalkSendInteractiveCardsInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkSendInteractiveCardsAccessTokenPost(accessToken: string, body?: DingTalkSendInteractiveCardsInput, options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultDingTalkSendInteractiveCardsOutput>> {
            return DingTalkApiFp(configuration).apiDingTalkDingTalkSendInteractiveCardsAccessTokenPost(accessToken, body, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取企业内部应用的access_token
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async apiDingTalkDingTalkTokenGet(options?: AxiosRequestConfig): Promise<AxiosResponse<AdminResultGetAccessTokenResponse>> {
            return DingTalkApiFp(configuration).apiDingTalkDingTalkTokenGet(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * DingTalkApi - object-oriented interface
 * @export
 * @class DingTalkApi
 * @extends {BaseAPI}
 */
export class DingTalkApi extends BaseAPI {
    /**
     * 
     * @summary 获取在职员工列表 🔖
     * @param {string} accessToken 
     * @param {number} size 
     * @param {number} offset 
     * @param {Array<string>} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DingTalkApi
     */
    public async apiDingTalkDingTalkCurrentEmployeesListAccessTokenSizeOffsetPost(accessToken: string, size: number, offset: number, body?: Array<string>, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultEmployeeQueryOnJobResponse>> {
        return DingTalkApiFp(this.configuration).apiDingTalkDingTalkCurrentEmployeesListAccessTokenSizeOffsetPost(accessToken, size, offset, body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取员工花名册字段信息 🔖
     * @param {string} accessToken 
     * @param {number} appAgentId 
     * @param {Array<string>} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DingTalkApi
     */
    public async apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenAppAgentIdPost(accessToken: string, appAgentId: number, body?: Array<string>, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultRosterListsQueryResponse>> {
        return DingTalkApiFp(this.configuration).apiDingTalkDingTalkCurrentEmployeesRosterListAccessTokenAppAgentIdPost(accessToken, appAgentId, body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 发送钉钉互动卡片 🔖
     * @param {string} accessToken 
     * @param {DingTalkSendInteractiveCardsInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DingTalkApi
     */
    public async apiDingTalkDingTalkSendInteractiveCardsAccessTokenPost(accessToken: string, body?: DingTalkSendInteractiveCardsInput, options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultDingTalkSendInteractiveCardsOutput>> {
        return DingTalkApiFp(this.configuration).apiDingTalkDingTalkSendInteractiveCardsAccessTokenPost(accessToken, body, options).then((request) => request(this.axios, this.basePath));
    }
    /**
     * 
     * @summary 获取企业内部应用的access_token
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DingTalkApi
     */
    public async apiDingTalkDingTalkTokenGet(options?: AxiosRequestConfig) : Promise<AxiosResponse<AdminResultGetAccessTokenResponse>> {
        return DingTalkApiFp(this.configuration).apiDingTalkDingTalkTokenGet(options).then((request) => request(this.axios, this.basePath));
    }
}
