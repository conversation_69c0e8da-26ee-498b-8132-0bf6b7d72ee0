﻿<script lang="ts" name="mqttTopic" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useMqttTopicApi } from '/@/api/mqtt_topic/mqttTopic';

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const mqttTopicApi = useMqttTopicApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
});

// 自行添加其他规则
const rules = ref<FormRules>({
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? {  };
	state.ruleForm = row.id ? await mqttTopicApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));
	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await mqttTopicApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="mqttTopic-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="实例ID" prop="instanceId">
							<el-input v-model="state.ruleForm.instanceId" placeholder="请输入实例ID" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="主题名称" prop="topicName">
							<el-input v-model="state.ruleForm.topicName" placeholder="请输入主题名称" maxlength="256" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="主题类型" prop="topicType">
							<el-input v-model="state.ruleForm.topicType" placeholder="请输入主题类型" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="QoS等级（0,1,2）" prop="qosLevel">
							<el-switch v-model="state.ruleForm.qosLevel" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否保留消息（0:否，1:是）" prop="retainMessage">
							<el-switch v-model="state.ruleForm.retainMessage" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="订阅数量" prop="subscriptionCount">
							<el-input-number v-model="state.ruleForm.subscriptionCount" placeholder="请输入订阅数量" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="消息数量" prop="messageCount">
							<el-input v-model="state.ruleForm.messageCount" placeholder="请输入消息数量" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="字节数量" prop="byteCount">
							<el-input v-model="state.ruleForm.byteCount" placeholder="请输入字节数量" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最后消息时间" prop="lastMessageTime">
							<el-date-picker v-model="state.ruleForm.lastMessageTime" type="date" placeholder="最后消息时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最后消息内容" prop="lastMessageContent">
							<el-input v-model="state.ruleForm.lastMessageContent" placeholder="请输入最后消息内容" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最大消息大小" prop="maxMessageSize">
							<el-input-number v-model="state.ruleForm.maxMessageSize" placeholder="请输入最大消息大小" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="消息过期时间（秒）" prop="messageExpiry">
							<el-input-number v-model="state.ruleForm.messageExpiry" placeholder="请输入消息过期时间（秒）" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="允许发布（0:否，1:是）" prop="allowPublish">
							<el-switch v-model="state.ruleForm.allowPublish" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="允许订阅（0:否，1:是）" prop="allowSubscribe">
							<el-switch v-model="state.ruleForm.allowSubscribe" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="发布权限（JSON）" prop="publishPermissions">
							<el-input v-model="state.ruleForm.publishPermissions" placeholder="请输入发布权限（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="订阅权限（JSON）" prop="subscribePermissions">
							<el-input v-model="state.ruleForm.subscribePermissions" placeholder="请输入订阅权限（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否保留（0:否，1:是）" prop="isRetained">
							<el-switch v-model="state.ruleForm.isRetained" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="描述" prop="description">
							<el-input v-model="state.ruleForm.description" placeholder="请输入描述" maxlength="500" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="主题标签（JSON）" prop="topicTags">
							<el-input v-model="state.ruleForm.topicTags" placeholder="请输入主题标签（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否启用（0:否，1:是）" prop="isEnabled">
							<el-switch v-model="state.ruleForm.isEnabled" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否系统主题（0:否，1:是）" prop="isSystemTopic">
							<el-switch v-model="state.ruleForm.isSystemTopic" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="监控配置（JSON）" prop="monitorConfig">
							<el-input v-model="state.ruleForm.monitorConfig" placeholder="请输入监控配置（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="告警规则（JSON）" prop="alertRules">
							<el-input v-model="state.ruleForm.alertRules" placeholder="请输入告警规则（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="转发规则（JSON）" prop="forwardRules">
							<el-input v-model="state.ruleForm.forwardRules" placeholder="请输入转发规则（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="扩展属性（JSON）" prop="extendedProperties">
							<el-input v-model="state.ruleForm.extendedProperties" placeholder="请输入扩展属性（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="备注" prop="remark">
							<el-input v-model="state.ruleForm.remark" placeholder="请输入备注" maxlength="500" show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>