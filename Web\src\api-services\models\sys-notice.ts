/* tslint:disable */
 
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { NoticeStatusEnum } from './notice-status-enum';
import { NoticeTypeEnum } from './notice-type-enum';
 /**
 * 系统通知公告表
 *
 * @export
 * @interface SysNotice
 */
export interface SysNotice {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof SysNotice
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof SysNotice
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof SysNotice
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof SysNotice
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof SysNotice
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof SysNotice
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof SysNotice
     */
    updateUserName?: string | null;

    /**
     * 标题
     *
     * @type {string}
     * @memberof SysNotice
     */
    title: string;

    /**
     * 内容
     *
     * @type {string}
     * @memberof SysNotice
     */
    content: string;

    /**
     * @type {NoticeTypeEnum}
     * @memberof SysNotice
     */
    type?: NoticeTypeEnum;

    /**
     * 发布人Id
     *
     * @type {number}
     * @memberof SysNotice
     */
    publicUserId?: number;

    /**
     * 发布人姓名
     *
     * @type {string}
     * @memberof SysNotice
     */
    publicUserName?: string | null;

    /**
     * 发布机构Id
     *
     * @type {number}
     * @memberof SysNotice
     */
    publicOrgId?: number;

    /**
     * 发布机构名称
     *
     * @type {string}
     * @memberof SysNotice
     */
    publicOrgName?: string | null;

    /**
     * 发布时间
     *
     * @type {Date}
     * @memberof SysNotice
     */
    publicTime?: Date | null;

    /**
     * 撤回时间
     *
     * @type {Date}
     * @memberof SysNotice
     */
    cancelTime?: Date | null;

    /**
     * @type {NoticeStatusEnum}
     * @memberof SysNotice
     */
    status?: NoticeStatusEnum;
}
