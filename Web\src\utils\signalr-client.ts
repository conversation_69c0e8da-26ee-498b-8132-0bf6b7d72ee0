import * as signalR from '@microsoft/signalr';
import { ElMessage } from 'element-plus';

/**
 * MQTT实时订阅SignalR客户端
 */
export class MqttRealtimeClient {
  private connection: signalR.HubConnection | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private eventHandlers: Map<string, Function[]> = new Map();
  private enabled = false; // 默认禁用SignalR
  private subscribedInstances: Set<number> = new Set(); // 已订阅的实例ID

  constructor(private hubUrl: string = import.meta.env.VITE_SIGNALR_HUB_URL || '/hubs/realtime-subscription') {
    // 检查是否启用SignalR
    this.enabled = this.checkSignalREnabled();
    if (this.enabled) {
      this.initializeConnection();
    }
  }

  /**
   * 检查是否启用SignalR
   */
  private checkSignalREnabled(): boolean {
    // 检查环境变量或配置文件
    const enableSignalR = import.meta.env.VITE_ENABLE_SIGNALR;
    if (enableSignalR !== undefined) {
      return enableSignalR === 'true' || enableSignalR === true;
    }
    
    // 默认启用SignalR实时功能
    return true;
  }

  /**
   * 初始化SignalR连接
   */
  private initializeConnection() {
    this.connection = new signalR.HubConnectionBuilder()
      .withUrl(this.hubUrl)
      .withAutomaticReconnect({
        nextRetryDelayInMilliseconds: (retryContext) => {
          if (retryContext.previousRetryCount < 3) {
            return 1000; // 1秒
          } else if (retryContext.previousRetryCount < 6) {
            return 5000; // 5秒
          } else {
            return 30000; // 30秒
          }
        }
      })
      .configureLogging(signalR.LogLevel.Information)
      .build();

    this.setupEventHandlers();
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.connection) return;

    // 连接状态变化
    this.connection.onclose((error) => {
      console.log('SignalR连接关闭:', error);
      this.isConnected = false;
      this.emit('disconnected', error);
    });

    this.connection.onreconnecting((error) => {
      console.log('SignalR重连中:', error);
      this.isConnected = false;
      this.emit('reconnecting', error);
    });

    this.connection.onreconnected((connectionId) => {
      console.log('SignalR重连成功:', connectionId);
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.emit('reconnected', connectionId);
      
      // 重连后重新订阅之前的实例
      if (this.subscribedInstances.size > 0) {
        const instanceIds = Array.from(this.subscribedInstances);
        console.log('重连后重新订阅实例:', instanceIds);
        this.subscribeToInstanceUpdates(instanceIds).catch(error => {
          console.error('重连后重新订阅失败:', error);
        });
      }
    });

    // 服务器推送的实时消息
    this.connection.on('ReceiveMessage', (message) => {
      if (import.meta.env.VITE_DEBUG_MODE === 'true') {
        console.log('收到实时消息:', message);
      }
      this.emit('message', message);
    });

    // 实例状态更新
    this.connection.on('InstanceStatusUpdate', (data) => {
      if (import.meta.env.VITE_DEBUG_MODE === 'true') {
        console.log('实例状态更新:', data);
      }
      this.emit('instanceStatusUpdate', data);
    });

    // 统计信息更新
    this.connection.on('StatisticsUpdate', (data) => {
      if (import.meta.env.VITE_DEBUG_MODE === 'true') {
        console.log('统计信息更新:', data);
      }
      this.emit('statisticsUpdate', data);
    });

    // 订阅成功
    this.connection.on('SubscriptionSuccess', (data) => {
      console.log('订阅成功:', data);
      if (data.instanceIds) {
        data.instanceIds.forEach((id: number) => this.subscribedInstances.add(id));
      }
      this.emit('subscriptionSuccess', data);
    });

    // 订阅失败
    this.connection.on('SubscriptionFailed', (data) => {
      console.warn('订阅失败:', data);
      this.emit('subscriptionFailed', data);
    });

    // 错误处理
    this.connection.on('Error', (error) => {
      console.error('SignalR服务器错误:', error);
      this.emit('error', error);
    });

    // 心跳响应
    this.connection.on('HeartbeatResponse', (data) => {
      if (import.meta.env.VITE_DEBUG_MODE === 'true') {
        console.log('收到心跳响应:', data);
      }
      this.emit('heartbeatResponse', data);
    });
  }

  /**
   * 连接到SignalR Hub
   */
  async connect(): Promise<boolean> {
    if (!this.enabled) {
      console.log('SignalR已禁用，跳过连接');
      return false;
    }

    if (this.connection?.state === signalR.HubConnectionState.Connected) {
      console.log('SignalR已连接');
      return true;
    }

    try {
      // 构建连接URL，支持相对路径和绝对路径
      const baseUrl = import.meta.env.VITE_API_URL || window.location.origin;
      const fullHubUrl = this.hubUrl.startsWith('http') ? this.hubUrl : `${baseUrl}${this.hubUrl}`;
      
      console.log(`正在连接SignalR Hub: ${fullHubUrl}`);
      
      this.connection = new signalR.HubConnectionBuilder()
        .withUrl(fullHubUrl, {
          // 添加认证头
          accessTokenFactory: () => {
            // 这里可以返回JWT token
            const token = localStorage.getItem('token');
            return token || '';
          }
        })
        .withAutomaticReconnect({
          nextRetryDelayInMilliseconds: (retryContext) => {
            // 指数退避策略，最大延迟30秒
            const delay = Math.min(1000 * Math.pow(2, retryContext.previousRetryCount), 30000);
            console.log(`SignalR重连延迟: ${delay}ms (第${retryContext.previousRetryCount + 1}次重试)`);
            return delay;
          }
        })
        .configureLogging(import.meta.env.VITE_DEBUG_MODE === 'true' ? signalR.LogLevel.Debug : signalR.LogLevel.Warning)
        .build();

      this.setupEventHandlers();
      await this.connection.start();
      this.isConnected = true;
      this.reconnectAttempts = 0;
      console.log('SignalR连接成功');
      this.emit('connected');
      return true;
    } catch (error) {
      console.error('SignalR连接失败:', error);
      this.reconnectAttempts++;
      this.emit('error', error);
      
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        const retryDelay = Math.min(3000 * this.reconnectAttempts, 15000);
        console.log(`将在${retryDelay}ms后重试连接 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        setTimeout(() => this.connect(), retryDelay);
      } else {
        console.warn('SignalR连接失败次数过多，将使用降级模式');
      }
      return false;
    }
  }

  /**
   * 断开连接
   */
  async disconnect(): Promise<void> {
    if (this.connection && this.isConnected) {
      await this.connection.stop();
      this.isConnected = false;
      console.log('SignalR连接已断开');
    }
  }

  /**
   * 订阅实例更新
   * @param instanceIds 实例ID数组
   */
  async subscribeToInstanceUpdates(instanceIds: number[]): Promise<void> {
    if (!this.enabled) {
      console.log('SignalR已禁用，跳过订阅');
      return;
    }

    if (!this.isConnected || !this.connection) {
      console.warn('SignalR未连接，无法订阅实例更新');
      throw new Error('SignalR未连接');
    }

    try {
      await this.connection.invoke('Subscribe', { instanceIds });
      // 添加到已订阅集合
      instanceIds.forEach(id => this.subscribedInstances.add(id));
      console.log('订阅实例更新成功:', instanceIds);
    } catch (error) {
      console.error('订阅实例更新失败:', error);
      throw error;
    }
  }

  /**
   * 取消订阅实例更新
   * @param instanceIds 实例ID数组
   */
  async unsubscribeFromInstanceUpdates(instanceIds: number[]): Promise<void> {
    if (!this.enabled) {
      console.log('SignalR已禁用，跳过取消订阅');
      return;
    }

    if (!this.isConnected || !this.connection) {
      console.warn('SignalR未连接，无法取消订阅');
      // 即使未连接也要从本地集合中移除
      instanceIds.forEach(id => this.subscribedInstances.delete(id));
      return;
    }

    try {
      await this.connection.invoke('Unsubscribe');
      // 从已订阅集合中移除
      instanceIds.forEach(id => this.subscribedInstances.delete(id));
      console.log('取消订阅实例更新成功:', instanceIds);
    } catch (error) {
      console.error('取消订阅实例更新失败:', error);
      // 即使失败也要从本地集合中移除
      instanceIds.forEach(id => this.subscribedInstances.delete(id));
      throw error;
    }
  }

  /**
   * 获取实时统计信息
   */
  async getStatistics(): Promise<any> {
    if (!this.enabled || !this.isConnected || !this.connection) {
      console.log('SignalR未启用或未连接，返回空统计信息');
      return null;
    }

    try {
      return await this.connection.invoke('GetStatistics');
    } catch (error) {
      console.error('获取统计信息失败:', error);
      return null;
    }
  }

  /**
   * 心跳检测
   */
  async ping(): Promise<string> {
    if (!this.enabled || !this.isConnected || !this.connection) {
      console.log('SignalR未启用或未连接，跳过心跳检测');
      return 'disabled';
    }

    try {
      return await this.connection.invoke('Ping');
    } catch (error) {
      console.error('心跳检测失败:', error);
      return 'error';
    }
  }

  /**
   * 注册事件监听器
   */
  on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  /**
   * 移除事件监听器
   */
  off(event: string, handler?: Function): void {
    if (!this.eventHandlers.has(event)) {
      return;
    }

    if (handler) {
      const handlers = this.eventHandlers.get(event)!;
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    } else {
      this.eventHandlers.delete(event);
    }
  }

  /**
   * 触发事件
   */
  private emit(event: string, data?: any): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`事件处理器执行失败 [${event}]:`, error);
        }
      });
    }
  }

  /**
   * 获取连接状态
   */
  get connected(): boolean {
    return this.isConnected;
  }

  /**
   * 获取连接对象
   */
  get connectionState(): signalR.HubConnectionState {
    return this.connection?.state || signalR.HubConnectionState.Disconnected;
  }
}

// 创建全局实例
export const mqttRealtimeClient = new MqttRealtimeClient();