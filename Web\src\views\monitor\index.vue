<template>
  <div class="monitor-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon><Monitor /></el-icon>
        实时监控
      </h1>
      <div class="header-actions">
        <el-button type="primary" :icon="Refresh" @click="refreshAllData" :loading="refreshing">
          刷新数据
        </el-button>
        <el-button type="success" :icon="Download" @click="exportData">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="16" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="stat-card online-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ monitorData.onlineInstances }}</div>
              <div class="stat-label">在线实例</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="stat-card clients-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ monitorData.totalClients }}</div>
              <div class="stat-label">连接客户端</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="stat-card messages-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><ChatDotRound /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ formatNumber(monitorData.totalMessages) }}</div>
              <div class="stat-label">消息总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6">
        <el-card class="stat-card topics-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Collection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ monitorData.activeTopic }}</div>
              <div class="stat-label">活跃主题</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="16" class="charts-row">
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>消息流量趋势</span>
              <el-select v-model="messageChartPeriod" size="small" style="width: 120px">
                <el-option label="最近1小时" value="1h" />
                <el-option label="最近6小时" value="6h" />
                <el-option label="最近24小时" value="24h" />
                <el-option label="最近7天" value="7d" />
              </el-select>
            </div>
          </template>
          <div ref="messageChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>客户端连接趋势</span>
              <el-select v-model="clientChartPeriod" size="small" style="width: 120px">
                <el-option label="最近1小时" value="1h" />
                <el-option label="最近6小时" value="6h" />
                <el-option label="最近24小时" value="24h" />
                <el-option label="最近7天" value="7d" />
              </el-select>
            </div>
          </template>
          <div ref="clientChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实例状态监控 -->
    <el-row :gutter="16" class="instances-row">
      <el-col :span="24">
        <el-card class="instances-card">
          <template #header>
            <div class="card-header">
              <span>MQTT实例状态</span>
              <div class="header-actions">
                <el-button size="small" :icon="Refresh" @click="refreshInstances" :loading="instancesLoading">
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          <el-table :data="instancesList" v-loading="instancesLoading" stripe>
            <el-table-column prop="instanceName" label="实例名称" min-width="150">
              <template #default="{ row }">
                <div class="instance-name">
                  <el-icon :class="getStatusIcon(row.status)"><component :is="getStatusIcon(row.status)" /></el-icon>
                  {{ row.instanceName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="host" label="主机地址" min-width="120" />
            <el-table-column prop="port" label="端口" width="80" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="clientCount" label="客户端数" width="100" />
            <el-table-column prop="messageRate" label="消息速率" width="120">
              <template #default="{ row }">
                {{ row.messageRate }}/s
              </template>
            </el-table-column>
            <el-table-column prop="uptime" label="运行时间" width="120" />
            <el-table-column prop="lastUpdate" label="最后更新" width="160">
              <template #default="{ row }">
                {{ formatTime(row.lastUpdate) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button size="small" type="primary" @click="viewInstanceDetail(row)">
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 活跃主题监控 -->
    <el-row :gutter="16" class="topics-row">
      <el-col :span="24">
        <el-card class="topics-card">
          <template #header>
            <div class="card-header">
              <span>活跃主题监控</span>
              <div class="header-actions">
                <el-input
                  v-model="topicFilter"
                  placeholder="搜索主题"
                  size="small"
                  style="width: 200px; margin-right: 8px"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <el-button size="small" :icon="Refresh" @click="refreshTopics" :loading="topicsLoading">
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          <el-table :data="filteredTopics" v-loading="topicsLoading" stripe max-height="400">
            <el-table-column prop="topicName" label="主题名称" min-width="200">
              <template #default="{ row }">
                <div class="topic-name">
                  <el-icon><Collection /></el-icon>
                  {{ row.topicName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="subscriberCount" label="订阅者" width="100" />
            <el-table-column prop="messageCount" label="消息数" width="100">
              <template #default="{ row }">
                {{ formatNumber(row.messageCount) }}
              </template>
            </el-table-column>
            <el-table-column prop="messageRate" label="消息速率" width="120">
              <template #default="{ row }">
                {{ row.messageRate }}/s
              </template>
            </el-table-column>
            <el-table-column prop="lastMessage" label="最后消息" width="160">
              <template #default="{ row }">
                {{ formatTime(row.lastMessage) }}
              </template>
            </el-table-column>
            <el-table-column prop="qos" label="QoS" width="80">
              <template #default="{ row }">
                <el-tag size="small" :type="getQosType(row.qos)">
                  {{ row.qos }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button size="small" type="primary" @click="viewTopicDetail(row)">
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实例详情对话框 -->
    <el-dialog v-model="instanceDetailVisible" title="实例详情" width="800px">
      <div v-if="selectedInstance" class="instance-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="实例名称">{{ selectedInstance.instanceName }}</el-descriptions-item>
          <el-descriptions-item label="主机地址">{{ selectedInstance.host }}:{{ selectedInstance.port }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedInstance.status)">
              {{ getStatusText(selectedInstance.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="运行时间">{{ selectedInstance.uptime }}</el-descriptions-item>
          <el-descriptions-item label="客户端数量">{{ selectedInstance.clientCount }}</el-descriptions-item>
          <el-descriptions-item label="消息速率">{{ selectedInstance.messageRate }}/s</el-descriptions-item>
          <el-descriptions-item label="CPU使用率">{{ selectedInstance.cpuUsage }}%</el-descriptions-item>
          <el-descriptions-item label="内存使用率">{{ selectedInstance.memoryUsage }}%</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 主题详情对话框 -->
    <el-dialog v-model="topicDetailVisible" title="主题详情" width="800px">
      <div v-if="selectedTopic" class="topic-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="主题名称">{{ selectedTopic.topicName }}</el-descriptions-item>
          <el-descriptions-item label="订阅者数量">{{ selectedTopic.subscriberCount }}</el-descriptions-item>
          <el-descriptions-item label="消息总数">{{ formatNumber(selectedTopic.messageCount) }}</el-descriptions-item>
          <el-descriptions-item label="消息速率">{{ selectedTopic.messageRate }}/s</el-descriptions-item>
          <el-descriptions-item label="QoS级别">
            <el-tag :type="getQosType(selectedTopic.qos)">{{ selectedTopic.qos }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="最后消息时间">{{ formatTime(selectedTopic.lastMessage) }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="topic-subscribers" style="margin-top: 20px">
          <h4>订阅者列表</h4>
          <el-table :data="selectedTopic.subscribers" stripe max-height="300">
            <el-table-column prop="clientId" label="客户端ID" />
            <el-table-column prop="subscribeTime" label="订阅时间">
              <template #default="{ row }">
                {{ formatTime(row.subscribeTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="qos" label="QoS">
              <template #default="{ row }">
                <el-tag size="small" :type="getQosType(row.qos)">{{ row.qos }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Monitor,
  Refresh,
  Download,
  Connection,
  User,
  ChatDotRound,
  Collection,
  Search,
  CircleCheck,
  CircleClose,
  Warning
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { useEmqxRealtimeApi } from '/@/api/emqxRealtime/emqxRealtime'

// API实例
const emqxRealtimeApi = useEmqxRealtimeApi()

// 响应式数据
const refreshing = ref(false)
const instancesLoading = ref(false)
const topicsLoading = ref(false)
const topicFilter = ref('')

// 监控数据
const monitorData = reactive({
  onlineInstances: 0,
  totalClients: 0,
  totalMessages: 0,
  activeTopic: 0
})

// 图表配置
const messageChartPeriod = ref('1h')
const clientChartPeriod = ref('1h')
const messageChartRef = ref()
const clientChartRef = ref()
let messageChart: echarts.ECharts | null = null
let clientChart: echarts.ECharts | null = null

// 实例列表
const instancesList = ref([
  {
    id: 1,
    instanceName: 'EMQX-Production',
    host: '*************',
    port: 1883,
    status: 'online',
    clientCount: 1250,
    messageRate: 850,
    uptime: '15天 8小时',
    lastUpdate: new Date(),
    cpuUsage: 45,
    memoryUsage: 62
  },
  {
    id: 2,
    instanceName: 'EMQX-Testing',
    host: '*************',
    port: 1883,
    status: 'online',
    clientCount: 320,
    messageRate: 180,
    uptime: '3天 12小时',
    lastUpdate: new Date(),
    cpuUsage: 25,
    memoryUsage: 38
  },
  {
    id: 3,
    instanceName: 'EMQX-Development',
    host: '*************',
    port: 1883,
    status: 'offline',
    clientCount: 0,
    messageRate: 0,
    uptime: '0天 0小时',
    lastUpdate: new Date(Date.now() - 300000),
    cpuUsage: 0,
    memoryUsage: 0
  }
])

// 主题列表
const topicsList = ref([
  {
    id: 1,
    topicName: 'sensor/temperature',
    subscriberCount: 25,
    messageCount: 15420,
    messageRate: 12,
    lastMessage: new Date(),
    qos: 1,
    subscribers: [
      { clientId: 'client_001', subscribeTime: new Date(), qos: 1 },
      { clientId: 'client_002', subscribeTime: new Date(), qos: 1 }
    ]
  },
  {
    id: 2,
    topicName: 'device/status',
    subscriberCount: 18,
    messageCount: 8950,
    messageRate: 8,
    lastMessage: new Date(),
    qos: 0,
    subscribers: [
      { clientId: 'client_003', subscribeTime: new Date(), qos: 0 }
    ]
  },
  {
    id: 3,
    topicName: 'alert/system',
    subscriberCount: 5,
    messageCount: 1250,
    messageRate: 2,
    lastMessage: new Date(),
    qos: 2,
    subscribers: [
      { clientId: 'client_004', subscribeTime: new Date(), qos: 2 }
    ]
  }
])

// 对话框状态
const instanceDetailVisible = ref(false)
const topicDetailVisible = ref(false)
const selectedInstance = ref(null)
const selectedTopic = ref(null)

// 计算属性
const filteredTopics = computed(() => {
  if (!topicFilter.value) return topicsList.value
  return topicsList.value.filter(topic => 
    topic.topicName.toLowerCase().includes(topicFilter.value.toLowerCase())
  )
})

// 定时器
let refreshTimer: NodeJS.Timeout | null = null

// 方法
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatTime = (time: Date): string => {
  return time.toLocaleString('zh-CN')
}

const getStatusIcon = (status: string): string => {
  switch (status) {
    case 'online': return 'CircleCheck'
    case 'offline': return 'CircleClose'
    case 'warning': return 'Warning'
    default: return 'CircleClose'
  }
}

const getStatusType = (status: string): string => {
  switch (status) {
    case 'online': return 'success'
    case 'offline': return 'danger'
    case 'warning': return 'warning'
    default: return 'info'
  }
}

const getStatusText = (status: string): string => {
  switch (status) {
    case 'online': return '在线'
    case 'offline': return '离线'
    case 'warning': return '警告'
    default: return '未知'
  }
}

const getQosType = (qos: number): string => {
  switch (qos) {
    case 0: return 'info'
    case 1: return 'warning'
    case 2: return 'danger'
    default: return 'info'
  }
}

// 刷新所有数据
const refreshAllData = async () => {
  refreshing.value = true
  try {
    await Promise.all([
      loadMonitorData(),
      refreshInstances(),
      refreshTopics(),
      updateCharts()
    ])
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  } finally {
    refreshing.value = false
  }
}

// 加载监控数据
const loadMonitorData = async () => {
  // 模拟API调用
  monitorData.onlineInstances = instancesList.value.filter(i => i.status === 'online').length
  monitorData.totalClients = instancesList.value.reduce((sum, i) => sum + i.clientCount, 0)
  monitorData.totalMessages = topicsList.value.reduce((sum, t) => sum + t.messageCount, 0)
  monitorData.activeTopic = topicsList.value.length
}

// 刷新实例数据
const refreshInstances = async () => {
  instancesLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    instancesList.value.forEach(instance => {
      if (instance.status === 'online') {
        instance.lastUpdate = new Date()
        instance.messageRate = Math.floor(Math.random() * 1000) + 100
        instance.clientCount = Math.floor(Math.random() * 500) + 100
      }
    })
  } finally {
    instancesLoading.value = false
  }
}

// 刷新主题数据
const refreshTopics = async () => {
  topicsLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    topicsList.value.forEach(topic => {
      topic.lastMessage = new Date()
      topic.messageRate = Math.floor(Math.random() * 20) + 1
      topic.messageCount += Math.floor(Math.random() * 100)
    })
  } finally {
    topicsLoading.value = false
  }
}

// 导出数据
const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

// 查看实例详情
const viewInstanceDetail = (instance: any) => {
  selectedInstance.value = instance
  instanceDetailVisible.value = true
}

// 查看主题详情
const viewTopicDetail = (topic: any) => {
  selectedTopic.value = topic
  topicDetailVisible.value = true
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  
  // 消息流量图表
  if (messageChartRef.value) {
    messageChart = echarts.init(messageChartRef.value)
    updateMessageChart()
  }
  
  // 客户端连接图表
  if (clientChartRef.value) {
    clientChart = echarts.init(clientChartRef.value)
    updateClientChart()
  }
}

// 更新消息流量图表
const updateMessageChart = () => {
  if (!messageChart) return
  
  const hours = Array.from({ length: 24 }, (_, i) => {
    const hour = new Date()
    hour.setHours(hour.getHours() - 23 + i)
    return hour.getHours() + ':00'
  })
  
  const data = Array.from({ length: 24 }, () => Math.floor(Math.random() * 1000) + 200)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: hours
    },
    yAxis: {
      type: 'value',
      name: '消息数/小时'
    },
    series: [{
      name: '消息数量',
      type: 'line',
      smooth: true,
      data: data,
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
          { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
        ])
      },
      lineStyle: {
        color: '#409eff'
      }
    }]
  }
  
  messageChart.setOption(option)
}

// 更新客户端连接图表
const updateClientChart = () => {
  if (!clientChart) return
  
  const hours = Array.from({ length: 24 }, (_, i) => {
    const hour = new Date()
    hour.setHours(hour.getHours() - 23 + i)
    return hour.getHours() + ':00'
  })
  
  const data = Array.from({ length: 24 }, () => Math.floor(Math.random() * 500) + 100)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: hours
    },
    yAxis: {
      type: 'value',
      name: '连接数'
    },
    series: [{
      name: '客户端连接',
      type: 'bar',
      data: data,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: '#67c23a' },
          { offset: 1, color: '#85ce61' }
        ])
      }
    }]
  }
  
  clientChart.setOption(option)
}

// 更新图表
const updateCharts = () => {
  updateMessageChart()
  updateClientChart()
}

// 启动定时刷新
const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    loadMonitorData()
    updateCharts()
  }, 30000) // 30秒刷新一次
}

// 停止定时刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(async () => {
  await loadMonitorData()
  await initCharts()
  startAutoRefresh()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    messageChart?.resize()
    clientChart?.resize()
  })
})

onUnmounted(() => {
  stopAutoRefresh()
  messageChart?.dispose()
  clientChart?.dispose()
  window.removeEventListener('resize', () => {
    messageChart?.resize()
    clientChart?.resize()
  })
})
</script>

<style lang="scss" scoped>
.monitor-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 4px;
  
  .page-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    
    .el-icon {
      color: #409eff;
    }
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }
  
  &.online-card {
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
    color: white;
  }
  
  &.clients-card {
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    color: white;
  }
  
  &.messages-card {
    background: linear-gradient(135deg, #e6a23c 0%, #ebb563 100%);
    color: white;
  }
  
  &.topics-card {
    background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
    color: white;
  }
  
  :deep(.el-card__body) {
    padding: 20px;
  }
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  opacity: 0.8;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart-container {
  height: 300px;
  width: 100%;
}

.instances-row,
.topics-row {
  margin-bottom: 20px;
}

.instances-card,
.topics-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
  
  .header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.instance-name,
.topic-name {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .el-icon {
    color: #409eff;
  }
}

.instance-detail,
.topic-detail {
  .topic-subscribers {
    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-weight: 600;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .monitor-container {
    padding: 12px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    
    .header-actions {
      justify-content: center;
    }
  }
  
  .stat-content {
    justify-content: center;
    text-align: center;
  }
  
  .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    
    .header-actions {
      justify-content: center;
    }
  }
}
</style>