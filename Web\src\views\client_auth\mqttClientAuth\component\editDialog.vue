﻿<script lang="ts" name="mqttClientAuth" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useMqttClientAuthApi } from '/@/api/client_auth/mqttClientAuth';

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const mqttClientAuthApi = useMqttClientAuthApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
});

// 自行添加其他规则
const rules = ref<FormRules>({
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? {  };
	state.ruleForm = row.id ? await mqttClientAuthApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));
	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await mqttClientAuthApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="mqttClientAuth-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="实例ID" prop="instanceId">
							<el-input v-model="state.ruleForm.instanceId" placeholder="请输入实例ID" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="客户端ID" prop="clientId">
							<el-input v-model="state.ruleForm.clientId" placeholder="请输入客户端ID" maxlength="128" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="客户端引用ID" prop="clientRefId">
							<el-input v-model="state.ruleForm.clientRefId" placeholder="请输入客户端引用ID" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="认证模式（Username、AliyunSignature、JWT等）" prop="authMode">
							<el-input v-model="state.ruleForm.authMode" placeholder="请输入认证模式（Username、AliyunSignature、JWT等）" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="用户名" prop="username">
							<el-input v-model="state.ruleForm.username" placeholder="请输入用户名" maxlength="128" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="密码" prop="password">
							<el-input v-model="state.ruleForm.password" placeholder="请输入密码" maxlength="128" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="密码哈希" prop="passwordHash">
							<el-input v-model="state.ruleForm.passwordHash" placeholder="请输入密码哈希" maxlength="256" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="密码盐值" prop="passwordSalt">
							<el-input v-model="state.ruleForm.passwordSalt" placeholder="请输入密码盐值" maxlength="64" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="AccessKey ID（阿里云认证）" prop="accessKeyId">
							<el-input v-model="state.ruleForm.accessKeyId" placeholder="请输入AccessKey ID（阿里云认证）" maxlength="64" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="AccessKey Secret（加密存储）" prop="accessKeySecret">
							<el-input v-model="state.ruleForm.accessKeySecret" placeholder="请输入AccessKey Secret（加密存储）" maxlength="128" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="签名算法（hmacmd5、hmacsha1、hmacsha256）" prop="signMethod">
							<el-input v-model="state.ruleForm.signMethod" placeholder="请输入签名算法（hmacmd5、hmacsha1、hmacsha256）" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="JWT密钥" prop="jwtSecret">
							<el-input v-model="state.ruleForm.jwtSecret" placeholder="请输入JWT密钥" maxlength="256" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="JWT过期时间（秒）" prop="jwtExpiry">
							<el-input-number v-model="state.ruleForm.jwtExpiry" placeholder="请输入JWT过期时间（秒）" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="JWT令牌" prop="jwtToken">
							<el-input v-model="state.ruleForm.jwtToken" placeholder="请输入JWT令牌" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="客户端证书" prop="certificate">
							<el-input v-model="state.ruleForm.certificate" placeholder="请输入客户端证书" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="私钥" prop="privateKey">
							<el-input v-model="state.ruleForm.privateKey" placeholder="请输入私钥" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="CA证书" prop="caCertificate">
							<el-input v-model="state.ruleForm.caCertificate" placeholder="请输入CA证书" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否启用（0:否，1:是）" prop="isEnabled">
							<el-switch v-model="state.ruleForm.isEnabled" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="允许的IP地址列表" prop="allowedIPs">
							<el-input v-model="state.ruleForm.allowedIPs" placeholder="请输入允许的IP地址列表" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="拒绝的IP地址列表" prop="deniedIPs">
							<el-input v-model="state.ruleForm.deniedIPs" placeholder="请输入拒绝的IP地址列表" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="速率限制（消息/秒）" prop="rateLimit">
							<el-input-number v-model="state.ruleForm.rateLimit" placeholder="请输入速率限制（消息/秒）" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="登录尝试次数" prop="loginAttempts">
							<el-input-number v-model="state.ruleForm.loginAttempts" placeholder="请输入登录尝试次数" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="过期时间" prop="expireTime">
							<el-date-picker v-model="state.ruleForm.expireTime" type="date" placeholder="过期时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最后认证时间" prop="lastAuthTime">
							<el-date-picker v-model="state.ruleForm.lastAuthTime" type="date" placeholder="最后认证时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最后登录时间" prop="lastLoginTime">
							<el-date-picker v-model="state.ruleForm.lastLoginTime" type="date" placeholder="最后登录时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最后登录IP" prop="lastLoginIP">
							<el-input v-model="state.ruleForm.lastLoginIP" placeholder="请输入最后登录IP" maxlength="45" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="认证成功次数" prop="successCount">
							<el-input-number v-model="state.ruleForm.successCount" placeholder="请输入认证成功次数" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="认证成功总数" prop="authSuccessCount">
							<el-input-number v-model="state.ruleForm.authSuccessCount" placeholder="请输入认证成功总数" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="认证失败次数" prop="failedCount">
							<el-input-number v-model="state.ruleForm.failedCount" placeholder="请输入认证失败次数" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否锁定（0:未锁定，1:已锁定）" prop="isLocked">
							<el-switch v-model="state.ruleForm.isLocked" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="锁定时间" prop="lockTime">
							<el-date-picker v-model="state.ruleForm.lockTime" type="date" placeholder="锁定时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="解锁时间" prop="unlockTime">
							<el-date-picker v-model="state.ruleForm.unlockTime" type="date" placeholder="解锁时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="锁定原因" prop="lockReason">
							<el-input v-model="state.ruleForm.lockReason" placeholder="请输入锁定原因" maxlength="256" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最大失败次数" prop="maxFailedCount">
							<el-input-number v-model="state.ruleForm.maxFailedCount" placeholder="请输入最大失败次数" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="锁定持续时间（分钟）" prop="lockDuration">
							<el-input-number v-model="state.ruleForm.lockDuration" placeholder="请输入锁定持续时间（分钟）" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="IP白名单" prop="ipWhitelist">
							<el-input v-model="state.ruleForm.ipWhitelist" placeholder="请输入IP白名单" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="IP黑名单" prop="ipBlacklist">
							<el-input v-model="state.ruleForm.ipBlacklist" placeholder="请输入IP黑名单" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="允许的主题列表" prop="allowedTopics">
							<el-input v-model="state.ruleForm.allowedTopics" placeholder="请输入允许的主题列表" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="拒绝的主题列表" prop="deniedTopics">
							<el-input v-model="state.ruleForm.deniedTopics" placeholder="请输入拒绝的主题列表" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最大连接数" prop="maxConnections">
							<el-input-number v-model="state.ruleForm.maxConnections" placeholder="请输入最大连接数" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="当前连接数" prop="currentConnections">
							<el-input-number v-model="state.ruleForm.currentConnections" placeholder="请输入当前连接数" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="连接超时时间（秒）" prop="connectionTimeout">
							<el-input-number v-model="state.ruleForm.connectionTimeout" placeholder="请输入连接超时时间（秒）" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最大消息大小" prop="maxMessageSize">
							<el-input-number v-model="state.ruleForm.maxMessageSize" placeholder="请输入最大消息大小" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最大订阅数" prop="maxSubscriptions">
							<el-input-number v-model="state.ruleForm.maxSubscriptions" placeholder="请输入最大订阅数" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="消息速率限制" prop="messageRateLimit">
							<el-input-number v-model="state.ruleForm.messageRateLimit" placeholder="请输入消息速率限制" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="字节速率限制" prop="byteRateLimit">
							<el-input-number v-model="state.ruleForm.byteRateLimit" placeholder="请输入字节速率限制" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="扩展属性（JSON）" prop="extendedProperties">
							<el-input v-model="state.ruleForm.extendedProperties" placeholder="请输入扩展属性（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="" prop="key">
							<el-input v-model="state.ruleForm.key" placeholder="请输入" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="备注" prop="remark">
							<el-input v-model="state.ruleForm.remark" placeholder="请输入备注" maxlength="500" show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>