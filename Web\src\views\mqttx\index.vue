<template>
	<div class="mqtt-box">
		<h1 class="header">MQTTX在线测试客户端</h1>
		
		<!-- 统计信息卡片 -->
		<el-card class="stats-card">
			<div class="stats-container">
				<div class="stat-item">
					<div class="stat-value">{{ testState.statistics.totalMessages }}</div>
					<div class="stat-label">总消息数</div>
				</div>
				<div class="stat-item">
					<div class="stat-value">{{ testState.statistics.publishedMessages }}</div>
					<div class="stat-label">已发布</div>
				</div>
				<div class="stat-item">
					<div class="stat-value">{{ testState.statistics.receivedMessages }}</div>
					<div class="stat-label">已接收</div>
				</div>
				<div class="stat-item">
					<div class="stat-value">{{ testState.statistics.connectionTime || '未连接' }}</div>
					<div class="stat-label">连接时间</div>
				</div>
				<div class="stat-actions">
					<el-button size="small" @click="resetStatistics">重置统计</el-button>
					<el-button size="small" type="primary" @click="openMessageMonitor">消息监控</el-button>
				</div>
			</div>
		</el-card>
		
		<el-card :model="connection">
			<div class="card-header">
				<h1>连接参数(Configuration)</h1>
				<div class="preset-actions">
					<el-dropdown @command="applyConnectionPreset">
						<el-button size="small" type="info">
							连接预设 <el-icon class="el-icon--right"><ele-ArrowDown /></el-icon>
						</el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item v-for="preset in testState.connectionPresets" :key="preset.name" :command="preset">
									{{ preset.name }}
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
					<el-button size="small" @click="saveAsPreset">保存预设</el-button>
				</div>
			</div>
			<el-form label-position="top" :model="connection">
				<el-row :gutter="6">
					<el-col :span="8">
						<el-form-item prop="host" label="协议|主机|端口">
							<el-input v-model="connection.host" :disabled="connSuccess" type="password" show-password>
								<template #prepend>
									<el-select v-model="connection.protocol" class="w80" :disabled="connSuccess" @change="handleProtocolChange">
										<el-option label="ws://" value="ws"></el-option>
										<el-option label="wss://" value="wss"></el-option>
									</el-select>
								</template>
								<template #append>
									<el-input v-model.number="connection.port" type="number" class="w80" :disabled="connSuccess" placeholder="8083/8084"></el-input>
								</template>
							</el-input>
						</el-form-item>
					</el-col>
					<el-col :span="0">
						<el-form-item prop="clientId" label="标识(Client ID)唯一性">
							<el-input v-model="connection.clientId"> </el-input>
						</el-form-item>
					</el-col>
					<el-col :span="0">
						<el-form-item prop="username" label="账号(Username)">
							<el-input v-model="connection.username"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="0">
						<el-form-item prop="password" label="密码(Password)">
							<el-input v-model="connection.password" type="password" show-password></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="4">
						<el-form-item prop="regpacket" label="设备包名(Regpacket)">
							<el-input v-model="connection.repacket" :disabled="connSuccess" @input="syncdhtreg" @change="init_topic"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="4">
						<el-form-item prop="dhtRegpack" label="共享传感器(dhtRegpacket)">
							<el-input v-model="connection.dhtRegpack" :disabled="connSuccess" @change="init_topic"></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="8" class="text-right">
						<el-button
							type="primary"
							:icon="Setting"
							class="sub-btn"
							:disabled="client.connected"
							@click="createConnection"
							:loading="btnLoadingType === 'connect'"
							:style="{ display: client.connected ? 'none' : '' }"
						>
							{{ client.connected ? '已连接(Connected)' : '连接(Connect)' }}
						</el-button>
						<el-button v-if="client.connected" class="sub-btn" type="warning" :icon="Discount" @click="destroyConnection" :loading="btnLoadingType === 'disconnect'"> 断开(Disconnect) </el-button>
					</el-col>
				</el-row>
			</el-form>
		</el-card>

		<el-card shadow="hover">
			<div class="card-header">
				<h1>订阅(Subscribe)</h1>
				<div class="batch-actions">
					<el-button size="small" type="success" @click="openBatchDialog('subscribe')">
						<el-icon><Bell /></el-icon>
						批量订阅
					</el-button>
				</div>
			</div>
			<el-form label-position="top" :model="subscription">
				<el-row :gutter="6">
					<el-col :span="12">
						<el-form-item prop="topic" label="订阅主题(Topic)">
							<el-input v-model="connection.subTopics" :disabled="subscribedSuccess" type="password" show-password></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="4">
						<el-form-item prop="qos" label="订阅质量(QoS)">
							<el-select v-model="subscription.qos" :disabled="subscribedSuccess">
								<el-option v-for="qos in qosList" :key="qos" :label="qos == 0 ? '0 至多一次' : qos == 1 ? '1 至少一次' : '2 仅仅一次'" :value="qos"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="8" class="text-right">
						<el-button
							type="primary"
							:icon="Connection"
							class="sub-btn"
							:style="{ display: subscribedSuccess ? 'none' : '' }"
							:loading="btnLoadingType === 'subscribe'"
							:disabled="!client.connected || subscribedSuccess"
							@click="doSubscribe"
						>
							{{ subscribedSuccess ? '已订阅(Subscribed)' : '订阅(Subscribe)' }}
						</el-button>
						<el-button v-if="subscribedSuccess" type="warning" :icon="Discount" class="sub-btn" :loading="btnLoadingType === 'unsubscribe'" :disabled="!client.connected" @click="doUnSubscribe">
							取消(Unsubscribe)
						</el-button>
					</el-col>
				</el-row>
			</el-form>
		</el-card>

		<el-card shadow="hover">
			<div class="card-header">
				<h1>发布(Publish)</h1>
				<div class="template-actions">
					<el-dropdown @command="applyMessageTemplate">
						<el-button size="small" type="info">
							消息模板 <el-icon class="el-icon--right"><ele-ArrowDown /></el-icon>
						</el-button>
						<template #dropdown>
							<el-dropdown-menu>
								<el-dropdown-item v-for="template in testState.messageTemplates" :key="template.name" :command="template">
									{{ template.name }}
								</el-dropdown-item>
							</el-dropdown-menu>
						</template>
					</el-dropdown>
					<el-button size="small" @click="saveAsTemplate">保存模板</el-button>
					<el-button size="small" type="warning" @click="openBatchDialog('publish')">
						<el-icon><Promotion /></el-icon>
						批量发布
					</el-button>
				</div>
			</div>
			<el-form label-position="top" :model="publish">
				<el-row :gutter="6">
					<el-col :span="8">
						<el-form-item prop="topic" label="发布主题(Topic)">
							<el-input v-model="connection.pubTopic" type="password" show-password></el-input>
						</el-form-item>
					</el-col>
					<el-col :span="4">
						<el-form-item prop="qos" label="发布质量(QoS)">
							<el-select v-model="publish.qos">
								<el-option v-for="qos in qosList" :key="qos" :label="qos == 0 ? '0 至多一次' : qos == 1 ? '1 至少一次' : '2 仅仅一次'" :value="qos"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="4">
						<el-form-item prop="retain" label="发布保留(Retain)">
							<el-select v-model="publish.retain">
								<el-option value="false" label="false 不保留"></el-option>
								<el-option value="true" label="true 不保留"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="6">
					<el-col :span="16">
						<el-form-item prop="payload" label="操作指令(Payload)">
							<el-input v-model="publish.payload" clearable maxlength="64" show-word-limit>
								<!--<template #prepend>
								<el-button :icon="Operation" />
								</template> -->
								<template #append>
									<el-select v-model="publish.payload" placeholder="选择指令" style="width: 115px">
										<el-option label="状态查询" value="55 AA AA AA AA 91 CF" />
										<el-option label="全部打开" value="55 AA AA AA AA 81 A4 01" />
										<el-option label="全部关闭" value="55 AA AA AA AA 81 A4 00" />
										<el-option label="一路开关" value="55 AA AA AA AA 81 BA 01" />
										<el-option label="二路开关" value="55 AA AA AA AA 81 BA 02" />
										<el-option label="三路开关" value="55 AA AA AA AA 81 BA 03" />
										<el-option label="四路开关" value="55 AA AA AA AA 81 BA 04" />
									</el-select>
								</template>
							</el-input>
						</el-form-item>
					</el-col>
					<el-col :span="8" class="text-right">
						<el-button type="success" :icon="Position" class="sub-btn" :loading="btnLoadingType === 'publish'" :disabled="!client.connected" @click="doPublish(publish.payload, connection.pubTopic)">
							发布(Publish)
						</el-button>
					</el-col>
				</el-row>
			</el-form>
		</el-card>

		<el-card shadow="hover">
			<h1>
				<el-button @click="clsmsg" type="success" :icon="Delete" title="点击清空历史记录">接收(Receive)</el-button>
				<el-tag title="接收次数">收 {{ recvnum }}</el-tag>
				<el-tag :title="dht_tm">{{ dht_wsd }}</el-tag>
				<el-tag title="设备已工作时长">{{ parseInt(runSeconds) }} 秒</el-tag>
				<el-button
					type="success"
					title="关闭一路"
					:disabled="!connection.onlineStatus || !client.connected"
					v-if="connection.ch1_Status"
					icon="ele-Check"
					id="ch1"
					v-reclick="2000"
					@click="switchLight('55 AA AA AA AA 81 01 00')"
					>关闭</el-button
				>
				<el-button
					type="warning"
					title="打开一路"
					:disabled="!connection.onlineStatus || !client.connected"
					v-else="!connection.ch1_Status"
					icon="ele-CloseBold"
					id="ch1"
					v-reclick="2000"
					@click="switchLight('55 AA AA AA AA 81 01 01')"
					>打开</el-button
				>
				<el-button
					type="success"
					title="关闭二路"
					:disabled="!connection.onlineStatus || !client.connected"
					v-if="connection.ch2_Status"
					icon="ele-Check"
					id="ch2"
					v-reclick="2000"
					@click="switchLight('55 AA AA AA AA 81 02 00')"
					>关闭</el-button
				>
				<el-button
					type="warning"
					title="打开二路"
					:disabled="!connection.onlineStatus || !client.connected"
					v-else="!connection.ch2_Status"
					icon="ele-CloseBold"
					id="ch2"
					v-reclick="2000"
					@click="switchLight('55 AA AA AA AA 81 02 01')"
					>打开</el-button
				>
				<el-button
					type="success"
					title="关闭三路"
					:disabled="!connection.onlineStatus || !client.connected"
					v-if="connection.ch3_Status"
					icon="ele-Check"
					id="ch3"
					v-reclick="2000"
					@click="switchLight('55 AA AA AA AA 81 03 00')"
					>关闭</el-button
				>
				<el-button
					type="warning"
					title="打开三路"
					:disabled="!connection.onlineStatus || !client.connected"
					v-else="!connection.ch3_Status"
					icon="ele-CloseBold"
					id="ch3"
					v-reclick="2000"
					@click="switchLight('55 AA AA AA AA 81 03 01')"
					>打开</el-button
				>
				<el-button
					type="success"
					title="关闭四路"
					:disabled="!connection.onlineStatus || !client.connected"
					v-if="connection.ch4_Status"
					icon="ele-Check"
					id="ch4"
					v-reclick="2000"
					@click="switchLight('55 AA AA AA AA 81 04 00')"
					>关闭</el-button
				>
				<el-button
					type="warning"
					title="打开四路"
					:disabled="!connection.onlineStatus || !client.connected"
					v-else="!connection.ch4_Status"
					icon="ele-CloseBold"
					id="ch4"
					v-reclick="2000"
					@click="switchLight('55 AA AA AA AA 81 04 01')"
					>打开</el-button
				>
				<el-button
					type="danger"
					title="四路全部关闭"
					:disabled="!connection.onlineStatus || !client.connected"
					v-if="connection.all_Status"
					icon="ele-SwitchButton"
					id="ch5"
					@click="switchLight('55 AA AA AA AA 81 A4 00')"
					>全关</el-button
				>
				<el-button
					type="success"
					title="四路全部打开"
					:disabled="!connection.onlineStatus || !client.connected"
					v-else="!connection.all_Status"
					icon="ele-Switch"
					id="ch5"
					@click="switchLight('55 AA AA AA AA 81 A4 01')"
					>全开</el-button
				>

				<el-alert v-if="!client.connected || !connection.onlineStatus" title="网络服务断开或设备离线!" center type="warning" effect="light" style="margin-top: 4px" />
			</h1>
			<!-- 绑定接收日志，只读 -->
			<el-col :span="24">
				<el-input type="textarea" :rows="8" id="recv" v-model="receivedMessages" readonly class="log"></el-input>
			</el-col>
		</el-card>
	</div>

	<!-- 批量操作对话框 -->
	<el-dialog v-model="testState.batchDialog.visible" :title="testState.batchDialog.type === 'publish' ? '批量发布消息' : '批量订阅主题'" width="600px" destroy-on-close>
		<div class="batch-dialog-content">
			<div class="batch-header">
				<span>{{ testState.batchDialog.type === 'publish' ? '发布到以下主题:' : '订阅以下主题:' }}</span>
				<el-button size="small" type="primary" @click="addBatchTopic">添加主题</el-button>
			</div>
			<div class="batch-topics">
				<div v-for="(topic, index) in testState.batchDialog.topics" :key="index" class="topic-input-group">
					<el-input v-model="testState.batchDialog.topics[index]" placeholder="请输入主题名称" />
					<el-button 
						size="small" 
						type="danger" 
						@click="removeBatchTopic(index)"
						:disabled="testState.batchDialog.topics.length <= 1"
					>
						删除
					</el-button>
				</div>
			</div>
			<div v-if="testState.batchDialog.type === 'publish'" class="batch-message">
				<el-form-item label="消息内容">
					<el-input v-model="publish.payload" type="textarea" :rows="3" placeholder="请输入要发布的消息内容" />
				</el-form-item>
			</div>
		</div>
		<template #footer>
			<el-button @click="testState.batchDialog.visible = false">取消</el-button>
			<el-button type="primary" @click="executeBatchOperation" :loading="testState.batchDialog.loading">
				{{ testState.batchDialog.type === 'publish' ? '批量发布' : '批量订阅' }}
			</el-button>
		</template>
	</el-dialog>

	<!-- 消息监控对话框 -->
	<el-dialog v-model="testState.messageMonitor.visible" title="消息监控" width="900px" destroy-on-close>
		<div class="message-monitor">
			<div class="monitor-header">
				<div class="subscribed-topics">
					<span>已订阅主题:</span>
					<el-tag v-for="topic in Array.from(testState.messageMonitor.subscribedTopics)" :key="topic" class="topic-tag">
						{{ topic }}
					</el-tag>
					<span v-if="testState.messageMonitor.subscribedTopics.size === 0" class="no-topics">暂无订阅主题</span>
				</div>
				<div class="monitor-actions">
					<el-switch v-model="testState.messageMonitor.autoScroll" active-text="自动滚动" />
					<el-button size="small" @click="clearMonitorMessages">清空消息</el-button>
					<el-button size="small" type="primary" @click="exportMonitorMessages">导出消息</el-button>
				</div>
			</div>
			<el-divider>实时消息 ({{ testState.messageMonitor.messages.length }}/{{ testState.messageMonitor.maxMessages }})</el-divider>
			<div class="message-list" ref="messageListRef">
				<div v-for="(message, index) in testState.messageMonitor.messages" :key="index" class="message-item">
					<div class="message-header">
						<span class="topic">{{ message.topic }}</span>
						<span class="qos">QoS: {{ message.qos }}</span>
						<span class="time">{{ message.timestamp }}</span>
					</div>
					<div class="message-content">{{ message.payload }}</div>
				</div>
				<el-empty v-if="testState.messageMonitor.messages.length === 0" description="暂无消息" />
			</div>
		</div>
		<template #footer>
			<el-button @click="testState.messageMonitor.visible = false">关闭</el-button>
		</template>
	</el-dialog>
</template>

<script setup lang="ts" name="mqttx">
import { reactive, ref, onMounted, nextTick, onUnmounted } from 'vue';
import { Search, ChatDotSquare, TopRight, Star, Operation, Setting, Connection, Discount, Open, Delete, Position, Monitor, Bell, Promotion } from '@element-plus/icons-vue';
import * as MQTT from 'mqtt/dist/mqtt.min'; // 针对4.3.7版本的引用方法。5.7.x会提示错误 (import * as MQTT from "mqtt")
import mittBus from '/@/utils/mitt'; // 事件总线mitt 解决打包后错误Uncaught (in promise) ReferenceError: Cannot access 'oe' before initialization
import { useEmqxRealtimeApi } from '/@/api/emqx-realtime';
import { ElMessage, ElMessageBox } from 'element-plus';

// vue 3 + vite use MQTT.js refer to https://github.com/mqttjs/MQTT.js/issues/1269
// https://github.com/mqttjs/MQTT.js#qos
const qosList = [0, 1, 2]; // 质量
const now = new Date();
const recvnum = ref(0);
const dht_wd = ref(0); // 温度、湿度
const dht_sd = ref(0);
const dht_tm = ref(''); // 同步时间
const dht_wsd = ref('温度0℃,湿度0%');
const runSeconds = ref(0); // 工作时长

// 初始化emqxRealtime API
const emqxRealtimeApi = useEmqxRealtimeApi();

// 增强的状态管理
const testState = reactive({
  // 连接测试
  connectionTests: new Map(),
  testingConnections: new Set(),
  
  // 消息监控
  messageMonitor: {
    visible: false,
    messages: [],
    subscribedTopics: new Set(),
    autoScroll: true,
    maxMessages: 1000
  },
  
  // 批量操作
  batchDialog: {
    visible: false,
    type: '', // 'publish' | 'subscribe'
    topics: [],
    loading: false
  },
  
  // 连接配置预设
  connectionPresets: [
    {
      name: 'EMQX Public Broker',
      protocol: 'ws',
      host: 'broker.emqx.io',
      port: 8083,
      username: '',
      password: ''
    },
    {
      name: 'EMQX Public Broker (SSL)',
      protocol: 'wss',
      host: 'broker.emqx.io',
      port: 8084,
      username: '',
      password: ''
    },
    {
      name: 'Local EMQX',
      protocol: 'ws',
      host: 'localhost',
      port: 8083,
      username: '',
      password: ''
    }
  ],
  
  // 消息模板
  messageTemplates: [
    {
      name: 'JSON消息',
      payload: '{"message": "Hello MQTT", "timestamp": "' + new Date().toISOString() + '"}'
    },
    {
      name: '设备状态',
      payload: '{"deviceId": "device001", "status": "online", "temperature": 25.5}'
    },
    {
      name: '传感器数据',
      payload: '{"sensorId": "temp001", "value": 23.8, "unit": "°C", "timestamp": "' + new Date().toISOString() + '"}'
    }
  ],
  
  // 统计信息
  statistics: {
    totalMessages: 0,
    publishedMessages: 0,
    receivedMessages: 0,
    connectionTime: null,
    lastActivity: null
  }
});

// mqtt客户端变量 let或const
const client = ref({
	connected: false, //未连接
} as MQTT.MqttClient);

const receivedMessages = ref('');
const subscribedSuccess = ref(false); //订阅成功标志
const connSuccess = ref(false); //连接成功标志
const btnLoadingType = ref('');
const retryTimes = ref(0); //重连次数

/**
 * this demo uses EMQX Public MQTT Broker (https://www.emqx.com/en/mqtt/public-mqtt5-broker), here are the details:
 * 参考https://github.com/emqx/MQTT-Client-Examples
 * 方法https://github.com/mqttjs/MQTT.js
 * Broker host: broker.emqx.io
 * WebSocket port: 8083
 * WebSocket over TLS/SSL port: 8084
 * ws -> 8083; wss -> 8084
 * By default, EMQX allows clients to connect without authentication.
 * https://docs.emqx.com/en/enterprise/v4.4/advanced/auth.html#anonymous-login

 * for more options and details, please refer to https://github.com/mqttjs/MQTT.js#mqttclientstreambuilder-options
 */
const connection = reactive({
	protocol: 'ws',
	host: 'broker.emqx.io',
	// ws -> 8083; wss -> 8084
	port: 8083,
	clientId: 'emqx_vue3_' + Math.random().toString(16).substring(2, 8),
	username: '',
	password: '',
	repacket: 'd1ca1ff51f04', //注册包（改为您的注册包）
	dhtRegpack: 'd1ca1ff51f04', //温度注册包（可以相同可以共享传感器）
	mqttToken: '0804d4c44c1f1bd11dea461481f19868', //授权TOKEN自己约定
	keepalive: 30,
	clean: true, //清除 clean session
	connectTimeout: 30 * 1000, // ms 超时毫秒
	reconnectPeriod: 5000, // ms 重连毫秒
	resubscribe: true, //重新订阅
	//定义您自己的主题
	subTopic: 'mqtt/admintnet/#0#/out',
	willTopic: 'mqtt/admintnet/#0#/will',
	dhtTopic: 'mqtt/admintnet/#0#/dht',
	pubTopic: 'mqtt/admintnet/#0#/into',
	subTopics: [],
	pubPayload: '{"msg":"hellow vue3 mqtt."}',
	onlineStatus: false,
	ch1_Status: false,
	ch2_Status: false,
	ch3_Status: false,
	ch4_Status: false,
	all_Status: false,
	isAC: null, //强电true
});
// 初始化主题
const init_topic = () => {
	let st = 'mqtt/admintnet/#0#/out'; //订阅主题
	let pt = 'mqtt/admintnet/#0#/into'; //发布主题
	let ptbody = '{"token":"{0}","cmd":"{1}","cmdpara":"{2}","clientid":"{3}"}';
	let wt = 'mqtt/admintnet/#0#/will'; //遗嘱主题
	let dh = 'mqtt/admintnet/#0#/dht'; //温湿度
	connection.subTopic = st.replace('#0#', connection.repacket);
	connection.willTopic = wt.replace('#0#', connection.repacket);
	connection.dhtTopic = dh.replace('#0#', connection.dhtRegpack); //温湿度
	connection.pubTopic = pt.replace('#0#', connection.repacket);
	connection.subTopics = [connection.subTopic, connection.willTopic, connection.dhtTopic];
	connection.pubPayload = ptbody;
	//console.log(connection.subTopics);
};

// 默认注册包同步和传感器包名一致，反之不动
const syncdhtreg = () => {
	connection.dhtRegpack = connection.repacket;
};

// 字符串替换模拟  string.format(str,ar1,arn)
const stringFormat = (formatted, args) => {
	for (let i = 0; i < args.length; i++) {
		let regexp = new RegExp('\\{' + i + '\\}', 'gi');
		formatted = formatted.replace(regexp, args[i]);
	}
	return formatted;
};

onMounted(async () => {
	init_topic();
	nextTick(() => {});
});

// topic & QoS for MQTT subscribing 订阅主题(多个)
const subscription = ref({
	topic: `$(connection.subTopics.value)`,
	qos: 0 as MQTT.QoS,
});

// topic, QoS & payload for publishing message 发布主题
const publish = ref({
	topic: `${connection.pubTopic}`,
	qos: 0 as MQTT.QoS,
	retain: false, //保留否
	payload: '55 AA AA AA AA 91 CF', //'{ "msg": "Hello, I am browser." }',
});

const initData = () => {
	client.value = {
		connected: false,
	} as MQTT.MqttClient;
	retryTimes.value = 0;
	btnLoadingType.value = '';
	subscribedSuccess.value = false;
};

const handleOnReConnect = () => {
	retryTimes.value++;
	connection.clientId = 'emqx_vue3_' + Math.random().toString(16).substring(2, 8);
	console.log(retryTimes.value, '重试次数');
	if (retryTimes.value > 5) {
		try {
			client.value.end(); //重连超过5次断开
			initData();
			console.log('connection maxReconnectTimes limit, stop retry');
			appmessage(now.toLocaleString() + '|超出重连接次数，停止重试' + retryTimes.value);
		} catch (error) {
			console.log('handleOnReConnect catch error:', error);
		}
	}
};

/**
 * if protocol is "ws", connectUrl = "ws://broker.emqx.io:8083/mqtt"
 * if protocol is "wss", connectUrl = "wss://broker.emqx.io:8084/mqtt"
 *
 * /mqtt: MQTT-WebSocket uniformly uses /path as the connection path,
 * which should be specified when connecting, and the path used on EMQX is /mqtt.
 *
 * for more details about "mqtt.connect" method & options,
 * please refer to https://github.com/mqttjs/MQTT.js#mqttconnecturl-options
 */
// create MQTT connection 创建连接
const createConnection = () => {
	try {
		btnLoadingType.value = 'connect';
		const { protocol, host, port, ...options } = connection;
		const connectUrl = `${protocol}://${host}:${port}/mqtt`; //组成新的连接字符串
		console.log(connectUrl, '连接地址');
		client.value = MQTT.connect(connectUrl, options);
		if (client.value.on) {
			// https://github.com/mqttjs/MQTT.js#event-connect
			client.value.on('connect', () => {
				//v5.x  reconnect
				btnLoadingType.value = '';
				connSuccess.value = true; //client.value.connected;
				testState.statistics.connectionTime = new Date().toLocaleString();
				console.log('connection successful', client.value.connected);
				appmessage(now.toLocaleString() + '|连接服务成功');
				ElMessage.success('MQTT连接成功');
			});

			// https://github.com/mqttjs/MQTT.js#event-reconnect 重连回调
			client.value.on('reconnect', handleOnReConnect);
			// https://github.com/mqttjs/MQTT.js#event-error
			client.value.on('error', (error) => {
				console.log('connection error:', error);
				appmessage(now.toLocaleString() + '|发生错误：' + error);
			});

			// https://github.com/mqttjs/MQTT.js#event-message 接收消息，处理方法单独定义
			client.value.on('message', (topic: string, message) => {
				//处理方法
				recvnum.value++; //接收次数累计
				testState.statistics.receivedMessages++;
				updateStatistics();
				
				// 添加到消息监控
				const messageData = {
					topic,
					payload: message.toString(),
					timestamp: new Date().toLocaleString(),
					qos: 0 // 这里可以从消息中获取实际的QoS
				};
				
				// 限制消息数量
				if (testState.messageMonitor.messages.length >= testState.messageMonitor.maxMessages) {
					testState.messageMonitor.messages.shift();
				}
				testState.messageMonitor.messages.push(messageData);
				
				doAction(topic, message); //处理
				receivedMessages.value = receivedMessages.value.concat(
					//拼接字符串输出
					now.toLocaleString() + ' ' + `${topic}\r\n` + message.toString() + '\r\n'
				);
				// console.log(now.toLocaleString()+`收到消息: ${message} from topic: ${topic}`);
				//滚动此方法可行
				nextTick(() => {
					setTimeout(() => {
						syncBottom(); //滚动到底部
					}, 50);
				});
			});
		}
	} catch (error) {
		btnLoadingType.value = '';
		console.log('mqtt.connect error:', error);
	}
};

// 处理事件
const doAction = (t, msg) => {
	let res = JSON.parse(msg.toString()); //必须规范的json格式否则出错，双引号不能是单引号；；；后不安全但强大 eval('(' + message.toString() + ')'); //JSON.parse(message.toString());//json对象

	// 消息不能带''否则错误
	let regp = res.regpacket; // 接收的注册包
	let regs = connection.repacket; // 订阅的注册包
	let isOK = regp == regs ? true : false; // 是不是本设备的消息
	if (!isOK || regp == null) {
		return; // 不是丢弃
	}

	if (t == connection.dhtTopic) {
		// 温湿度
		let rp = res.regpacket;
		let wd = res.temperature;
		let sd = res.humidity;
		let sj = res.time;
		let sc = res.runsec;
		if (rp != connection.dhtRegpack) {
			// 来自订阅的温湿度包
			return;
		}
		if (rp != null) {
			dht_wd.value = wd; // 实际应用时替换此3个变量即可
			dht_sd.value = sd;
			dht_tm.value = '更新时间:' + sj;
			runSeconds.value = sc;
			dht_wsd.value = '温度:' + dht_wd.value + '℃,湿度:' + dht_sd.value + '%';
			//state.option.title.text="实时温湿度变化趋势图(运行"+parseInt(sc)+"秒)";
			//updatechart(false);//实时数据(这种方法是实时推送，如果用 定时器 是定时显示的)updatewsd_time(false)
		}
	}
	if (t == connection.willTopic) {
		// 遗嘱
		if (res.redata == 'offline') {
			connection.onlineStatus = false;
		} else {
			connection.onlineStatus = true;
		}
	}
	if (t == connection.subTopic) {
		let rp0 = res.regpacket;
		if (rp0 != undefined) {
			if (rp0 == regs) {
				op(res.redata); // 该设备执行指令其他放弃
			}
		}
	}
};

// 处理开关状态(自定义的指令，需要修改为您自己的指令)
const op = (cmd: any) => {
	if (cmd == '55 AA AA AA AA 82 01 01') {
		connection.ch1_Status = true;
	}
	if (cmd == '55 AA AA AA AA 82 01 00') {
		connection.ch1_Status = false;
	}
	if (cmd == '55 AA AA AA AA 82 02 01') {
		connection.ch2_Status = true;
	}
	if (cmd == '55 AA AA AA AA 82 02 00') {
		connection.ch2_Status = false;
	}
	if (cmd == '55 AA AA AA AA 82 03 01') {
		connection.ch3_Status = true;
	}
	if (cmd == '55 AA AA AA AA 82 03 00') {
		connection.ch3_Status = false;
	}
	if (cmd == '55 AA AA AA AA 82 04 01') {
		connection.ch4_Status = true;
	}
	if (cmd == '55 AA AA AA AA 82 04 00') {
		connection.ch4_Status = false;
	}
	if (cmd == '55 AA AA AA AA 82 A4 01') {
		connection.ch1_Status = true;
		connection.ch2_Status = true;
		connection.ch3_Status = true;
		connection.ch4_Status = true;
	}
	if (cmd == '55 AA AA AA AA 82 A4 00') {
		connection.ch1_Status = false;
		connection.ch2_Status = false;
		connection.ch3_Status = false;
		connection.ch4_Status = false;
	}
	if (cmd == '55 AA AA AA AA 84 AC 01') {
		connection.isAC = true;
	}
	if (cmd == '55 AA AA AA AA 84 AC 00') {
		connection.isAC = false;
	}
	if (connection.ch1_Status && connection.ch2_Status && connection.ch3_Status && connection.ch4_Status) {
		connection.all_Status = true;
	}
	if (!connection.ch1_Status && !connection.ch2_Status && !connection.ch3_Status && !connection.ch4_Status) {
		connection.all_Status = false;
	}
	if (cmd == '55 AA AA AA AA 84 AC 01') {
		connection.isAC = true;
	}
	if (cmd == '55 AA AA AA AA 84 AC 00') {
		connection.isAC = false;
	}
};

// 自动同步滚动（建议延时执行）textarea:any=null
const syncBottom = () => {
	const textarea = document.getElementById('recv');
	if (textarea) {
		textarea.scrollTop = textarea.scrollHeight - 30;
	}
};

// subscribe topic 开始订阅
// https://github.com/mqttjs/MQTT.js#mqttclientsubscribetopictopic-arraytopic-object-options-callback
const doSubscribe = () => {
	btnLoadingType.value = 'subscribe';
	const { topic, qos } = subscription.value;
	console.log(connection.subTopics, '订阅主题');
	client.value.subscribe(connection.subTopics, { qos }, (error: Error, granted: mqtt.ISubscriptionGrant[]) => {
		btnLoadingType.value = '';
		if (error) {
			console.log('subscribe error:', error);
			return;
		}
		subscribedSuccess.value = true; //订阅成功
		// 连接成功，发布首个问询指令
		switchLight('55 AA AA AA AA 91 CF'); //发送首页问询指令
		console.log('订阅成功subscribe successfully:', granted);
	});
};

// unsubscribe topic 取消订阅
// https://github.com/mqttjs/MQTT.js#mqttclientunsubscribetopictopic-array-options-callback
const doUnSubscribe = () => {
	btnLoadingType.value = 'unsubscribe';
	const { topic, qos } = subscription.value;
	client.value.unsubscribe(connection.subTopics, { qos }, (error) => {
		btnLoadingType.value = '';
		subscribedSuccess.value = false;
		if (error) {
			console.log('unsubscribe error:', error);
			return;
		}
		console.log(`unsubscribed topic: ${topic}`);
	});
};

// publish message发布消息
// https://github.com/mqttjs/MQTT.js#mqttclientpublishtopic-message-options-callback
const doPublish = (b, t) => {
	btnLoadingType.value = "publish";
	const { topic, qos, payload, retain } = publish.value;
	//console.log(t+b,"发布内容")
	let paybody = stringFormat(connection.pubPayload, [connection.mqttToken, b ?? publish.value.payload, '', connection.clientId]); //标准格式payload
	client.value.publish(t ?? connection.pubTopic, paybody, { qos }, (error) => {
		nextTick(() => {
			// 测试延时
			setTimeout(() => {
				btnLoadingType.value = '';
			}, 50);
		});
		if (error) {
			appmessage(now.toLocaleString() + '|发布消息错误.' + error);
			console.log('publish error:', error);
			ElMessage.error('消息发布失败');
			return;
		}
		// 更新统计信息
		testState.statistics.publishedMessages++;
		updateStatistics();
		ElMessage.success('消息发布成功');
	});
};

// 消息追加消息框
const appmessage = (msg) => {
	receivedMessages.value = receivedMessages.value.concat(
		// 拼接字符串输出
		msg + '\r\n'
	);
};

// 开关
const switchLight = (cmd) => {
	if (!client.value.connected) {
		appmessage('尚未连接到服务!');
		return;
	}
	let paybody = stringFormat(connection.pubPayload, [connection.mqttToken, cmd ?? publish.value.payload, '', connection.clientId]);
	const { topic, qos, payload, retain } = publish.value;
	//console.log(t+b,"发布内容")
	client.value.publish(connection.pubTopic, paybody, { qos }, retain, (error) => {
		btnLoadingType.value = '';
		if (error) {
			console.log('publish error:', error);
			return;
		}
	});
};

// disconnect 端口连接
// https://github.com/mqttjs/MQTT.js#mqttclientendforce-options-callback
const destroyConnection = () => {
	if (client.value.connected) {
		btnLoadingType.value = 'disconnect';
		try {
			client.value.end(false, () => {
				initData();
				connSuccess.value = false;
				testState.statistics.connectionTime = null;
				//console.log("断开成功 disconnected successfully");
				appmessage(now.toLocaleString() + '|连接已断开.');
				ElMessage.success('MQTT连接已断开');
			});
		} catch (error) {
			btnLoadingType.value = '';
			console.log('断开错误 disconnect error:', error);
			ElMessage.error('断开连接失败');
		}
	}
};

// 端口随协议而改变
const handleProtocolChange = (value: string) => {
	connection.port = value === 'wss' ? 8084 : 8083;
};

// 清空消息框
const clsmsg = () => {
	receivedMessages.value = '';
	testState.statistics.receivedMessages = 0;
};

// 增强功能函数

// 测试连接到指定实例
const testInstanceConnection = async (instanceId: string) => {
	if (testState.testingConnections.has(instanceId)) {
		return;
	}
	
	testState.testingConnections.add(instanceId);
	try {
		const result = await emqxRealtimeApi.testConnection({ instanceId });
		testState.connectionTests.set(instanceId, {
			status: result.data.success ? 'success' : 'failed',
			message: result.data.message || '',
			timestamp: new Date().toLocaleString()
		});
		
		ElMessage.success(`实例 ${instanceId} 连接测试${result.data.success ? '成功' : '失败'}`);
	} catch (error) {
		console.error('连接测试失败:', error);
		testState.connectionTests.set(instanceId, {
			status: 'error',
			message: '测试失败',
			timestamp: new Date().toLocaleString()
		});
		ElMessage.error('连接测试失败');
	} finally {
		testState.testingConnections.delete(instanceId);
	}
};

// 应用连接预设
const applyConnectionPreset = (preset: any) => {
	connection.protocol = preset.protocol;
	connection.host = preset.host;
	connection.port = preset.port;
	connection.username = preset.username;
	connection.password = preset.password;
	ElMessage.success(`已应用预设: ${preset.name}`);
};

// 保存当前连接为预设
const saveAsPreset = () => {
	ElMessageBox.prompt('请输入预设名称', '保存连接预设', {
		confirmButtonText: '保存',
		cancelButtonText: '取消',
		inputPattern: /\S+/,
		inputErrorMessage: '预设名称不能为空'
	}).then(({ value }) => {
		const newPreset = {
			name: value,
			protocol: connection.protocol,
			host: connection.host,
			port: connection.port,
			username: connection.username,
			password: connection.password
		};
		testState.connectionPresets.push(newPreset);
		ElMessage.success('预设保存成功');
	}).catch(() => {});
};

// 应用消息模板
const applyMessageTemplate = (template: any) => {
	publish.value.payload = template.payload;
	ElMessage.success(`已应用模板: ${template.name}`);
};

// 保存当前消息为模板
const saveAsTemplate = () => {
	if (!publish.value.payload.trim()) {
		ElMessage.warning('消息内容不能为空');
		return;
	}
	
	ElMessageBox.prompt('请输入模板名称', '保存消息模板', {
		confirmButtonText: '保存',
		cancelButtonText: '取消',
		inputPattern: /\S+/,
		inputErrorMessage: '模板名称不能为空'
	}).then(({ value }) => {
		const newTemplate = {
			name: value,
			payload: publish.value.payload
		};
		testState.messageTemplates.push(newTemplate);
		ElMessage.success('模板保存成功');
	}).catch(() => {});
};

// 打开批量操作对话框
const openBatchDialog = (type: string) => {
	testState.batchDialog.type = type;
	testState.batchDialog.topics = [''];
	testState.batchDialog.visible = true;
};

// 添加批量主题
const addBatchTopic = () => {
	testState.batchDialog.topics.push('');
};

// 移除批量主题
const removeBatchTopic = (index: number) => {
	if (testState.batchDialog.topics.length > 1) {
		testState.batchDialog.topics.splice(index, 1);
	}
};

// 执行批量操作
const executeBatchOperation = async () => {
	const validTopics = testState.batchDialog.topics.filter(topic => topic.trim());
	if (validTopics.length === 0) {
		ElMessage.warning('请至少输入一个有效的主题');
		return;
	}
	
	testState.batchDialog.loading = true;
	try {
		if (testState.batchDialog.type === 'subscribe') {
			// 批量订阅
			for (const topic of validTopics) {
				if (client.value.connected) {
					client.value.subscribe(topic, { qos: subscription.value.qos });
					testState.messageMonitor.subscribedTopics.add(topic);
				}
			}
			ElMessage.success(`批量订阅完成，共订阅 ${validTopics.length} 个主题`);
		} else if (testState.batchDialog.type === 'publish') {
			// 批量发布
			for (const topic of validTopics) {
				if (client.value.connected) {
					client.value.publish(topic, publish.value.payload, { qos: publish.value.qos });
					testState.statistics.publishedMessages++;
				}
			}
			ElMessage.success(`批量发布完成，共发布到 ${validTopics.length} 个主题`);
		}
		testState.batchDialog.visible = false;
	} catch (error) {
		console.error('批量操作失败:', error);
		ElMessage.error('批量操作失败');
	} finally {
		testState.batchDialog.loading = false;
	}
};

// 打开消息监控
const openMessageMonitor = () => {
	testState.messageMonitor.visible = true;
};

// 清空监控消息
const clearMonitorMessages = () => {
	testState.messageMonitor.messages = [];
};

// 导出监控消息
const exportMonitorMessages = () => {
	const messages = testState.messageMonitor.messages;
	if (messages.length === 0) {
		ElMessage.warning('没有消息可导出');
		return;
	}
	
	const content = messages.map(msg => 
		`时间: ${msg.timestamp}\n主题: ${msg.topic}\n消息: ${msg.payload}\n---`
	).join('\n');
	
	const blob = new Blob([content], { type: 'text/plain' });
	const url = URL.createObjectURL(blob);
	const a = document.createElement('a');
	a.href = url;
	a.download = `mqtt-messages-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
	a.click();
	URL.revokeObjectURL(url);
	ElMessage.success('消息导出成功');
};

// 更新统计信息
const updateStatistics = () => {
	testState.statistics.lastActivity = new Date().toLocaleString();
	testState.statistics.totalMessages = testState.statistics.publishedMessages + testState.statistics.receivedMessages;
};

// 重置统计信息
const resetStatistics = () => {
	testState.statistics.totalMessages = 0;
	testState.statistics.publishedMessages = 0;
	testState.statistics.receivedMessages = 0;
	testState.statistics.connectionTime = null;
	testState.statistics.lastActivity = null;
	ElMessage.success('统计信息已重置');
};

// 组件卸载时清理
onUnmounted(() => {
	if (client.value && client.value.connected) {
		client.value.end();
	}
});
</script>

<style lang="scss" scoped>
.mqtt-box {
	max-width: 100%;
	margin: 0 auto;
}

.header {
	font-size: 24px;
	font-weight: bold;
	margin: -6px auto 0px auto;
	text-align: center;
	color: #409eff;
}

// 统计信息卡片样式
.stats-card {
	margin-bottom: 16px;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	
	:deep(.el-card__body) {
		padding: 20px;
	}
}

.stats-container {
	display: flex;
	align-items: center;
	gap: 24px;
	flex-wrap: wrap;
}

.stat-item {
	text-align: center;
	min-width: 100px;
}

.stat-value {
	font-size: 24px;
	font-weight: bold;
	margin-bottom: 4px;
}

.stat-label {
	font-size: 12px;
	opacity: 0.9;
}

.stat-actions {
	margin-left: auto;
	display: flex;
	gap: 8px;
}

// 卡片头部样式
.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16px;
	flex-wrap: wrap;
	gap: 12px;
}

.preset-actions,
.template-actions,
.batch-actions {
	display: flex;
	gap: 8px;
	align-items: center;
}

// 批量操作对话框样式
.batch-dialog-content {
	.batch-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16px;
		font-weight: bold;
	}
	
	.batch-topics {
		max-height: 300px;
		overflow-y: auto;
		margin-bottom: 16px;
	}
	
	.topic-input-group {
		display: flex;
		gap: 8px;
		margin-bottom: 8px;
		align-items: center;
	}
	
	.batch-message {
		border-top: 1px solid #ebeef5;
		padding-top: 16px;
	}
}

// 消息监控样式
.message-monitor {
	.monitor-header {
		margin-bottom: 16px;
		
		.subscribed-topics {
			display: flex;
			align-items: center;
			gap: 8px;
			margin-bottom: 12px;
			flex-wrap: wrap;
			
			.topic-tag {
				margin: 2px;
			}
			
			.no-topics {
				color: #909399;
				font-style: italic;
			}
		}
		
		.monitor-actions {
			display: flex;
			gap: 12px;
			align-items: center;
			justify-content: flex-end;
		}
	}
	
	.message-list {
		height: 400px;
		overflow-y: auto;
		border: 1px solid #ebeef5;
		border-radius: 4px;
		padding: 8px;
		background-color: #fafafa;
		
		.message-item {
			background: white;
			border: 1px solid #e4e7ed;
			border-radius: 6px;
			padding: 12px;
			margin-bottom: 8px;
			transition: all 0.3s ease;
			
			&:hover {
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
				transform: translateY(-1px);
			}
			
			.message-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 8px;
				font-size: 12px;
				flex-wrap: wrap;
				gap: 8px;
				
				.topic {
					font-weight: bold;
					color: #409eff;
					background: #ecf5ff;
					padding: 2px 8px;
					border-radius: 12px;
				}
				
				.qos {
					color: #67c23a;
					font-weight: 500;
				}
				
				.time {
					color: #909399;
				}
			}
			
			.message-content {
				background-color: #f8f9fa;
				padding: 8px 12px;
				border-radius: 4px;
				font-family: 'Courier New', monospace;
				font-size: 13px;
				word-break: break-all;
				line-height: 1.4;
				border-left: 3px solid #409eff;
			}
		}
	}
}

h1 {
	font-size: 16px;
	margin: 0;
	padding: 0;
	color: #303133;
}

.el-col {
	padding: 4px;
}

.el-input {
	font-size: 13px;
}

.el-card {
	margin-bottom: 16px;
	border-radius: 8px;
	box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	
	&:hover {
		box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
	}
}

.el-card__body {
	padding: 24px;
}

.el-select {
	width: 100%;
}

.text-right {
	text-align: right;
}

.sub-btn {
	margin-top: 20px;
	width: 160px;
	border-radius: 6px;
	font-weight: 500;
}

.hidden {
	display: none;
}

.w80 {
	width: 80px;
}

.w100 {
	width: 100px;
}

.log {
	font-size: 14px;
	color: #fff;
	background-color: #2c3e50;
	border-radius: 4px;
	font-family: 'Courier New', monospace;
	line-height: 1.5;
}

.center {
	text-align: center;
}

#ch1,
#ch2,
#ch3,
#ch4,
#ch5 {
	width: 120px;
	border-radius: 6px;
	font-weight: 500;
}

.el-tag {
	padding: 4px 8px;
	margin: 2px;
	min-width: 60px;
	border-radius: 4px;
}

// 响应式设计
@media (max-width: 768px) {
	.stats-container {
		justify-content: center;
	}
	
	.card-header {
		flex-direction: column;
		align-items: stretch;
	}
	
	.preset-actions,
	.template-actions,
	.batch-actions {
		justify-content: center;
	}
	
	.sub-btn {
		width: 100%;
	}
}
</style>
