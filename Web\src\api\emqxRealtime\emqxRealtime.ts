import { useB<PERSON><PERSON><PERSON> } from '/@/api/base/baseApi';
import request from '/@/utils/request';

// EMQX连接输入
export interface EmqxConnectInput {
	instanceId: number; // MQTT实例ID
}

// EMQX断开连接输入
export interface EmqxDisconnectInput {
	instanceId: number; // MQTT实例ID
}

// EMQX发布消息输入
export interface EmqxPublishInput {
	instanceId: number; // MQTT实例ID
	topic: string; // 主题
	payload: string; // 消息内容
	qos?: number; // QoS等级(0-2)
	retain?: boolean; // 是否保留消息
}

// EMQX订阅主题输入
export interface EmqxSubscribeInput {
	instanceId: number; // MQTT实例ID
	topic: string; // 主题
	qos?: number; // QoS等级(0-2)
}

// EMQX取消订阅输入
export interface EmqxUnsubscribeInput {
	instanceId: number; // MQTT实例ID
	topic: string; // 主题
}

// EMQX连接状态输出
export interface EmqxConnectionStatusOutput {
	instanceId: number; // MQTT实例ID
	isConnected: boolean; // 是否已连接
	status: string; // 连接状态
	checkTime: string; // 检查时间
}

// EMQX活跃连接输出
export interface EmqxActiveConnectionsOutput {
	connections: EmqxConnectionInfo[]; // 连接列表
	totalCount: number; // 总连接数
}

// EMQX连接信息
export interface EmqxConnectionInfo {
	instanceId: number; // MQTT实例ID
	clientId: string; // 客户端ID
	isConnected: boolean; // 是否已连接
	connectedAt: string; // 连接时间
	lastActivity: string; // 最后活动时间
}

// EMQX批量连接输入
export interface EmqxBatchConnectInput {
	instanceIds: number[]; // MQTT实例ID列表
}

// EMQX批量断开输入
export interface EmqxBatchDisconnectInput {
	instanceIds: number[]; // MQTT实例ID列表
}

// EMQX批量操作输出
export interface EmqxBatchOperationOutput {
	results: EmqxOperationResult[]; // 操作结果列表
	successCount: number; // 成功数量
	failureCount: number; // 失败数量
	operationTime: string; // 操作时间
}

// EMQX操作结果
export interface EmqxOperationResult {
	instanceId: number; // MQTT实例ID
	success: boolean; // 是否成功
	message: string; // 消息
}

// EMQX实时统计输出
export interface EmqxRealtimeStatisticsOutput {
	activeConnections: number; // 活跃连接数
	activeInstances: number; // 活跃实例数
	subscribedTopics: number; // 订阅主题数
	cachedMessages: number; // 缓存消息数
	totalMessages: number; // 总消息数
	lastUpdated: string; // 最后更新时间
}

// EMQX健康状态输出
export interface EmqxHealthStatusOutput {
	isHealthy: boolean; // 是否健康
	status: string; // 状态描述
	services: EmqxServiceStatus[]; // 服务状态列表
	lastCheck: string; // 最后检查时间
}

// EMQX服务状态
export interface EmqxServiceStatus {
	serviceName: string; // 服务名称
	isRunning: boolean; // 是否运行中
	status: string; // 状态
	message?: string; // 消息
}

/**
 * EMQX实时操作API服务
 */
export const useEmqxRealtimeApi = () => {
	return {
		/**
		 * 连接到EMQX服务器
		 * @param input 连接输入参数
		 */
		connect: (input: EmqxConnectInput) => {
			return request<boolean>({
				url: '/api/emqxRealtime/connect',
				method: 'POST',
				data: input,
			});
		},

		/**
		 * 断开EMQX连接
		 * @param input 断开连接输入参数
		 */
		disconnect: (input: EmqxDisconnectInput) => {
			return request<boolean>({
				url: '/api/emqxRealtime/disconnect',
				method: 'POST',
				data: input,
			});
		},

		/**
		 * 发布消息到EMQX
		 * @param input 发布消息输入参数
		 */
		publish: (input: EmqxPublishInput) => {
			return request<boolean>({
				url: '/api/emqxRealtime/publish',
				method: 'POST',
				data: input,
			});
		},

		/**
		 * 订阅EMQX主题
		 * @param input 订阅主题输入参数
		 */
		subscribe: (input: EmqxSubscribeInput) => {
			return request<boolean>({
				url: '/api/emqxRealtime/subscribe',
				method: 'POST',
				data: input,
			});
		},

		/**
		 * 取消订阅EMQX主题
		 * @param input 取消订阅输入参数
		 */
		unsubscribe: (input: EmqxUnsubscribeInput) => {
			return request<boolean>({
				url: '/api/emqxRealtime/unsubscribe',
				method: 'POST',
				data: input,
			});
		},

		/**
		 * 获取连接状态
		 * @param input 获取连接状态输入参数
		 */
		getConnectionStatus: (input: { instanceId: number }) => {
			return request<EmqxConnectionStatusOutput>({
				url: '/api/emqxRealtime/getConnectionStatus',
				method: 'GET',
				params: input,
			});
		},

		/**
		 * 获取所有活跃连接
		 */
		getActiveConnections: () => {
			return request<EmqxActiveConnectionsOutput>({
				url: '/api/emqxRealtime/getActiveConnections',
				method: 'GET',
			});
		},

		/**
		 * 批量连接EMQX实例
		 * @param input 批量连接输入参数
		 */
		batchConnect: (input: EmqxBatchConnectInput) => {
			return request<EmqxBatchOperationOutput>({
				url: '/api/emqxRealtime/batchConnect',
				method: 'POST',
				data: input,
			});
		},

		/**
		 * 批量断开EMQX连接
		 * @param input 批量断开输入参数
		 */
		batchDisconnect: (input: EmqxBatchDisconnectInput) => {
			return request<EmqxBatchOperationOutput>({
				url: '/api/emqxRealtime/batchDisconnect',
				method: 'POST',
				data: input,
			});
		},

		/**
		 * 获取实时统计信息
		 */
		getRealtimeStatistics: () => {
			return request<EmqxRealtimeStatisticsOutput>({
				url: '/api/emqxRealtime/getRealtimeStatistics',
				method: 'GET',
			});
		},

		/**
		 * 获取健康状态
		 */
		getHealthStatus: () => {
			return request<EmqxHealthStatusOutput>({
				url: '/api/emqxRealtime/getHealthStatus',
				method: 'GET',
			});
		},

		/**
		 * 刷新实例状态
		 * @param input 实例ID列表
		 */
		refreshInstanceStatus: (input: { instanceIds: number[] }) => {
			return request<boolean>({
				url: '/api/emqxRealtime/refreshInstanceStatus',
				method: 'POST',
				data: input,
			});
		},
	};
};