<script lang="ts" setup name="mqttClientAuth">
import { ref, reactive, onMounted } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { downloadStreamFile } from "/@/utils/download";
import { useMqttClientAuthApi } from '/@/api/client_auth/mqttClientAuth';
import editDialog from '/@/views/client_auth/mqttClientAuth/component/editDialog.vue'
import printDialog from '/@/views/system/print/component/hiprint/preview.vue'
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import { 
  Search, 
  Plus, 
  Delete, 
  Edit, 
  Refresh, 
  Download, 
  Upload,
  Key,
  Lock,
  Unlock,
  Connection,
  Warning
} from '@element-plus/icons-vue';

const mqttClientAuthApi = useMqttClientAuthApi();
const printDialogRef = ref();
const editDialogRef = ref();
const state = reactive({
  exportLoading: false,
  tableLoading: false,
  stores: {},
  showAdvanceQueryUI: false,
  dropdownData: {} as any,
  selectData: [] as any[],
  tableQueryParams: {} as any,
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 0,
    field: 'createTime', // 默认的排序字段
    order: 'descending', // 排序方向
    descStr: 'descending', // 降序排序的关键字符
  },
  tableData: [],
});

// 页面加载时
onMounted(async () => {
});

// 查询操作
const handleQuery = async (params: any = {}) => {
  state.tableLoading = true;
  state.tableParams = Object.assign(state.tableParams, params);
  const result = await mqttClientAuthApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then(res => res.data.result);
  state.tableParams.total = result?.total;
  state.tableData = result?.items ?? [];
  state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};

// 删除
const delMqttClientAuth = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await mqttClientAuthApi.delete({ id: row.id });
    handleQuery();
    ElMessage.success("删除成功");
  }).catch(() => {});
};

// 查看详情
const viewDetails = (row: any) => {
  ElMessageBox.alert(
    `<div style="text-align: left;">
      <p><strong>实例ID:</strong> ${row.instanceId || '-'}</p>
      <p><strong>客户端ID:</strong> ${row.clientId || '-'}</p>
      <p><strong>认证模式:</strong> ${row.authMode || '-'}</p>
      <p><strong>用户名:</strong> ${row.username || '-'}</p>
      <p><strong>创建时间:</strong> ${row.createTime || '-'}</p>
      <p><strong>更新时间:</strong> ${row.updateTime || '-'}</p>
      <p><strong>备注:</strong> ${row.remark || '-'}</p>
    </div>`,
    '认证配置详情',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确定'
    }
  );
};

// 导出Excel
const onExportExcel = () => {
  state.exportLoading = true;
  mqttClientAuthApi.exportMqttClientAuth(state.tableQueryParams)
    .then((res: any) => {
      downloadStreamFile(res, '客户端认证配置.xlsx');
    })
    .finally(() => {
      state.exportLoading = false;
    });
};

// 批量删除
const batchDelMqttClientAuth = () => {
  ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await mqttClientAuthApi.batchDelete(state.selectData.map(u => ({ id: u.id }) )).then(res => {
      ElMessage.success(`成功批量删除${res.data.result}条记录`);
      handleQuery();
    });
  }).catch(() => {});
};

handleQuery();
</script>
<template>
  <div class="mqtt-client-auth-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-icon class="header-icon"><Key /></el-icon>
          <div class="header-text">
            <h2>客户端认证管理</h2>
            <p>管理MQTT客户端的认证配置和权限控制</p>
          </div>
        </div>
        <div class="header-stats">
          <div class="stat-card">
            <div class="stat-number">{{ state.tableParams.total }}</div>
            <div class="stat-label">总认证配置</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ state.tableData.filter(item => item.isEnabled).length }}</div>
            <div class="stat-label">活跃认证</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never" v-loading="state.exportLoading">
      <div class="search-container">
        <div class="search-form">
          <el-form :model="state.tableQueryParams" inline>
            <el-form-item label="关键字">
              <el-input 
                v-model="state.tableQueryParams.keyword" 
                placeholder="请输入模糊查询关键字" 
                :prefix-icon="Search"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="实例ID" v-if="state.showAdvanceQueryUI">
              <el-input 
                v-model="state.tableQueryParams.instanceId" 
                placeholder="请输入实例ID" 
                clearable
                style="width: 180px"
              />
            </el-form-item>
            <el-form-item label="客户端ID" v-if="state.showAdvanceQueryUI">
              <el-input 
                v-model="state.tableQueryParams.clientId" 
                placeholder="请输入客户端ID" 
                clearable
                style="width: 180px"
              />
            </el-form-item>
            <el-form-item label="认证模式" v-if="state.showAdvanceQueryUI">
              <el-select 
                v-model="state.tableQueryParams.authMode" 
                placeholder="请选择认证模式" 
                clearable 
                style="width: 180px"
              >
                <el-option label="用户名密码" value="Username">
                  <div class="auth-option">
                    <el-icon><Lock /></el-icon>
                    <span>用户名密码</span>
                  </div>
                </el-option>
                <el-option label="阿里云签名" value="AliyunSignature">
                  <div class="auth-option">
                    <el-icon><Key /></el-icon>
                    <span>阿里云签名</span>
                  </div>
                </el-option>
                <el-option label="JWT" value="JWT">
                  <div class="auth-option">
                    <el-icon><Connection /></el-icon>
                    <span>JWT</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleQuery" v-auth="'mqttClientAuth:page'" v-reclick="1000">
                查询
              </el-button>
              <el-button :icon="Refresh" @click="() => state.tableQueryParams = {}">
                重置
              </el-button>
              <el-button :icon="state.showAdvanceQueryUI ? 'ele-ZoomOut' : 'ele-ZoomIn'" @click="() => state.showAdvanceQueryUI = !state.showAdvanceQueryUI">
                {{ state.showAdvanceQueryUI ? '隐藏' : '高级查询' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="action-buttons">
          <el-button 
            type="primary" 
            :icon="Plus" 
            @click="editDialogRef.openDialog(null, '新增MQTT 客户端认证信息表')" 
            v-auth="'mqttClientAuth:add'"
          >
            新增认证
          </el-button>
          <el-button 
            type="danger" 
            :icon="Delete" 
            @click="batchDelMqttClientAuth" 
            v-auth="'mqttClientAuth:batchDelete'"
            :disabled="state.selectData.length == 0"
          >
            批量删除
          </el-button>
          <el-button 
            type="success" 
            :icon="Download" 
            @click="onExportExcel"
          >
            导出
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 高级查询面板 -->
    <el-card v-if="state.showAdvanceQueryUI" class="advanced-search-card" shadow="never">
      <el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="120px">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6" class="mb10">
            <el-form-item label="客户端引用ID">
              <el-input v-model="state.tableQueryParams.clientRefId" clearable placeholder="请输入客户端引用ID"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" class="mb10">
            <el-form-item label="用户名">
              <el-input v-model="state.tableQueryParams.username" clearable placeholder="请输入用户名"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" class="mb10">
            <el-form-item label="密码">
              <el-input v-model="state.tableQueryParams.password" clearable placeholder="请输入密码" type="password" show-password/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="密码哈希">
              <el-input v-model="state.tableQueryParams.passwordHash" clearable placeholder="请输入密码哈希"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="密码盐值">
              <el-input v-model="state.tableQueryParams.passwordSalt" clearable placeholder="请输入密码盐值"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="AccessKey ID（阿里云认证）">
              <el-input v-model="state.tableQueryParams.accessKeyId" clearable placeholder="请输入AccessKey ID（阿里云认证）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="AccessKey Secret（加密存储）">
              <el-input v-model="state.tableQueryParams.accessKeySecret" clearable placeholder="请输入AccessKey Secret（加密存储）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="签名算法（hmacmd5、hmacsha1、hmacsha256）">
              <el-input v-model="state.tableQueryParams.signMethod" clearable placeholder="请输入签名算法（hmacmd5、hmacsha1、hmacsha256）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="JWT密钥">
              <el-input v-model="state.tableQueryParams.jwtSecret" clearable placeholder="请输入JWT密钥"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="JWT过期时间（秒）">
              <el-input-number v-model="state.tableQueryParams.jwtExpiry"  clearable placeholder="请输入JWT过期时间（秒）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="JWT令牌">
              <el-input v-model="state.tableQueryParams.jwtToken" clearable placeholder="请输入JWT令牌"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="客户端证书">
              <el-input v-model="state.tableQueryParams.certificate" clearable placeholder="请输入客户端证书"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="私钥">
              <el-input v-model="state.tableQueryParams.privateKey" clearable placeholder="请输入私钥"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="CA证书">
              <el-input v-model="state.tableQueryParams.caCertificate" clearable placeholder="请输入CA证书"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="是否启用（0:否，1:是）">
                  <el-select clearable filterable v-model="state.tableQueryParams.isEnabled" placeholder="请选择是否启用（0:否，1:是）"> 
                    <el-option     value="true" label="是" /> 
                    <el-option     value="false" label="否" />  
                  </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="允许的IP地址列表">
              <el-input v-model="state.tableQueryParams.allowedIPs" clearable placeholder="请输入允许的IP地址列表"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="拒绝的IP地址列表">
              <el-input v-model="state.tableQueryParams.deniedIPs" clearable placeholder="请输入拒绝的IP地址列表"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="速率限制（消息/秒）">
              <el-input-number v-model="state.tableQueryParams.rateLimit"  clearable placeholder="请输入速率限制（消息/秒）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="登录尝试次数">
              <el-input-number v-model="state.tableQueryParams.loginAttempts"  clearable placeholder="请输入登录尝试次数"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="过期时间">
              <el-date-picker type="daterange" v-model="state.tableQueryParams.expireTimeRange"  value-format="YYYY-MM-DD HH:mm:ss" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="最后认证时间">
              <el-date-picker type="daterange" v-model="state.tableQueryParams.lastAuthTimeRange"  value-format="YYYY-MM-DD HH:mm:ss" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="最后登录时间">
              <el-date-picker type="daterange" v-model="state.tableQueryParams.lastLoginTimeRange"  value-format="YYYY-MM-DD HH:mm:ss" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="最后登录IP">
              <el-input v-model="state.tableQueryParams.lastLoginIP" clearable placeholder="请输入最后登录IP"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="认证成功次数">
              <el-input-number v-model="state.tableQueryParams.successCount"  clearable placeholder="请输入认证成功次数"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="认证成功总数">
              <el-input-number v-model="state.tableQueryParams.authSuccessCount"  clearable placeholder="请输入认证成功总数"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="认证失败次数">
              <el-input-number v-model="state.tableQueryParams.failedCount"  clearable placeholder="请输入认证失败次数"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="是否锁定（0:未锁定，1:已锁定）">
                  <el-select clearable filterable v-model="state.tableQueryParams.isLocked" placeholder="请选择是否锁定（0:未锁定，1:已锁定）"> 
                    <el-option     value="true" label="是" /> 
                    <el-option     value="false" label="否" />  
                  </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="锁定时间">
              <el-date-picker type="daterange" v-model="state.tableQueryParams.lockTimeRange"  value-format="YYYY-MM-DD HH:mm:ss" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="解锁时间">
              <el-date-picker type="daterange" v-model="state.tableQueryParams.unlockTimeRange"  value-format="YYYY-MM-DD HH:mm:ss" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="锁定原因">
              <el-input v-model="state.tableQueryParams.lockReason" clearable placeholder="请输入锁定原因"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="最大失败次数">
              <el-input-number v-model="state.tableQueryParams.maxFailedCount"  clearable placeholder="请输入最大失败次数"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="锁定持续时间（分钟）">
              <el-input-number v-model="state.tableQueryParams.lockDuration"  clearable placeholder="请输入锁定持续时间（分钟）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="IP白名单">
              <el-input v-model="state.tableQueryParams.ipWhitelist" clearable placeholder="请输入IP白名单"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="IP黑名单">
              <el-input v-model="state.tableQueryParams.ipBlacklist" clearable placeholder="请输入IP黑名单"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="允许的主题列表">
              <el-input v-model="state.tableQueryParams.allowedTopics" clearable placeholder="请输入允许的主题列表"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="拒绝的主题列表">
              <el-input v-model="state.tableQueryParams.deniedTopics" clearable placeholder="请输入拒绝的主题列表"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="最大连接数">
              <el-input-number v-model="state.tableQueryParams.maxConnections"  clearable placeholder="请输入最大连接数"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="当前连接数">
              <el-input-number v-model="state.tableQueryParams.currentConnections"  clearable placeholder="请输入当前连接数"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="连接超时时间（秒）">
              <el-input-number v-model="state.tableQueryParams.connectionTimeout"  clearable placeholder="请输入连接超时时间（秒）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="最大消息大小">
              <el-input-number v-model="state.tableQueryParams.maxMessageSize"  clearable placeholder="请输入最大消息大小"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="最大订阅数">
              <el-input-number v-model="state.tableQueryParams.maxSubscriptions"  clearable placeholder="请输入最大订阅数"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="消息速率限制">
              <el-input-number v-model="state.tableQueryParams.messageRateLimit"  clearable placeholder="请输入消息速率限制"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="字节速率限制">
              <el-input-number v-model="state.tableQueryParams.byteRateLimit"  clearable placeholder="请输入字节速率限制"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="扩展属性（JSON）">
              <el-input v-model="state.tableQueryParams.extendedProperties" clearable placeholder="请输入扩展属性（JSON）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="">
              <el-input v-model="state.tableQueryParams.key" clearable placeholder="请输入"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="备注">
              <el-input v-model="state.tableQueryParams.remark" clearable placeholder="请输入备注"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table :data="state.tableData" @selection-change="(val: any[]) => { state.selectData = val; }" style="width: 100%" v-loading="state.tableLoading" tooltip-effect="light" row-key="id" @sort-change="sortChange" border>
        <el-table-column type="selection" width="40" align="center" v-if="auth('mqttClientAuth:batchDelete') || auth('mqttClientAuth:export')" />
        <el-table-column type="index" label="序号" width="55" align="center"/>
        <el-table-column prop='instanceId' label='实例ID' show-overflow-tooltip width="120" />
        <el-table-column prop='clientId' label='客户端ID' show-overflow-tooltip width="150" />
        <el-table-column prop='authMode' label='认证模式' show-overflow-tooltip width="140">
          <template #default="{ row }">
            <el-tag 
              :type="row.authMode === 'Username' ? 'primary' : row.authMode === 'JWT' ? 'success' : 'warning'" 
              :icon="row.authMode === 'Username' ? Lock : row.authMode === 'JWT' ? Connection : Key"
              size="small"
            >
              {{ row.authMode === 'Username' ? '用户名密码' : row.authMode === 'JWT' ? 'JWT' : row.authMode === 'AliyunSignature' ? '阿里云签名' : row.authMode }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop='username' label='用户名' show-overflow-tooltip width="120" />
        <el-table-column prop='passwordHash' label='密码哈希' show-overflow-tooltip />
        <el-table-column prop='passwordSalt' label='密码盐值' show-overflow-tooltip />
        <el-table-column prop='accessKeyId' label='AccessKey ID（阿里云认证）' show-overflow-tooltip />
        <el-table-column prop='accessKeySecret' label='AccessKey Secret（加密存储）' show-overflow-tooltip />
        <el-table-column prop='signMethod' label='签名算法（hmacmd5、hmacsha1、hmacsha256）' show-overflow-tooltip />
        <el-table-column prop='jwtSecret' label='JWT密钥' show-overflow-tooltip />
        <el-table-column prop='jwtExpiry' label='JWT过期时间（秒）' show-overflow-tooltip />
        <el-table-column prop='jwtToken' label='JWT令牌' show-overflow-tooltip />
        <el-table-column prop='certificate' label='客户端证书' show-overflow-tooltip />
        <el-table-column prop='privateKey' label='私钥' show-overflow-tooltip />
        <el-table-column prop='caCertificate' label='CA证书' show-overflow-tooltip />
        <el-table-column prop='isEnabled' label='是否启用（0:否，1:是）' show-overflow-tooltip>
          <template #default="scope">
            <el-tag v-if="scope.row.isEnabled"> 是 </el-tag>
            <el-tag type="danger" v-else> 否 </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop='allowedIPs' label='允许的IP地址列表' show-overflow-tooltip />
        <el-table-column prop='deniedIPs' label='拒绝的IP地址列表' show-overflow-tooltip />
        <el-table-column prop='rateLimit' label='速率限制（消息/秒）' show-overflow-tooltip />
        <el-table-column prop='loginAttempts' label='登录尝试次数' show-overflow-tooltip />
        <el-table-column prop='expireTime' label='过期时间' show-overflow-tooltip />
        <el-table-column prop='lastAuthTime' label='最后认证时间' show-overflow-tooltip />
        <el-table-column prop='lastLoginTime' label='最后登录时间' show-overflow-tooltip />
        <el-table-column prop='lastLoginIP' label='最后登录IP' show-overflow-tooltip />
        <el-table-column prop='successCount' label='认证成功次数' show-overflow-tooltip />
        <el-table-column prop='authSuccessCount' label='认证成功总数' show-overflow-tooltip />
        <el-table-column prop='failedCount' label='认证失败次数' show-overflow-tooltip />
        <el-table-column prop='isLocked' label='是否锁定（0:未锁定，1:已锁定）' show-overflow-tooltip>
          <template #default="scope">
            <el-tag v-if="scope.row.isLocked"> 是 </el-tag>
            <el-tag type="danger" v-else> 否 </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop='lockTime' label='锁定时间' show-overflow-tooltip />
        <el-table-column prop='unlockTime' label='解锁时间' show-overflow-tooltip />
        <el-table-column prop='lockReason' label='锁定原因' show-overflow-tooltip />
        <el-table-column prop='maxFailedCount' label='最大失败次数' show-overflow-tooltip />
        <el-table-column prop='lockDuration' label='锁定持续时间（分钟）' show-overflow-tooltip />
        <el-table-column prop='ipWhitelist' label='IP白名单' show-overflow-tooltip />
        <el-table-column prop='ipBlacklist' label='IP黑名单' show-overflow-tooltip />
        <el-table-column prop='allowedTopics' label='允许的主题列表' show-overflow-tooltip />
        <el-table-column prop='deniedTopics' label='拒绝的主题列表' show-overflow-tooltip />
        <el-table-column prop='maxConnections' label='最大连接数' show-overflow-tooltip />
        <el-table-column prop='currentConnections' label='当前连接数' show-overflow-tooltip />
        <el-table-column prop='connectionTimeout' label='连接超时时间（秒）' show-overflow-tooltip />
        <el-table-column prop='maxMessageSize' label='最大消息大小' show-overflow-tooltip />
        <el-table-column prop='maxSubscriptions' label='最大订阅数' show-overflow-tooltip />
        <el-table-column prop='messageRateLimit' label='消息速率限制' show-overflow-tooltip />
        <el-table-column prop='byteRateLimit' label='字节速率限制' show-overflow-tooltip />
        <el-table-column prop='extendedProperties' label='扩展属性（JSON）' show-overflow-tooltip />
        <el-table-column prop='key' label='' show-overflow-tooltip />
        <el-table-column prop='remark' label='备注' show-overflow-tooltip />
        <el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
          <template #default="scope">
            <ModifyRecord :data="scope.row" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right" show-overflow-tooltip v-if="auth('mqttClientAuth:update') || auth('mqttClientAuth:delete')">
          <template #default="scope">
            <div class="action-buttons-group">
              <el-tooltip content="编辑认证配置" placement="top">
                <el-button 
                  size="small" 
                  type="primary" 
                  :icon="Edit" 
                  circle 
                  @click="editDialogRef.openDialog(scope.row, '编辑MQTT 客户端认证信息表')" 
                  v-auth="'mqttClientAuth:update'"
                />
              </el-tooltip>
              <el-tooltip content="删除认证配置" placement="top">
                <el-button 
                  size="small" 
                  type="danger" 
                  :icon="Delete" 
                  circle 
                  @click="delMqttClientAuth(scope.row)" 
                  v-auth="'mqttClientAuth:delete'"
                />
              </el-tooltip>
              <el-tooltip content="查看详情" placement="top">
                <el-button 
                  size="small" 
                  type="info" 
                  icon="ele-View" 
                  circle 
                  @click="viewDetails(scope.row)"
                />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination 
              v-model:currentPage="state.tableParams.page"
              v-model:page-size="state.tableParams.pageSize"
              @size-change="(val: any) => handleQuery({ pageSize: val })"
              @current-change="(val: any) => handleQuery({ page: val })"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 50, 100, 200, 500]"
              :total="state.tableParams.total"
              size="small"
              background />
      <printDialog ref="printDialogRef" :title="'打印MQTT 客户端认证信息表'" @reloadTable="handleQuery" />
      <editDialog ref="editDialogRef" @reloadTable="handleQuery" />
    </el-card>
  </div>
</template>
<style scoped>
:deep(.el-input), :deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}

.mqtt-client-auth-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 12px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-icon {
  font-size: 48px;
  opacity: 0.9;
}

.header-text h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.header-text p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-stats {
  display: flex;
  gap: 30px;
}

.stat-card {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  min-width: 120px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.search-card, .advanced-search-card, .table-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: none;
}

.search-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.search-form {
  flex: 1;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.auth-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-buttons-group {
  display: flex;
  gap: 8px;
  justify-content: center;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background: #f8f9fa;
}

:deep(.el-table th) {
  background: #f8f9fa !important;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover > td) {
  background: #f5f7fa !important;
}

:deep(.el-pagination) {
  margin-top: 20px;
  justify-content: center;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .search-container {
    flex-direction: column;
  }
  
  .action-buttons {
    width: 100%;
    justify-content: center;
  }
  
  .header-stats {
    justify-content: center;
  }
}
</style>