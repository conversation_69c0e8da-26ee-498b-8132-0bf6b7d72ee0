# MQTT管理中台产品需求文档

## 1. 产品概述

本项目是一个现代化的MQTT管理中台，为企业提供统一的MQTT服务管理、设备连接监控、消息流转控制和数据可视化分析能力。

- 解决企业在物联网场景下MQTT服务分散管理、缺乏统一监控和数据分析的问题，为运维人员和开发者提供一站式MQTT管理解决方案。
- 目标是成为企业级MQTT服务的统一管控平台，提升运维效率，降低管理成本。

## 2. 核心功能

### 2.1 用户角色

| 角色 | 注册方式 | 核心权限 |
|------|----------|----------|
| 系统管理员 | 系统预设账号 | 全平台管理权限，包括用户管理、系统配置等 |
| 运维管理员 | 管理员邀请注册 | MQTT实例管理、监控告警、日志查看等 |
| 开发者 | 邀请码注册 | 客户端管理、主题订阅发布、测试工具使用等 |
| 普通用户 | 申请审批注册 | 基础查看权限，设备状态查询等 |

### 2.2 功能模块

本MQTT管理中台包含以下核心页面：

1. **仪表板页面**：实时监控概览、关键指标展示、告警信息汇总
2. **MQTT实例管理页面**：实例配置、连接管理、状态监控
3. **客户端管理页面**：设备连接管理、认证配置、连接状态监控
4. **主题管理页面**：主题配置、权限控制、消息流转规则
5. **设备组管理页面**：设备分组、批量操作、组织架构管理
6. **实时监控页面**：连接状态、消息流量、性能指标实时展示
7. **消息测试页面**：MQTTX在线测试工具、消息发布订阅测试
8. **日志审计页面**：操作日志、连接日志、消息日志查询分析
9. **系统配置页面**：平台参数配置、告警规则设置、用户权限管理

### 2.3 页面详情

| 页面名称 | 模块名称 | 功能描述 |
|----------|----------|----------|
| 仪表板页面 | 实时概览 | 显示MQTT实例总数、在线设备数、消息吞吐量等关键指标，支持时间范围筛选 |
| 仪表板页面 | 告警中心 | 展示系统告警信息、告警级别分类、快速处理入口 |
| 仪表板页面 | 性能图表 | 实时展示连接数趋势、消息量趋势、系统资源使用情况 |
| MQTT实例管理页面 | 实例列表 | 分页查询MQTT实例、支持多条件筛选、批量操作 |
| MQTT实例管理页面 | 实例配置 | 新增编辑MQTT实例、连接参数配置、SSL/WebSocket设置 |
| MQTT实例管理页面 | 连接测试 | 实时连接状态检测、连接参数验证、故障诊断 |
| 客户端管理页面 | 客户端列表 | 设备连接信息管理、在线状态监控、连接历史查询 |
| 客户端管理页面 | 认证管理 | 客户端认证配置、密钥管理、权限控制 |
| 客户端管理页面 | 批量操作 | 批量连接断开、批量配置更新、批量导入导出 |
| 主题管理页面 | 主题配置 | 主题创建编辑、QoS设置、保留消息配置 |
| 主题管理页面 | 权限控制 | 发布订阅权限设置、访问控制列表管理 |
| 主题管理页面 | 消息监控 | 主题消息流量统计、消息内容预览、异常监控 |
| 设备组管理页面 | 分组管理 | 设备分组创建、层级结构管理、组织架构配置 |
| 设备组管理页面 | 批量管理 | 按组批量操作设备、组级别权限控制、统计分析 |
| 实时监控页面 | 连接监控 | 实时连接状态展示、连接数变化趋势、异常连接告警 |
| 实时监控页面 | 消息监控 | 实时消息流量监控、消息类型分析、热点主题统计 |
| 实时监控页面 | 性能监控 | 系统性能指标监控、资源使用率、响应时间统计 |
| 消息测试页面 | 连接测试 | MQTT连接参数配置、连接状态验证、协议版本选择 |
| 消息测试页面 | 发布订阅 | 消息发布测试、主题订阅测试、QoS级别验证 |
| 消息测试页面 | 消息历史 | 测试消息历史记录、消息内容查看、测试结果分析 |
| 日志审计页面 | 操作日志 | 用户操作记录查询、操作类型筛选、日志导出 |
| 日志审计页面 | 连接日志 | 设备连接断开日志、连接异常分析、统计报表 |
| 日志审计页面 | 消息日志 | 消息发布订阅日志、消息内容审计、合规性检查 |
| 系统配置页面 | 基础配置 | 系统参数设置、默认值配置、功能开关控制 |
| 系统配置页面 | 告警配置 | 告警规则设置、通知方式配置、告警级别定义 |
| 系统配置页面 | 用户管理 | 用户账号管理、角色权限分配、访问控制设置 |

## 3. 核心流程

### 管理员流程
管理员登录系统后，首先查看仪表板获取系统整体状况，然后进入MQTT实例管理配置和监控各个MQTT服务实例，通过客户端管理页面管理设备连接和认证，在主题管理页面配置消息路由规则，最后通过实时监控页面持续监控系统运行状态。

### 开发者流程
开发者登录后查看分配的MQTT实例和客户端权限，在客户端管理页面配置设备连接参数，通过主题管理页面设置消息订阅发布规则，使用消息测试页面验证连接和消息传输，查看日志审计页面排查问题。

### 运维流程
运维人员重点关注仪表板的告警信息和性能指标，通过实时监控页面监控系统运行状态，在发现异常时查看日志审计页面分析问题原因，通过MQTT实例管理页面进行故障处理和配置调整。

```mermaid
graph TD
    A[登录页面] --> B[仪表板页面]
    B --> C[MQTT实例管理]
    B --> D[客户端管理]
    B --> E[主题管理]
    B --> F[设备组管理]
    B --> G[实时监控]
    B --> H[消息测试]
    B --> I[日志审计]
    B --> J[系统配置]
    C --> K[实例详情]
    D --> L[客户端详情]
    E --> M[主题详情]
    G --> N[监控详情]
    H --> O[测试结果]
```

## 4. 用户界面设计

### 4.1 设计风格

- **主色调**：深蓝色(#1890ff)作为主色，浅灰色(#f0f2f5)作为背景色
- **按钮样式**：圆角按钮设计，支持悬停和点击状态变化
- **字体**：主要使用14px微软雅黑，标题使用16px-20px加粗
- **布局风格**：左侧导航+顶部面包屑的经典后台布局，卡片式内容展示
- **图标风格**：使用Element Plus图标库，配合自定义MQTT相关图标

### 4.2 页面设计概览

| 页面名称 | 模块名称 | UI元素 |
|----------|----------|--------|
| 仪表板页面 | 实时概览 | 数据卡片展示关键指标，使用ECharts图表展示趋势，颜色编码表示状态 |
| 仪表板页面 | 告警中心 | 告警列表表格，颜色区分告警级别，支持快速筛选和处理 |
| MQTT实例管理页面 | 实例列表 | 表格展示实例信息，状态标签显示连接状态，操作按钮支持快速操作 |
| MQTT实例管理页面 | 实例配置 | 表单布局配置参数，分组展示不同类型配置，实时验证输入 |
| 客户端管理页面 | 客户端列表 | 表格展示设备信息，在线状态图标，支持搜索和高级筛选 |
| 实时监控页面 | 连接监控 | 实时更新的图表和数据，使用WebSocket推送最新数据 |
| 消息测试页面 | 发布订阅 | 分栏布局，左侧配置右侧结果，代码编辑器展示消息内容 |
| 日志审计页面 | 日志查询 | 时间选择器+条件筛选+表格展示，支持日志详情弹窗查看 |

### 4.3 响应式设计

产品采用桌面优先设计，支持平板和大屏适配。在移动端提供基础的查看功能，复杂操作建议在桌面端完成。支持触摸操作优化，确保在触屏设备上的良好体验。