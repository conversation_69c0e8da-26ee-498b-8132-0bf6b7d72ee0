﻿<script lang="ts" name="mqttClient" setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import type { FormRules } from "element-plus";
import { formatDate } from '/@/utils/formatTime';
import { useMqttClientApi } from '/@/api/mqtt_client/mqttClient';

//父级传递来的函数，用于回调
const emit = defineEmits(["reloadTable"]);
const mqttClientApi = useMqttClientApi();
const ruleFormRef = ref();

const state = reactive({
	title: '',
	loading: false,
	showDialog: false,
	ruleForm: {} as any,
	stores: {},
	dropdownData: {} as any,
});

// 自行添加其他规则
const rules = ref<FormRules>({
});

// 页面加载时
onMounted(async () => {
});

// 打开弹窗
const openDialog = async (row: any, title: string) => {
	state.title = title;
	row = row ?? {  };
	state.ruleForm = row.id ? await mqttClientApi.detail(row.id).then(res => res.data.result) : JSON.parse(JSON.stringify(row));
	state.showDialog = true;
};

// 关闭弹窗
const closeDialog = () => {
	emit("reloadTable");
	state.showDialog = false;
};

// 提交
const submit = async () => {
	ruleFormRef.value.validate(async (isValid: boolean, fields?: any) => {
		if (isValid) {
			let values = state.ruleForm;
			await mqttClientApi[state.ruleForm.id ? 'update' : 'add'](values);
			closeDialog();
		} else {
			ElMessage({
				message: `表单有${Object.keys(fields).length}处验证失败，请修改后再提交`,
				type: "error",
			});
		}
	});
};

//将属性或者函数暴露给父组件
defineExpose({ openDialog });
</script>
<template>
	<div class="mqttClient-container">
		<el-dialog v-model="state.showDialog" :width="800" draggable :close-on-click-modal="false">
			<template #header>
				<div style="color: #fff">
					<span>{{ state.title }}</span>
				</div>
			</template>
			<el-form :model="state.ruleForm" ref="ruleFormRef" label-width="auto" :rules="rules">
				<el-row :gutter="35">
					<el-form-item v-show="false">
						<el-input v-model="state.ruleForm.id" />
					</el-form-item>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="实例ID" prop="instanceId">
							<el-input v-model="state.ruleForm.instanceId" placeholder="请输入实例ID" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="客户端ID" prop="clientId">
							<el-input v-model="state.ruleForm.clientId" placeholder="请输入客户端ID" maxlength="128" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="设备名称" prop="deviceName">
							<el-input v-model="state.ruleForm.deviceName" placeholder="请输入设备名称" maxlength="64" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="设备组ID" prop="groupId">
							<el-input v-model="state.ruleForm.groupId" placeholder="请输入设备组ID" maxlength="64" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="产品Key" prop="productKey">
							<el-input v-model="state.ruleForm.productKey" placeholder="请输入产品Key" maxlength="64" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="设备密钥（加密存储）" prop="deviceSecret">
							<el-input v-model="state.ruleForm.deviceSecret" placeholder="请输入设备密钥（加密存储）" maxlength="128" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="IP地址" prop="ipAddress">
							<el-input v-model="state.ruleForm.ipAddress" placeholder="请输入IP地址" maxlength="45" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="端口" prop="port">
							<el-input-number v-model="state.ruleForm.port" placeholder="请输入端口" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="连接状态" prop="status">
							<el-input v-model="state.ruleForm.status" placeholder="请输入连接状态" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="协议版本" prop="protocolVersion">
							<el-input v-model="state.ruleForm.protocolVersion" placeholder="请输入协议版本" maxlength="16" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="心跳间隔（秒）" prop="keepAlive">
							<el-input-number v-model="state.ruleForm.keepAlive" placeholder="请输入心跳间隔（秒）" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="清除会话（0:否，1:是）" prop="cleanSession">
							<el-switch v-model="state.ruleForm.cleanSession" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="连接时间" prop="connectedAt">
							<el-date-picker v-model="state.ruleForm.connectedAt" type="date" placeholder="连接时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="断开时间" prop="disconnectedAt">
							<el-date-picker v-model="state.ruleForm.disconnectedAt" type="date" placeholder="断开时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最后活动时间" prop="lastActivity">
							<el-date-picker v-model="state.ruleForm.lastActivity" type="date" placeholder="最后活动时间" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="发送消息数" prop="messagesSent">
							<el-input v-model="state.ruleForm.messagesSent" placeholder="请输入发送消息数" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="接收消息数" prop="messagesReceived">
							<el-input v-model="state.ruleForm.messagesReceived" placeholder="请输入接收消息数" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="发送字节数" prop="bytesSent">
							<el-input v-model="state.ruleForm.bytesSent" placeholder="请输入发送字节数" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="接收字节数" prop="bytesReceived">
							<el-input v-model="state.ruleForm.bytesReceived" placeholder="请输入接收字节数" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="订阅数量" prop="subscriptionCount">
							<el-input-number v-model="state.ruleForm.subscriptionCount" placeholder="请输入订阅数量" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最大订阅数" prop="maxSubscriptions">
							<el-input-number v-model="state.ruleForm.maxSubscriptions" placeholder="请输入最大订阅数" clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="设备类型" prop="deviceType">
							<el-input v-model="state.ruleForm.deviceType" placeholder="请输入设备类型" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="设备版本" prop="deviceVersion">
							<el-input v-model="state.ruleForm.deviceVersion" placeholder="请输入设备版本" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="设备标签（JSON）" prop="deviceTags">
							<el-input v-model="state.ruleForm.deviceTags" placeholder="请输入设备标签（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="位置信息（JSON）" prop="locationInfo">
							<el-input v-model="state.ruleForm.locationInfo" placeholder="请输入位置信息（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否在线（0:离线，1:在线）" prop="isOnline">
							<el-switch v-model="state.ruleForm.isOnline" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="是否启用（0:否，1:是）" prop="isEnabled">
							<el-switch v-model="state.ruleForm.isEnabled" />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="认证模式" prop="authMode">
							<el-input v-model="state.ruleForm.authMode" placeholder="请输入认证模式" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="客户端类型" prop="clientType">
							<el-input v-model="state.ruleForm.clientType" placeholder="请输入客户端类型" maxlength="32" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="最后错误信息" prop="lastError">
							<el-input v-model="state.ruleForm.lastError" placeholder="请输入最后错误信息" maxlength="500" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="扩展属性（JSON）" prop="extendedProperties">
							<el-input v-model="state.ruleForm.extendedProperties" placeholder="请输入扩展属性（JSON）" maxlength="255" show-word-limit clearable />
						</el-form-item>
					</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20" >
						<el-form-item label="备注" prop="remark">
							<el-input v-model="state.ruleForm.remark" placeholder="请输入备注" maxlength="500" show-word-limit clearable />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="() => state.showDialog = false">取 消</el-button>
					<el-button @click="submit" type="primary" v-reclick="1000">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>
<style lang="scss" scoped>
:deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>