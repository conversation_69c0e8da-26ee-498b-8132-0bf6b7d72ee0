﻿import {useBaseApi} from '/@/api/base';

// MQTT设备组管理表接口服务
export const useMqttDeviceGroupApi = () => {
	const baseApi = useBaseApi("mqttDeviceGroup");
	return {
		// 分页查询MQTT设备组管理表
		page: baseApi.page,
		// 查看MQTT设备组管理表详细
		detail: baseApi.detail,
		// 新增MQTT设备组管理表
		add: baseApi.add,
		// 更新MQTT设备组管理表
		update: baseApi.update,
		// 删除MQTT设备组管理表
		delete: baseApi.delete,
		// 批量删除MQTT设备组管理表
		batchDelete: baseApi.batchDelete,
		// 导出MQTT设备组管理表数据
		exportData: baseApi.exportData,
		// 导入MQTT设备组管理表数据
		importData: baseApi.importData,
		// 下载MQTT设备组管理表数据导入模板
		downloadTemplate: baseApi.downloadTemplate,
	}
}

// MQTT设备组管理表实体
export interface MqttDeviceGroup {
	// 主键Id
	id: number;
	// 实例ID
	instanceId?: number;
	// 设备组标识（如：GID_TestGroup）
	groupId?: string;
	// 设备组名称
	groupName?: string;
	// 设备组类型（Production、Test、Development等）
	groupType?: string;
	// 产品Key（阿里云IoT产品标识）
	productKey?: string;
	// 设备组描述
	description: string;
	// 最大设备数量
	maxDeviceCount?: number;
	// 当前设备数量
	currentDeviceCount?: number;
	// 允许的主题模式（JSON数组）
	allowedTopicPatterns: string;
	// 禁止的主题模式（JSON数组）
	deniedTopicPatterns: string;
	// 默认QoS等级（0,1,2）
	defaultQosLevel?: boolean;
	// 最大消息大小（字节）
	maxMessageSize?: number;
	// 消息速率限制（消息/秒）
	messageRateLimit?: number;
	// 字节速率限制（字节/秒）
	byteRateLimit?: number;
	// 最大连接数
	maxConnections?: number;
	// 连接超时时间（秒）
	connectionTimeout?: number;
	// 心跳超时时间（秒）
	keepAliveTimeout?: number;
	// 是否允许保留消息
	enableRetainMessage?: boolean;
	// 是否允许通配符订阅
	enableWildcardSubscription?: boolean;
	// 是否允许共享订阅
	enableSharedSubscription?: boolean;
	// IP白名单（JSON数组）
	ipWhitelist: string;
	// IP黑名单（JSON数组）
	ipBlacklist: string;
	// 允许连接的时间范围（JSON）
	allowedTimeRanges: string;
	// 安全级别（Low、Medium、High）
	securityLevel?: string;
	// 是否要求加密连接
	encryptionRequired?: boolean;
	// 是否要求客户端证书
	certificateRequired?: boolean;
	// 认证模式（Signature、Username、JWT、Certificate）
	authMode?: string;
	// 签名算法（hmacsha1、hmacsha256、hmacmd5）
	signMethod?: string;
	// AccessKey ID（阿里云认证）
	accessKeyId?: string;
	// AccessKey Secret（加密存储）
	accessKeySecret?: string;
	// JWT密钥
	jwtSecret: string;
	// JWT过期时间（秒）
	jwtExpiry: number;
	// CA证书
	caCertificate: string;
	// 是否启用
	isEnabled?: boolean;
	// 过期时间
	expireTime: string;
	// 最后活动时间
	lastActivity: string;
	// 已创建设备数
	createdDeviceCount?: number;
	// 在线设备数
	onlineDeviceCount?: number;
	// 总消息数
	totalMessageCount?: number;
	// 总字节数
	totalByteCount?: number;
	// 最后消息时间
	lastMessageTime: string;
	// 告警规则（JSON）
	alertRules: string;
	// 监控配置（JSON）
	monitorConfig: string;
	// 扩展属性（JSON）
	extendedProperties: string;
	// 标签（JSON）
	tags: string;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 修改者Id
	updateUserId: number;
	// 软删除
	isDelete?: boolean;
	// 创建者姓名
	createUserName: string;
	// 修改者姓名
	updateUserName: string;
}