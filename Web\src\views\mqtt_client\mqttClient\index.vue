<script lang="ts" setup name="mqttClient">
import { ref, reactive, onMounted } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { downloadStreamFile } from "/@/utils/download";
import { useMqttClientApi } from '/@/api/mqtt_client/mqttClient';
import { useEmqxRealtimeApi } from '/@/api/emqxRealtime/emqxRealtime';
import editDialog from '/@/views/mqtt_client/mqttClient/component/editDialog.vue'
import printDialog from '/@/views/system/print/component/hiprint/preview.vue'
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import {
  Search,
  Plus,
  Delete,
  Edit,
  Refresh,
  Download,
  Upload,
  Connection,
  SwitchButton,
  Monitor,
  View,
  Warning,
  CircleCheck,
  CircleClose,
  Link,
  Unlock
} from '@element-plus/icons-vue';

const mqttClientApi = useMqttClientApi();
const emqxRealtimeApi = useEmqxRealtimeApi();
const printDialogRef = ref();
const editDialogRef = ref();
const state = reactive({
  exportLoading: false,
  tableLoading: false,
  stores: {},
  showAdvanceQueryUI: false,
  dropdownData: {} as any,
  selectData: [] as any[],
  tableQueryParams: {} as any,
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 0,
    field: 'createTime', // 默认的排序字段
    order: 'descending', // 排序方向
    descStr: 'descending', // 降序排序的关键字符
  },
  tableData: [],
  // 新增实时连接状态相关
  connectionStatus: new Map(),
  testingConnections: new Set(),
  refreshingStatus: false,
});

// 页面加载时
onMounted(async () => {
});

// 查询操作
const handleQuery = async (params: any = {}) => {
  state.tableLoading = true;
  state.tableParams = Object.assign(state.tableParams, params);
  const result = await mqttClientApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then(res => res.data.result);
  state.tableParams.total = result?.total;
  state.tableData = result?.items ?? [];
  state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};

// 删除
const delMqttClient = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await mqttClientApi.delete({ id: row.id });
    handleQuery();
    ElMessage.success("删除成功");
  }).catch(() => {});
};

// 批量删除
const batchDelMqttClient = () => {
  ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await mqttClientApi.batchDelete(state.selectData.map(u => ({ id: u.id }) )).then(res => {
      ElMessage.success(`成功批量删除${res.data.result}条记录`);
      handleQuery();
    });
  }).catch(() => {});
};

// 测试客户端连接
const testClientConnection = async (row: any) => {
  if (!row.instanceId || !row.clientId) {
    ElMessage.warning('实例ID或客户端ID不能为空');
    return;
  }
  
  state.testingConnections.add(row.id);
  try {
    const result = await emqxRealtimeApi.connect({ instanceId: row.instanceId });
    if (result.data.success) {
      ElMessage.success('连接测试成功');
      await getClientConnectionStatus(row);
    } else {
      ElMessage.error(`连接测试失败: ${result.data.message}`);
    }
  } catch (error) {
    console.error('连接测试失败:', error);
    ElMessage.error('连接测试失败');
  } finally {
    state.testingConnections.delete(row.id);
  }
};

// 断开客户端连接
const disconnectClient = async (row: any) => {
  if (!row.instanceId) {
    ElMessage.warning('实例ID不能为空');
    return;
  }
  
  try {
    const result = await emqxRealtimeApi.disconnect({ instanceId: row.instanceId });
    if (result.data.success) {
      ElMessage.success('断开连接成功');
      await getClientConnectionStatus(row);
    } else {
      ElMessage.error(`断开连接失败: ${result.data.message}`);
    }
  } catch (error) {
    console.error('断开连接失败:', error);
    ElMessage.error('断开连接失败');
  }
};

// 获取客户端连接状态
const getClientConnectionStatus = async (row: any) => {
  if (!row.instanceId) return;
  
  try {
    const result = await emqxRealtimeApi.getConnectionStatus({ instanceId: row.instanceId });
    if (result.data) {
      state.connectionStatus.set(row.id, {
        isConnected: result.data.isConnected,
        status: result.data.status,
        checkTime: result.data.checkTime
      });
    }
  } catch (error) {
    console.error('获取连接状态失败:', error);
  }
};

// 批量连接客户端
const batchConnectClients = async () => {
  if (state.selectData.length === 0) {
    ElMessage.warning('请选择要连接的客户端');
    return;
  }
  
  const instanceIds = state.selectData
    .filter(item => item.instanceId)
    .map(item => item.instanceId);
    
  if (instanceIds.length === 0) {
    ElMessage.warning('所选客户端中没有有效的实例ID');
    return;
  }
  
  try {
    const result = await emqxRealtimeApi.batchConnect({ instanceIds });
    if (result.data.success) {
      ElMessage.success(`批量连接操作完成，成功连接 ${result.data.successCount} 个实例`);
      await refreshAllConnectionStatus();
    } else {
      ElMessage.error(`批量连接失败: ${result.data.message}`);
    }
  } catch (error) {
    console.error('批量连接失败:', error);
    ElMessage.error('批量连接失败');
  }
};

// 批量断开客户端
const batchDisconnectClients = async () => {
  if (state.selectData.length === 0) {
    ElMessage.warning('请选择要断开的客户端');
    return;
  }
  
  const instanceIds = state.selectData
    .filter(item => item.instanceId)
    .map(item => item.instanceId);
    
  if (instanceIds.length === 0) {
    ElMessage.warning('所选客户端中没有有效的实例ID');
    return;
  }
  
  try {
    const result = await emqxRealtimeApi.batchDisconnect({ instanceIds });
    if (result.data.success) {
      ElMessage.success(`批量断开操作完成，成功断开 ${result.data.successCount} 个实例`);
      await refreshAllConnectionStatus();
    } else {
      ElMessage.error(`批量断开失败: ${result.data.message}`);
    }
  } catch (error) {
    console.error('批量断开失败:', error);
    ElMessage.error('批量断开失败');
  }
};

// 刷新所有连接状态
const refreshAllConnectionStatus = async () => {
  state.refreshingStatus = true;
  try {
    for (const row of state.tableData) {
      if (row.instanceId) {
        await getClientConnectionStatus(row);
      }
    }
    ElMessage.success('连接状态刷新完成');
  } catch (error) {
    console.error('刷新连接状态失败:', error);
    ElMessage.error('刷新连接状态失败');
  } finally {
    state.refreshingStatus = false;
  }
};

// 获取连接状态显示文本
const getConnectionStatusText = (rowId: string) => {
  const status = state.connectionStatus.get(rowId);
  if (!status) return '未知';
  return status.isConnected ? '已连接' : '已断开';
};

// 获取连接状态标签类型
const getConnectionStatusType = (rowId: string) => {
  const status = state.connectionStatus.get(rowId);
  if (!status) return 'info';
  return status.isConnected ? 'success' : 'danger';
};

// 获取连接状态图标
const getConnectionStatusIcon = (rowId: string) => {
  const status = state.connectionStatus.get(rowId);
  if (!status) return Warning;
  return status.isConnected ? CircleCheck : CircleClose;
};

// 获取在线客户端数量
const getOnlineClientCount = () => {
  return state.tableData.filter((item: any) => item.isOnline).length;
};

// 获取离线客户端数量
const getOfflineClientCount = () => {
  return state.tableData.filter((item: any) => !item.isOnline).length;
};

// 获取设备类型颜色
const getDeviceTypeColor = (deviceType: string) => {
  const typeMap: Record<string, string> = {
    'sensor': 'success',
    'gateway': 'warning',
    'controller': 'primary',
    'monitor': 'info'
  };
  return typeMap[deviceType] || 'info';
};

// 获取设备类型图标
const getDeviceTypeIcon = (deviceType: string) => {
  const iconMap: Record<string, any> = {
    'sensor': Monitor,
    'gateway': Link,
    'controller': Connection,
    'monitor': View
  };
  return iconMap[deviceType] || Monitor;
};

// 获取设备类型标签
const getDeviceTypeLabel = (deviceType: string) => {
  const labelMap: Record<string, string> = {
    'sensor': '传感器',
    'gateway': '网关',
    'controller': '控制器',
    'monitor': '监控设备'
  };
  return labelMap[deviceType] || deviceType || '未知';
};

// 获取认证模式颜色
const getAuthModeColor = (authMode: string) => {
  const modeMap: Record<string, string> = {
    'username': 'primary',
    'certificate': 'success',
    'token': 'warning'
  };
  return modeMap[authMode] || 'info';
};

// 获取认证模式图标
const getAuthModeIcon = (authMode: string) => {
  const iconMap: Record<string, any> = {
    'username': Unlock,
    'certificate': Connection,
    'token': Link
  };
  return iconMap[authMode] || Unlock;
};

// 获取认证模式标签
const getAuthModeLabel = (authMode: string) => {
  const labelMap: Record<string, string> = {
    'username': '用户名密码',
    'certificate': '证书认证',
    'token': 'Token认证'
  };
  return labelMap[authMode] || authMode || '未知';
};

// 查看客户端详情
const viewClientDetails = (row: any) => {
  const details = `
    <div style="text-align: left; line-height: 1.8;">
      <p><strong>客户端ID:</strong> ${row.clientId || '-'}</p>
      <p><strong>设备名称:</strong> ${row.deviceName || '-'}</p>
      <p><strong>实例ID:</strong> ${row.instanceId || '-'}</p>
      <p><strong>IP地址:</strong> ${row.ipAddress || '-'}</p>
      <p><strong>端口:</strong> ${row.port || '-'}</p>
      <p><strong>协议版本:</strong> ${row.protocolVersion || '-'}</p>
      <p><strong>认证模式:</strong> ${getAuthModeLabel(row.authMode)}</p>
      <p><strong>设备类型:</strong> ${getDeviceTypeLabel(row.deviceType)}</p>
      <p><strong>连接时间:</strong> ${row.connectedAt || '-'}</p>
      <p><strong>最后活动:</strong> ${row.lastActivity || '-'}</p>
      <p><strong>发送消息数:</strong> ${row.messagesSent || 0}</p>
      <p><strong>接收消息数:</strong> ${row.messagesReceived || 0}</p>
      <p><strong>订阅数量:</strong> ${row.subscriptionCount || 0}</p>
      <p><strong>备注:</strong> ${row.remark || '-'}</p>
    </div>
  `;
  
  ElMessageBox.alert(details, '客户端详情', {
    dangerouslyUseHTMLString: true,
    confirmButtonText: '确定'
  });
};

// 重置搜索
const resetSearch = () => {
  state.tableQueryParams = {};
  handleQuery();
};

// 导出Excel
const onExportExcel = async () => {
  try {
    state.exportLoading = true;
    const result = await mqttClientApi.exportMqttClient(state.tableQueryParams);
    downloadStreamFile(result, 'MQTT客户端数据.xlsx');
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  } finally {
    state.exportLoading = false;
  }
};

handleQuery();

// 页面加载完成后刷新连接状态
onMounted(() => {
  setTimeout(() => {
    refreshAllConnectionStatus();
  }, 1000);
});
</script>

<template>
  <div class="mqtt-client-container" v-loading="state.exportLoading">
    <!-- 页面头部统计 -->
    <div class="page-header">
      <div class="header-title">
        <h2>MQTT 客户端管理</h2>
        <p>管理和监控 MQTT 客户端连接状态</p>
      </div>
      <div class="header-stats">
        <div class="stat-card">
          <div class="stat-icon online">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ getOnlineClientCount() }}</div>
            <div class="stat-label">在线客户端</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon offline">
            <el-icon><CircleClose /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ getOfflineClientCount() }}</div>
            <div class="stat-label">离线客户端</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon total">
            <el-icon><Connection /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ state.tableParams.total || 0 }}</div>
            <div class="stat-label">总客户端数</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="hover">
      <el-form :model="state.tableQueryParams" ref="queryForm" label-width="90px">
        <el-row :gutter="16">
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <el-form-item label="关键字">
              <el-input 
                v-model="state.tableQueryParams.keyword" 
                clearable 
                placeholder="搜索客户端ID、设备名称等"
                :prefix-icon="Search"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <el-form-item label="实例ID">
              <el-input 
                v-model="state.tableQueryParams.instanceId" 
                clearable 
                placeholder="请输入实例ID"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <el-form-item label="连接状态">
              <el-select 
                v-model="state.tableQueryParams.isOnline" 
                clearable 
                placeholder="选择连接状态"
                style="width: 100%"
              >
                <el-option 
                  :icon="CircleCheck" 
                  value="true" 
                  label="在线" 
                />
                <el-option 
                  :icon="CircleClose" 
                  value="false" 
                  label="离线" 
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <el-form-item>
              <div class="search-actions">
                <el-button 
                  type="primary" 
                  :icon="Search" 
                  @click="handleQuery" 
                  v-auth="'mqttClient:page'"
                >
                  查询
                </el-button>
                <el-button 
                  :icon="Refresh" 
                  @click="resetSearch"
                >
                  重置
                </el-button>
                <el-button 
                  :icon="state.showAdvanceQueryUI ? 'ele-ZoomOut' : 'ele-ZoomIn'" 
                  @click="() => state.showAdvanceQueryUI = !state.showAdvanceQueryUI"
                  text
                >
                  {{ state.showAdvanceQueryUI ? '收起' : '展开' }}
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 高级搜索区域 -->
    <el-card v-if="state.showAdvanceQueryUI" class="advanced-search-card" shadow="hover">
      <template #header>
        <span>高级搜索</span>
      </template>


      <el-form :model="state.tableQueryParams" label-width="120px">
        <el-row :gutter="16">
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <el-form-item label="客户端ID">
              <el-input 
                v-model="state.tableQueryParams.clientId" 
                clearable 
                placeholder="请输入客户端ID"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <el-form-item label="设备名称">
              <el-input 
                v-model="state.tableQueryParams.deviceName" 
                clearable 
                placeholder="请输入设备名称"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <el-form-item label="设备组ID">
              <el-input 
                v-model="state.tableQueryParams.groupId" 
                clearable 
                placeholder="请输入设备组ID"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <el-form-item label="设备类型">
              <el-select 
                v-model="state.tableQueryParams.deviceType" 
                clearable 
                placeholder="选择设备类型"
                style="width: 100%"
              >
                <el-option value="sensor" label="传感器" />
                <el-option value="gateway" label="网关" />
                <el-option value="controller" label="控制器" />
                <el-option value="monitor" label="监控设备" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <el-form-item label="认证模式">
              <el-select 
                v-model="state.tableQueryParams.authMode" 
                clearable 
                placeholder="选择认证模式"
                style="width: 100%"
              >
                <el-option value="username" label="用户名密码" />
                <el-option value="certificate" label="证书认证" />
                <el-option value="token" label="Token认证" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <el-form-item label="IP地址">
              <el-input 
                v-model="state.tableQueryParams.ipAddress" 
                clearable 
                placeholder="请输入IP地址"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <el-form-item label="协议版本">
              <el-select 
                v-model="state.tableQueryParams.protocolVersion" 
                clearable 
                placeholder="选择协议版本"
                style="width: 100%"
              >
                <el-option value="3.1" label="MQTT 3.1" />
                <el-option value="3.1.1" label="MQTT 3.1.1" />
                <el-option value="5.0" label="MQTT 5.0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
            <el-form-item label="启用状态">
              <el-select 
                v-model="state.tableQueryParams.isEnabled" 
                clearable 
                placeholder="选择启用状态"
                style="width: 100%"
              >
                <el-option 
                  :icon="CircleCheck" 
                  value="true" 
                  label="已启用" 
                />
                <el-option 
                  :icon="CircleClose" 
                  value="false" 
                  label="已禁用" 
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="action-buttons" shadow="hover">
      <div class="button-group">
        <div class="left-buttons">
          <el-button 
            type="primary" 
            :icon="Plus" 
            @click="editDialogRef.openDialog(null, '新增MQTT客户端')" 
            v-auth="'mqttClient:add'"
          >
            新增客户端
          </el-button>
          <el-button 
            type="danger" 
            :icon="Delete" 
            @click="batchDelMqttClient" 
            :disabled="state.selectData.length === 0" 
            v-auth="'mqttClient:batchDelete'"
          >
            批量删除
          </el-button>
          <el-button 
            type="success" 
            :icon="Connection" 
            @click="batchConnectClients" 
            :disabled="state.selectData.length === 0"
          >
            批量连接
          </el-button>
          <el-button 
            type="warning" 
            :icon="SwitchButton" 
            @click="batchDisconnectClients" 
            :disabled="state.selectData.length === 0"
          >
            批量断开
          </el-button>
        </div>
        <div class="right-buttons">
          <el-button 
            :icon="Refresh" 
            @click="refreshAllConnectionStatus" 
            :loading="state.refreshingStatus"
          >
            刷新状态
          </el-button>
          <el-button 
            :icon="Download" 
            @click="onExportExcel"
          >
            导出数据
          </el-button>
        </div>
      </div>
    </el-card>
    <!-- 数据表格 -->
    <el-card class="table-card" shadow="hover">
      <el-table 
        :data="state.tableData" 
        @selection-change="(val: any[]) => { state.selectData = val; }" 
        style="width: 100%" 
        v-loading="state.tableLoading" 
        tooltip-effect="light" 
        row-key="id" 
        @sort-change="sortChange" 
        border
        stripe
      >
        <el-table-column 
          type="selection" 
          width="50" 
          align="center" 
          v-if="auth('mqttClient:batchDelete') || auth('mqttClient:export')" 
        />
        <el-table-column 
          type="index" 
          label="序号" 
          width="60" 
          align="center"
        />
        <el-table-column 
          prop="instanceId" 
          label="实例ID" 
          width="120" 
          show-overflow-tooltip 
        />
        <el-table-column 
          prop="clientId" 
          label="客户端ID" 
          width="150" 
          show-overflow-tooltip 
        />
        <el-table-column 
          prop="deviceName" 
          label="设备名称" 
          width="120" 
          show-overflow-tooltip 
        />
        <el-table-column 
          label="连接状态" 
          width="100" 
          align="center"
        >
          <template #default="scope">
            <el-tag 
              :type="getConnectionStatusType(scope.row.id)" 
              :icon="getConnectionStatusIcon(scope.row.id)"
              size="small"
            >
              {{ getConnectionStatusText(scope.row.id) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column 
          label="在线状态" 
          width="100" 
          align="center"
        >
          <template #default="scope">
            <el-tag 
              :type="scope.row.isOnline ? 'success' : 'danger'" 
              :icon="scope.row.isOnline ? CircleCheck : CircleClose"
              size="small"
            >
              {{ scope.row.isOnline ? '在线' : '离线' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column 
          label="设备类型" 
          width="100" 
          align="center"
        >
          <template #default="scope">
            <el-tag 
              :type="getDeviceTypeColor(scope.row.deviceType)" 
              :icon="getDeviceTypeIcon(scope.row.deviceType)"
              size="small"
            >
              {{ getDeviceTypeLabel(scope.row.deviceType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column 
          prop="ipAddress" 
          label="IP地址" 
          width="120" 
          show-overflow-tooltip 
        />
        <el-table-column 
          prop="port" 
          label="端口" 
          width="80" 
          align="center" 
        />
        <el-table-column 
          prop="protocolVersion" 
          label="协议版本" 
          width="100" 
          align="center" 
        />
        <el-table-column 
          label="认证模式" 
          width="100" 
          align="center"
        >
          <template #default="scope">
            <el-tag 
              :type="getAuthModeColor(scope.row.authMode)" 
              :icon="getAuthModeIcon(scope.row.authMode)"
              size="small"
            >
              {{ getAuthModeLabel(scope.row.authMode) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column 
          prop="connectedAt" 
          label="连接时间" 
          width="150" 
          show-overflow-tooltip 
        />
        <el-table-column 
          prop="lastActivity" 
          label="最后活动" 
          width="150" 
          show-overflow-tooltip 
        />
        <el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
          <template #default="scope">
            <ModifyRecord :data="scope.row" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-tooltip content="测试连接" placement="top">
                <el-button 
                  :icon="Connection" 
                  circle 
                  size="small" 
                  type="success" 
                  @click="testClientConnection(scope.row)" 
                  :loading="state.testingConnections.has(scope.row.id)"
                />
              </el-tooltip>
              <el-tooltip content="断开连接" placement="top">
                <el-button 
                  :icon="SwitchButton" 
                  circle 
                  size="small" 
                  type="warning" 
                  @click="disconnectClient(scope.row)"
                />
              </el-tooltip>
              <el-tooltip content="查看详情" placement="top">
                <el-button 
                  :icon="View" 
                  circle 
                  size="small" 
                  type="info" 
                  @click="viewClientDetails(scope.row)"
                />
              </el-tooltip>
              <el-tooltip content="编辑" placement="top">
                <el-button 
                  :icon="Edit" 
                  circle 
                  size="small" 
                  type="primary" 
                  @click="editDialogRef.openDialog(scope.row, '编辑MQTT客户端')" 
                  v-auth="'mqttClient:update'"
                />
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button 
                  :icon="Delete" 
                  circle 
                  size="small" 
                  type="danger" 
                  @click="delMqttClient(scope.row)" 
                  v-auth="'mqttClient:delete'"
                />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination 
              v-model:currentPage="state.tableParams.page"
              v-model:page-size="state.tableParams.pageSize"
              @size-change="(val: any) => handleQuery({ pageSize: val })"
              @current-change="(val: any) => handleQuery({ page: val })"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 50, 100, 200, 500]"
              :total="state.tableParams.total"
              size="small"
              background />
      <printDialog ref="printDialogRef" :title="'打印MQTT 客户端连接信息表'" @reloadTable="handleQuery" />
      <editDialog ref="editDialogRef" @reloadTable="handleQuery" />
    </el-card>
  </div>
</template>
<style scoped>
.mqtt-client-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 20px;
}

.header-title h2 {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.header-title p {
  color: #606266;
  margin: 0;
  font-size: 14px;
}

.header-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 12px;
  background: white;
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
  min-width: 140px;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.stat-icon.online {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.stat-icon.offline {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.advanced-search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.button-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
  padding: 16px;
}

.left-buttons, .right-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.table-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.el-table {
  border-radius: 12px;
}

.el-table th {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mqtt-client-container {
    padding: 12px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-stats {
    justify-content: center;
  }
  
  .stat-card {
    min-width: 120px;
  }
  
  .button-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .left-buttons, .right-buttons {
    justify-content: center;
  }
  
  .header-title h2 {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .stat-card {
    padding: 12px 16px;
    min-width: 100px;
  }
  
  .stat-number {
    font-size: 20px;
  }
  
  .stat-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
}

:deep(.el-input), :deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}
</style>