﻿import {useBaseApi} from '/@/api/base';

// MQTT 客户端连接信息表接口服务
export const useMqttClientApi = () => {
	const baseApi = useBaseApi("mqttClient");
	return {
		// 分页查询MQTT 客户端连接信息表
		page: baseApi.page,
		// 查看MQTT 客户端连接信息表详细
		detail: baseApi.detail,
		// 新增MQTT 客户端连接信息表
		add: baseApi.add,
		// 更新MQTT 客户端连接信息表
		update: baseApi.update,
		// 删除MQTT 客户端连接信息表
		delete: baseApi.delete,
		// 批量删除MQTT 客户端连接信息表
		batchDelete: baseApi.batchDelete,
	}
}

// MQTT 客户端连接信息表实体
export interface MqttClient {
	// 主键Id
	id: number;
	// 实例ID
	instanceId: number;
	// 客户端ID
	clientId: string;
	// 设备名称
	deviceName: string;
	// 设备组ID
	groupId: string;
	// 产品Key
	productKey: string;
	// 设备密钥（加密存储）
	deviceSecret: string;
	// IP地址
	ipAddress: string;
	// 端口
	port: number;
	// 连接状态
	status: string;
	// 协议版本
	protocolVersion: string;
	// 心跳间隔（秒）
	keepAlive: number;
	// 清除会话（0:否，1:是）
	cleanSession: boolean;
	// 连接时间
	connectedAt: string;
	// 断开时间
	disconnectedAt: string;
	// 最后活动时间
	lastActivity: string;
	// 发送消息数
	messagesSent: number;
	// 接收消息数
	messagesReceived: number;
	// 发送字节数
	bytesSent: number;
	// 接收字节数
	bytesReceived: number;
	// 订阅数量
	subscriptionCount: number;
	// 最大订阅数
	maxSubscriptions: number;
	// 设备类型
	deviceType: string;
	// 设备版本
	deviceVersion: string;
	// 设备标签（JSON）
	deviceTags: string;
	// 位置信息（JSON）
	locationInfo: string;
	// 是否在线（0:离线，1:在线）
	isOnline: boolean;
	// 是否启用（0:否，1:是）
	isEnabled: boolean;
	// 认证模式
	authMode: string;
	// 客户端类型
	clientType: string;
	// 最后错误信息
	lastError: string;
	// 扩展属性（JSON）
	extendedProperties: string;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 修改者Id
	updateUserId: number;
	// 软删除
	isDelete?: boolean;
	// 创建者姓名
	createUserName: string;
	// 修改者姓名
	updateUserName: string;
}