﻿import {useBaseApi} from '/@/api/base';

// MQTT 主题信息表接口服务
export const useMqttTopicApi = () => {
	const baseApi = useBaseApi("mqttTopic");
	return {
		// 分页查询MQTT 主题信息表
		page: baseApi.page,
		// 查看MQTT 主题信息表详细
		detail: baseApi.detail,
		// 新增MQTT 主题信息表
		add: baseApi.add,
		// 更新MQTT 主题信息表
		update: baseApi.update,
		// 删除MQTT 主题信息表
		delete: baseApi.delete,
		// 批量删除MQTT 主题信息表
		batchDelete: baseApi.batchDelete,
	}
}

// MQTT 主题信息表实体
export interface MqttTopic {
	// 主键Id
	id: number;
	// 实例ID
	instanceId: number;
	// 主题名称
	topicName: string;
	// 主题类型
	topicType: string;
	// QoS等级（0,1,2）
	qosLevel: boolean;
	// 是否保留消息（0:否，1:是）
	retainMessage: boolean;
	// 订阅数量
	subscriptionCount: number;
	// 消息数量
	messageCount: number;
	// 字节数量
	byteCount: number;
	// 最后消息时间
	lastMessageTime: string;
	// 最后消息内容
	lastMessageContent: string;
	// 最大消息大小
	maxMessageSize: number;
	// 消息过期时间（秒）
	messageExpiry: number;
	// 允许发布（0:否，1:是）
	allowPublish: boolean;
	// 允许订阅（0:否，1:是）
	allowSubscribe: boolean;
	// 发布权限（JSON）
	publishPermissions: string;
	// 订阅权限（JSON）
	subscribePermissions: string;
	// 是否保留（0:否，1:是）
	isRetained: boolean;
	// 描述
	description: string;
	// 主题标签（JSON）
	topicTags: string;
	// 是否启用（0:否，1:是）
	isEnabled: boolean;
	// 是否系统主题（0:否，1:是）
	isSystemTopic: boolean;
	// 监控配置（JSON）
	monitorConfig: string;
	// 告警规则（JSON）
	alertRules: string;
	// 转发规则（JSON）
	forwardRules: string;
	// 扩展属性（JSON）
	extendedProperties: string;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 修改者Id
	updateUserId: number;
	// 软删除
	isDelete?: boolean;
	// 创建者姓名
	createUserName: string;
	// 修改者姓名
	updateUserName: string;
}