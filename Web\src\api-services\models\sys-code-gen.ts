/* tslint:disable */
 
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { TableUniqueConfigItem } from './table-unique-config-item';
 /**
 * 代码生成表
 *
 * @export
 * @interface SysCodeGen
 */
export interface SysCodeGen {

    /**
     * 雪花Id
     *
     * @type {number}
     * @memberof SysCodeGen
     */
    id?: number;

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof SysCodeGen
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof SysCodeGen
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof SysCodeGen
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof SysCodeGen
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    updateUserName?: string | null;

    /**
     * 作者姓名
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    authorName?: string | null;

    /**
     * 是否移除表前缀
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    tablePrefix?: string | null;

    /**
     * 生成方式
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    generateType?: string | null;

    /**
     * 库定位器名
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    configId?: string | null;

    /**
     * 库名
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    dbNickName?: string | null;

    /**
     * 数据库名(保留字段)
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    dbName?: string | null;

    /**
     * 数据库类型
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    dbType?: string | null;

    /**
     * 数据库链接
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    connectionString?: string | null;

    /**
     * 数据库表名
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    tableName?: string | null;

    /**
     * 命名空间
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    nameSpace?: string | null;

    /**
     * 业务名
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    busName?: string | null;

    /**
     * 表唯一字段配置
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    tableUniqueConfig?: string | null;

    /**
     * 是否生成菜单
     *
     * @type {boolean}
     * @memberof SysCodeGen
     */
    generateMenu?: boolean;

    /**
     * 菜单图标
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    menuIcon?: string | null;

    /**
     * 菜单编码
     *
     * @type {number}
     * @memberof SysCodeGen
     */
    menuPid?: number | null;

    /**
     * 页面目录
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    pagePath?: string | null;

    /**
     * 支持打印类型
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    printType?: string | null;

    /**
     * 打印模版名称
     *
     * @type {string}
     * @memberof SysCodeGen
     */
    printName?: string | null;

    /**
     * 表唯一字段列表
     *
     * @type {Array<TableUniqueConfigItem>}
     * @memberof SysCodeGen
     */
    tableUniqueList?: Array<TableUniqueConfigItem> | null;
}
