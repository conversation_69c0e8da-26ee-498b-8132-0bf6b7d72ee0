{"version": 3, "sources": ["../../vue3-tree-org/lib/index.esm.js"], "sourcesContent": ["import{h as e,withDirectives as n,vShow as t,resolveDirective as o,defineComponent as a,ref as l,computed as r,onMounted as d,onBeforeUnmount as s,openBlock as c,createElementBlock as u,toDisplayString as i,createCommentVNode as p,createElementVNode as f,normalizeClass as m,onBeforeMount as v,onUnmounted as g,watch as y,warn as h,normalizeStyle as b,renderSlot as x,resolveComponent as $,createBlock as N,Teleport as S,createVNode as z,createSlots as E,withCtx as D,nextTick as C,reactive as k,Transition as M,Fragment as O,renderList as w,vModelText as _}from\"vue\";var R=\"vue3-tree-org\",F=\"4.2.2\";const A=function(e){return\"[object Object]\"===Object.prototype.toString.call(e)},B=function(e){return\"string\"==typeof e},L=function(e){return\"number\"==typeof e},T={print:(e,n,t)=>B(n)||\"boolean\"==typeof t,pretty:(e,n,t)=>B(n)&&B(e)||\"string\"==typeof t,primary:(e,n)=>B(e)||\"boolean\"==typeof n,success:(e,n)=>B(e)||\"boolean\"==typeof n,info:(e,n)=>B(e)||\"boolean\"==typeof n,warning:(e,n)=>B(e)||\"boolean\"==typeof n,danger:(e,n)=>B(e)||\"boolean\"==typeof n};function j(e=\"default\"){let n=\"\";switch(e){case\"primary\":n=\"#2d8cf0\";break;case\"success\":n=\"#19be6b\";break;case\"info\":n=\"#909399\";break;case\"warning\":n=\"#ff9900\";break;case\"danger\":n=\"#ff4d4f\";break;case\"default\":n=\"#35495E\";break;default:n=e}return n}T.print=function(e,n=\"default\",t=!1){return\"object\"==typeof e?(console.dir(e),!0):(t?console.log(`%c ${e} `,`background:${j(n)}; padding: 2px; border-radius: 4px;color: #fff;`):console.log(`%c ${e} `,`color: ${j(n)};`),!0)},T.pretty=function(e,n,t=\"primary\"){return console.log(`%c ${e} %c ${n} %c`,`background:${j(t)};border:1px solid ${j(t)}; padding: 1px; border-radius: 4px 0 0 4px; color: #fff;`,`border:1px solid ${j(t)}; padding: 1px; border-radius: 0 4px 4px 0; color: ${j(t)};`,\"background:transparent\"),!0},T.primary=function(e,n=!1){return this.print&&this.print(e,\"primary\",n),!0},T.success=function(e,n=!1){return this.print&&this.print(e,\"success\",n),!0},T.info=function(e,n=!1){return this.print&&this.print(e,\"info\",n),!0},T.warning=function(e,n=!1){return this.print&&this.print(e,\"warning\",n),!0},T.danger=function(e,n=!1){return this.print&&this.print(e,\"danger\",n),!0};var V={async install(e){e.config.globalProperties.$log=T,T.pretty(\"[\"+R+\"] \"+F,\"success\")}};const Y=function(){let e=!1;return{get:()=>e,set:n=>{e=n}}}(),P=function(e,n,t){const{id:o,children:a}=n;if(e[o]===t)return e;if(Array.isArray(e[a])){const o=e[a];for(let e=0,a=o.length;e<a;e++){const a=o[e],l=P(a,n,t);if(l)return l}}},X=function(e,n){const{parenNode:t,onlyOneNode:o,cloneNodeDrag:a}=n;if(t.value){const{keys:l}=n,{id:r,pid:d,children:s}=l,c=t.value.$$data,u=JSON.parse(JSON.stringify(e.$$data));a?(!function(e,n,t){const{children:o}=n;if(A(e))a(e);else if(Array.isArray(e))for(let n=0,t=e.length;n<t;n++)a(e[n]);function a(e){if(t(e),Array.isArray(e[o])){const n=e[o];for(let e=0,t=n.length;e<t;e++)a(n[e])}}}(u,l,(function(e){\"string\"==typeof e[r]&&-1!==e[r].indexOf(\"clone-node\")&&(e[r]=`clone-node-${e[r]}`)})),o&&Array.isArray(u[s])&&(u[s]=[]),u[l.pid]=c[l.id],c[s]?c[s].push(u):c[s]=[u]):(!function(e,n){const{keys:t,data:o,onlyOneNode:a}=n,{id:l,pid:r,children:d}=t,s=P(o,t,e[r]),c=s[d];let u;for(let n=0,t=c.length;n<t;n++)if(c[n][l]===e[l]){c.splice(n,1),u=n;break}const i=e[d];a&&void 0!==u&&i&&(e[d]=[],i.forEach((e=>{e[r]=s[l]})),s[d].splice(u,0,...i))}(u,n),u[d]=c[r],c[s]?c[s].push(u):c[s]=[].concat(u))}},K={beforeMount(e,n){const{l:t,t:o}=n.modifiers,{drag:a,dragData:l,node:r,emit:d,beforeDragEnd:s,initNodes:c}=n.value,{value:u}=n,i={...l};e.addEventListener(\"mousedown\",(function(e){const n=e.target;if(!1===a||0!==e.button||r.focused||r.$$data.noDragging||n.className.indexOf(\"tree-org-node-btn\")>-1)return!1;(function(e){e.stopPropagation(),v=e.screenX,g=e.screenY,f=0,i.contextmenu.value=!1;const{keys:n,onlyOneNode:t}=i;if(t){const{children:e}=n,t={...r.$$data};t[e]=[],i.cloneData.value=c(t)}else i.cloneData.value=c(r.$$data)})(e),document.addEventListener(\"mousemove\",y),document.addEventListener(\"mouseup\",h),x(\"start\")}));let p,f=0,m=!1,v=0,g=0;function y(n){return n.preventDefault(),!(Math.abs(n.screenX-v)<5&&Math.abs(n.screenY-g)<5)&&(m||(m=!0,function(n){Y.set(!0),i.nodeMoving.value=!0,r.moving=!0;let t=e;for(;!t.classList.contains(\"tree-org-node\");)f+=t.offsetLeft,t=t.offsetParent;f+=2,p=document.querySelector(\"#clone-tree-org\"),p&&(p.style.width=`${t.clientWidth}px`,p.style.opacity=\"0.8\",p.style.left=n.clientX-f+\"px\",p.style.top=n.clientY+2+\"px\")}(n)),t&&o&&u?(p&&(p.style.left=n.clientX-f+\"px\",p.style.top=n.clientY+2+\"px\"),void x(\"move\")):t&&u?(e.style.left=n.clientX-f+\"px\",void x(\"move\")):void(o&&u&&(e.style.top=n.clientY+\"px\",x(\"move\"))))}function h(e){if(document.removeEventListener(\"mousemove\",y),document.removeEventListener(\"mouseup\",h),m){if(\"function\"==typeof s){const n=s(r,i.parenNode.value);n&&n.then?n.then((()=>{b(e)}),(()=>{})):!1!==n&&b(e)}else b(e);m=!1,p=null,r.moving=!1,i.nodeMoving.value=!1,setTimeout((()=>{Y.set(!1)}),200)}}function b(e){const n=document.querySelector(\".tree-org-node__moving\");if(n&&n.contains(e.target))return x(\"end\"),!1;X(r,i),x(\"end\")}function x(e){\"start\"!==e?\"move\"!==e?\"end\"===e&&d(\"on-node-drag-end\",r,i.parenNode.value):d(\"on-node-drag\",r):d(\"on-node-drag-start\",r)}}};const W={onClick:\"onNodeClick\",onDblclick:\"onNodeDblclick\",onContextmenu:\"onNodeContextmenu\",onMouseenter:\"onNodeMouseenter\",onMouseleave:\"onNodeMouseleave\"};function q(e,n){if(\"function\"==typeof e)return function(t){t.target.className.indexOf(\"org-tree-node-btn\")>-1||e(t,n)}}const U=(e,n,t)=>!(Array.isArray(e[n])&&e[n].length>0)&&!t||e.isLeaf,I=(e,o,a)=>{const{attrs:l}=a,r=[\"tree-org-node\"],d=[],{expand:s,children:c,id:u}=o;return U(o,\"children\",l.lazy)?r.push(\"is-leaf\"):l.collapsable&&!s&&r.push(\"collapsed\"),o.moving&&r.push(\"tree-org-node__moving\"),d.push(J(e,o,a)),l.collapsable&&!s||d.push(H(e,c,a)),n(e(\"div\",{class:r,key:u},d),[[t,!o.hidden]])},J=(e,a,l)=>{const{attrs:r}=l,d=r.props,s=r.renderContent,{label:c}=a,u=[];if(l.slots.default)u.push(l.slots.default({node:a}));else if(\"function\"==typeof s){T.warning(\"scoped-slot header is easier to use. We recommend users to use scoped-slot header.\");const n=s(e,a);n&&u.push(n)}else u.push(e(\"div\",{class:\"tree-org-node__text\"},c));r.collapsable&&!U(a,\"children\",r.lazy)&&u.push(((e,n,t)=>{const{attrs:o}=t,a=o.onOnExpand,l=[\"tree-org-node__expand\"];n.expand&&l.push(\"expanded\");const r=[];return t.slots.expand?r.push(t.slots.expand({node:n})):r.push(e(\"span\",{class:\"tree-org-node__expand-btn\"})),e(\"span\",{class:l,onMousedown:e=>{e.stopPropagation()},onDblclick:e=>{e.stopPropagation()},onClick:e=>{e.stopPropagation(),a&&a(e,n)}},r)})(e,a,l));const i=[\"tree-org-node__inner\"];let{labelStyle:p,labelClassName:f,selectedClassName:m,selectedKey:v}=r;\"function\"==typeof f&&(f=f(a)),f&&i.push(f),a.className&&i.push(a.className),\"function\"==typeof m&&(m=m(a)),void 0!==v&&(v=Array.isArray(v)?v:[v]),m&&v&&v.includes(a.id)&&i.push(m);const g=[\"tree-org-node__content\"];a.$$root&&g.push(`is-root_${r.suffix}`),a.label||g.push(\"is-empty\"),a.$$focused&&g.push(\"is-edit\");const y=o(\"nodedrag\"),h=[];r.vNodedrag&&y&&!a.$$root&&h.push([y,Object.assign({node:a},r.vNodedrag),\"\",{l:!0,t:!0}]);const b={};for(const e in W)if(Object.prototype.hasOwnProperty.call(W,e)){const n=r[W[e]];n&&(b[e]=q(n,a))}const x=r.onNodeFocus,$=r.onNodeBlur,N=o(\"focus\"),S=[[t,a.$$focused]];return N&&S.push([N,a.$$focused]),e(\"div\",{class:g},[n(e(\"div\",{class:i,style:a.style?a.style:p,...b},u),h),n(e(\"textarea\",{class:\"tree-org-node__textarea\",placeholder:\"请输入节点名称\",value:a.label,onFocus:e=>{x&&x(e,a.$$data,a)},onInput:e=>{a.label=e.target.value},onBlur:e=>{void 0!==a.$$data.focused&&(a.$$data.focused=!1),a.$$data[d.label]=e.target.value,a.$$focused=!1,$&&$(e,a.$$data,a)},onClick:e=>e.stopPropagation()}),S)])},H=(e,n,t)=>{if(Array.isArray(n)&&n.length){const o=n.filter((e=>!e.$$hidden)).map((n=>I(e,n,t)));return e(\"div\",{class:\"tree-org-node__children\"},o)}return\"\"},G=(n,t)=>n.data&&0!==Object.keys(n.data).length?(n.data.$$root=!n.isClone,I(e,n.data,t)):\"\";G.directives={focus:{mounted(e,{value:n}){n&&e.focus()},updated(e,{value:n}){n&&e.focus()}},nodedrag:K};var Q=a({props:{scale:String,tools:Object},setup(e,{emit:n}){const t=l(!1),o=l(!1);function a(e){o.value=!o.value,n(\"onFullscreen\",e)}function c(){document.isFullScreen||document.mozIsFullScreen||document.webkitIsFullScreen||!o.value||a(\"esc\")}const u=r((()=>t.value?\"全部收起\":\"全部展开\")),i=r((()=>o.value?\"退出全屏\":\"全屏\"));return d((()=>{window.addEventListener(\"resize\",c)})),s((()=>{window.removeEventListener(\"resize\",c)})),{expanded:t,fullscreen:o,expandTitle:u,fullTiltle:i,handleExpand:function(){t.value=!t.value,n(\"onExpand\")},handleScale:function(e){\"out\"===e?n(\"onScale\",.1):\"in\"===e?n(\"onScale\",-.1):n(\"onRestore\")},handleFullscreen:a}}});const Z={class:\"zm-tree-handle\"},ee={key:0,class:\"zm-tree-percent\"},ne=[\"title\"],te={class:\"zm-tree-svg\"},oe=[f(\"span\",{class:\"zm-tree-icon\"},\"+\",-1)],ae=[f(\"span\",{class:\"zm-tree-icon\"},\"-\",-1)],le=[f(\"span\",{class:\"zm-tree-restore\"},null,-1)],re=[\"title\"],de={class:\"zm-tree-svg\"};function se(e){return\"function\"==typeof e||\"[object Function]\"===Object.prototype.toString.call(e)}function ce(e,n,t,o=1){return{deltaX:Math.round(n/o/e[0])*e[0],deltaY:Math.round(t/o/e[1])*e[1]}}function ue(e,n,t){return e<n?n:t<e?t:e}Q.render=function(e,n,t,o,a,l){return c(),u(\"div\",Z,[e.tools.scale?(c(),u(\"div\",ee,i(e.scale),1)):p(\"v-if\",!0),e.tools.expand?(c(),u(\"div\",{key:1,onClick:n[0]||(n[0]=(...n)=>e.handleExpand&&e.handleExpand(...n)),title:e.expandTitle,class:\"zm-tree-handle-item\"},[f(\"span\",te,[f(\"i\",{class:m([\"treefont\",e.expanded?\"icon-collapse\":\"icon-expand\"])},null,2)])],8,ne)):p(\"v-if\",!0),e.tools.zoom?(c(),u(\"div\",{key:2,onClick:n[1]||(n[1]=n=>e.handleScale(\"out\")),title:\"放大\",class:\"zm-tree-handle-item zoom-out\"},oe)):p(\"v-if\",!0),e.tools.zoom?(c(),u(\"div\",{key:3,onClick:n[2]||(n[2]=n=>e.handleScale(\"in\")),title:\"缩小\",class:\"zm-tree-handle-item zoom-in\"},ae)):p(\"v-if\",!0),e.tools.restore?(c(),u(\"div\",{key:4,onClick:n[3]||(n[3]=n=>e.handleScale(\"restore\")),title:\"还原\",class:\"zm-tree-handle-item\"},le)):p(\"v-if\",!0),e.tools.fullscreen?(c(),u(\"div\",{key:5,onClick:n[4]||(n[4]=(...n)=>e.handleFullscreen&&e.handleFullscreen(...n)),title:e.fullTiltle,class:\"zm-tree-handle-item\"},[f(\"span\",de,[f(\"i\",{class:m([\"treefont\",e.fullscreen?\"icon-unfullscreen\":\"icon-fullscreen\"])},null,2)])],8,re)):p(\"v-if\",!0)])},Q.__file=\"src/components/tools/tools.vue\";const ie=function(e,n,t,o=!1){e&&n&&t&&e.addEventListener(n,t,o)},pe=function(e,n,t,o=!1){e&&n&&t&&e.removeEventListener(n,t,o)},fe=(e,{emit:n},t)=>{const o={start:\"mousedown\",move:\"mousemove\",stop:\"mouseup\"},a={start:\"touchstart\",move:\"touchmove\",stop:\"touchend\"},s={userSelect:\"none\",MozUserSelect:\"none\",WebkitUserSelect:\"none\",MsUserSelect:\"none\"},c={userSelect:\"auto\",MozUserSelect:\"auto\",WebkitUserSelect:\"auto\",MsUserSelect:\"auto\"};let u=o;const i=l(e.x),p=l(e.y),f=l(e.z),m=l(e.active),h=l(!1),b=l(0),x=l(0),$=l(0),N=l(0),S=l(0),z=l(0);let E={},D={};function C(){E={mouseX:0,mouseY:0,x:0,y:0,w:0,h:0},D={minLeft:-1/0,maxLeft:1/0,minRight:-1/0,maxRight:1/0,minTop:-1/0,maxTop:1/0,minBottom:-1/0,maxBottom:1/0}}function k(){if(e.parent){const[e,n]=M();S.value=e||0,z.value=n||0}}function M(){if(e.parent&&t.eleRef.value){const e=window.getComputedStyle(t.eleRef.value.parentNode,null);return[parseInt(e.getPropertyValue(\"width\"),10),parseInt(e.getPropertyValue(\"height\"),10)]}return[0,0]}function O(o){if(o instanceof MouseEvent&&1!==o.which)return;const a=o.target||o.srcElement;if(a&&t.eleRef.value&&t.eleRef.value.contains(a)){if(e.dragCancel&&function(e,n,t){let o=e;const a=[\"matches\",\"webkitMatchesSelector\",\"mozMatchesSelector\",\"msMatchesSelector\",\"oMatchesSelector\"].find((e=>o&&se(o[e])))||\"\";if(!se(o[a]))return!1;do{if(o[a](n))return!0;if(o===t)return!1;o=o.parentNode}while(o);return!1}(a,e.dragCancel,t.eleRef.value))return void(h.value=!1);m.value||(m.value=!0,n(\"activated\"),n(\"update:active\",!0)),e.draggable&&(h.value=!0),o instanceof MouseEvent?(E.mouseX=o.pageX,E.mouseY=o.pageY):(E.mouseX=o.touches[0].pageX,E.mouseY=o.touches[0].pageY),E.left=i.value,E.right=b.value,E.top=p.value,E.bottom=x.value,e.parent&&(D=w()),ie(document.documentElement,u.move,R),ie(document.documentElement,u.stop,F)}}function w(){const n=e.grid;return{minLeft:i.value%n[0],maxLeft:Math.floor((S.value-$.value-i.value)/n[0])*n[0]+i.value,minRight:b.value%n[0],maxRight:Math.floor((S.value-$.value-b.value)/n[0])*n[0]+b.value,minTop:p.value%n[1],maxTop:Math.floor((z.value-N.value-p.value)/n[1])*n[1]+p.value,minBottom:x.value%n[1],maxBottom:Math.floor((z.value-N.value-x.value)/n[1])*n[1]+x.value}}function _(o){const a=o.target||o.srcElement;t.eleRef.value&&!t.eleRef.value.contains(a)&&m.value&&!e.preventDeactivation&&(m.value=!1,n(\"deactivated\"),n(\"update:active\",!1))}function R(t){h.value&&function(t){const o=e.axis,a=e.grid;let l=0,r=0;t instanceof MouseEvent?(l=t.pageX,r=t.pageY):(l=t.touches[0].pageX,r=t.touches[0].pageY);const d=o&&\"y\"!==o?E.mouseX-l:0,s=o&&\"x\"!==o?E.mouseY-r:0,{deltaX:c,deltaY:u}=ce(a,d,s,e.scale),f=ue(E.left-c,D.minLeft,D.maxLeft),m=ue(E.top-u,D.minTop,D.maxTop),v=ue(E.right+c,D.minRight,D.maxRight),g=ue(E.bottom+u,D.minBottom,D.maxBottom);i.value=f,p.value=m,b.value=v,x.value=g,n(\"dragging\",i.value,p.value)}(t)}function F(){C(),h.value&&(h.value=!1,n(\"dragstop\",i.value,p.value))}v((()=>{C()})),d((()=>{!e.enableNativeDrag&&t.eleRef.value&&(t.eleRef.value.ondragstart=()=>!1);const[n,o]=M();if(S.value=n,z.value=o,t.eleRef.value){const[e,n]=function(e){const n=window.getComputedStyle(e);return[parseFloat(n.getPropertyValue(\"width\")),parseFloat(n.getPropertyValue(\"height\"))]}(t.eleRef.value);$.value=e,N.value=n,b.value=S.value-$.value-i.value,x.value=z.value-N.value-p.value}ie(document.documentElement,\"mousedown\",_),ie(document.documentElement,\"touchend touchcancel\",_),ie(window,\"resize\",k)})),g((()=>{pe(document.documentElement,\"mousedown\",_),pe(document.documentElement,\"touchstart\",F),pe(document.documentElement,\"mousemove\",R),pe(document.documentElement,\"touchmove\",R),pe(document.documentElement,\"mouseup\",F),pe(document.documentElement,\"touchend touchcancel\",_),pe(window,\"resize\",k)}));const A=r((()=>({transform:`translate(${i.value}px, ${p.value}px)`,zIndex:f.value,...h.value&&e.disableUserSelect?s:c})));return y((()=>e.active),(e=>{m.value=e,n(e?\"activated\":\"deactivated\")})),y((()=>e.z),(e=>{(e>=0||\"auto\"===e)&&(f.value=e)})),y((()=>e.x),(n=>{h.value||(e.parent&&(D=w()),function(n){const{deltaX:t}=ce(e.grid,n,p.value,e.scale),o=ue(t,D.minLeft,D.maxLeft);i.value=o,b.value=S.value-$.value-o}(n))})),y((()=>e.y),(n=>{h.value||(e.parent&&(D=w()),function(n){const{deltaY:t}=ce(e.grid,i.value,n,e.scale),o=ue(t,D.minTop,D.maxTop);p.value=o,x.value=z.value-N.value-o}(n))})),{enabled:m,dragging:h,style:A,resetBoundsAndMouseState:C,elementTouchDown:function(e){u=a,O(e)},elementMouseDown:function(e){u=o,O(e)}}};\"production\"===process.env.NODE_ENV||Object.freeze({}),\"production\"===process.env.NODE_ENV||Object.freeze([]);const me=Symbol(\"wrapper\");function ve(e,n){if(null===(t=e)||\"object\"!=typeof t||e.__elPropsReservedKey)return e;var t;const{values:o,required:a,default:l,type:r,validator:d}=e,s=o||d?e=>{let t=!1,a=[];if(o&&(a=[...o,l],t=t||a.includes(e)),d&&(t=t||d(e)),!t&&a.length>0){const t=[...new Set(a)].map((e=>JSON.stringify(e))).join(\", \");h(`Invalid prop: validation failed${n?` for prop \"${n}\"`:\"\"}. Expected one of [${t}], got value ${JSON.stringify(e)}.`)}return t}:void 0;return{type:\"object\"==typeof r&&Object.getOwnPropertySymbols(r).includes(me)?r[me]:r,validator:s,__elPropsReservedKey:!0,default:l,required:!!a}}const ge=e=>function(e){let n=-1;const t=e?e.length:0,o={};for(;++n<t;){const t=e[n];o[t[0]]=t[1]}return o}(Object.entries(e).map((([e,n])=>[e,ve(n,e)]))),ye=e=>({[me]:e});var he=a({replace:!0,name:\"Draggable\",props:ge({className:{type:String,default:\"zm-draggable\"},classNameDraggable:{type:String,default:\"draggable\"},classNameDragging:{type:String,default:\"dragging\"},classNameActive:{type:String,default:\"active\"},disableUserSelect:{type:Boolean,default:!0},enableNativeDrag:{type:Boolean,default:!1},preventDeactivation:{type:Boolean,default:!1},active:{type:Boolean,default:!1},draggable:{type:Boolean,default:!0},x:{type:Number,default:0},y:{type:Number,default:0},z:{type:[String,Number],default:\"auto\",validator:e=>\"string\"==typeof e?\"auto\"===e:e>=0},dragCancel:String,axis:{type:String,default:\"both\",validator:e=>[\"x\",\"y\",\"both\"].includes(e)},grid:{type:Array,default:()=>[1,1]},parent:{type:Boolean,default:!1},scale:{type:Number,default:1,validator:e=>e>0}}),emits:{activated:()=>!0,deactivated:()=>!0,dragging:(e,n)=>L(e)&&L(n),dragstop:(e,n)=>L(e)&&L(n),\"update:active\":e=>\"boolean\"==typeof e},setup(e,n){const t=l(),o=fe(e,n,{eleRef:t});return{top:top,eleRef:t,...o}}});he.render=function(e,n,t,o,a,l){return c(),u(\"div\",{style:b(e.style),class:m([{[e.classNameActive]:e.enabled,[e.classNameDragging]:e.dragging,[e.classNameDraggable]:e.draggable},e.className]),ref:\"eleRef\",onMousedown:n[0]||(n[0]=(...n)=>e.elementMouseDown&&e.elementMouseDown(...n)),onTouchstart:n[1]||(n[1]=(...n)=>e.elementTouchDown&&e.elementTouchDown(...n))},[x(e.$slots,\"default\")],38)},he.__file=\"src/components/draggable/src/draggable.vue\";var be=a({components:{TreeOrgNode:G},props:ge({data:{type:Object,required:!0},props:{type:ye(Object)},modelValue:Boolean,horizontal:Boolean,selectedKey:String,collapsable:Boolean,renderContent:Function,labelStyle:Object,labelClassName:{type:[Function,String]}}),setup:(e,n)=>({defaultSlot:!!n.slots.default,expandSlot:!!n.slots.expand})});be.render=function(e,o,a,l,r,d){const s=$(\"tree-org-node\");return c(),N(S,{to:\"body\"},[n(f(\"div\",{id:\"clone-tree-org\",class:m([\"clone-tree-org tree-org\",{horizontal:e.horizontal,collapsable:e.collapsable}])},[z(s,{data:e.data,props:e.props,isClone:!1,horizontal:e.horizontal,labelStyle:e.labelStyle,collapsable:e.collapsable,renderContent:e.renderContent,labelClassName:e.labelClassName},E({_:2},[e.defaultSlot?{name:\"default\",fn:D((({node:n})=>[x(e.$slots,\"default\",{node:n})]))}:void 0,e.expandSlot?{name:\"expand\",fn:D((({node:n})=>[x(e.$slots,\"expand\",{node:n})]))}:void 0]),1032,[\"data\",\"props\",\"horizontal\",\"labelStyle\",\"collapsable\",\"renderContent\",\"labelClassName\"])],2),[[t,e.modelValue]])])},be.__file=\"src/components/clone-org/clone-org.vue\";const xe=(e,{emit:n},t)=>{const o=l(\"\");function a(e,n,t){if(e[n.id]===t)return e;if(Array.isArray(e[n.children])){const o=e[n.children];for(let e=0,l=o.length;e<l;e++){const l=a(o[e],n,t);if(l)return l}}}function s(){const{props:t,data:o,node:l}=e;if(e.nodeDelete)return void e.nodeDelete(l.$$data);if(l.$$root)return void T.pretty(\"[提示] \",\"根节点不允许删除\",\"danger\");const{id:r,children:d}=t,s=a(o,t,l.pid);if(s){const e=s[d];for(let t=0,o=e.length;t<o;t++)if(e[t][r]===l.id){e.splice(t,1),n(\"onNodeDelete\",l.$$data,l);break}}}function c(){const{nodeEdit:t,node:o}=e;t?t(o.$$data):(o.$$focused=!0,n(\"onNodeFocus\",o.$$data))}function u(o){if(e.modelValue){if(t.eleRef.value&&t.eleRef.value.contains(o.target))return!1;n(\"update:modelValue\",!1)}}const i=r((()=>({left:`${e.x}px`,top:`${e.y}px`}))),p=r((()=>!e.disabled&&!e.node.disabled));return d((()=>{document.addEventListener(\"mousedown\",u)})),g((()=>{document.removeEventListener(\"mousedown\",u)})),{position:i,editable:p,handleMenu:function(a){const l=a.target;if(\"zm-tree-menu-item\"===l.className){const a=l.getAttribute(\"action\");switch(a){case\"copy\":!function(){if(e.nodeCopy)return void e.nodeCopy(e.node.$$data);o.value=e.node.label,C((()=>{t.inputRef&&t.inputRef.value&&(t.inputRef&&t.inputRef.value.select(),navigator.clipboard.writeText(o.value).then((function(){T.pretty(\"[提示] \",\"文本复制成功\",\"success\")}),(function(){T.pretty(\"[错误] \",\"浏览器不支持\",\"danger\")})),n(\"onNodeCopy\",o.value))}))}();break;case\"add\":!function(){if(e.nodeAdd)return void e.nodeAdd(e.node.$$data);const{id:t,pid:o,label:a,expand:l,children:r}=e.props,{node:d}=e,s={[t]:String((new Date).getTime()),[o]:d.id,[a]:\"\",[l]:!1,[r]:[],newNode:!0,focused:!0};Array.isArray(d.children)?d.$$data[r].push(s):d.$$data[r]=[s];n(\"onNodeFocus\",s)}();break;case\"edit\":c();break;case\"delete\":s()}n(\"contextmenu\",{command:a,node:e.node,data:e.node.$$data}),n(\"update:modelValue\",!1)}},handleEdit:c,handleClose:u,afterEnter:function(){n(\"opened\")},afterLeave:function(){n(\"closed\")}}};var $e=a({name:\"Contextmenu\",props:ge({modelValue:{type:Boolean,required:!0},data:{type:Object,required:!0},node:{type:ye(Object),required:!0},props:{type:ye(Object)},x:Number,y:Number,menus:{type:ye(Array)},nodeAdd:Function,nodeDelete:Function,nodeEdit:Function,nodeCopy:Function,disabled:Boolean}),emits:{onNodeDelete:(e,n)=>A(e)&&A(n),onNodeCopy:e=>B(e),contextmenu:e=>A(e),onNodeFocus:e=>A(e),opened:()=>!0,closed:()=>!0,\"update:modelValue\":e=>\"boolean\"==typeof e},setup(e,n){const t=l(),o=l();return{eleRef:o,inputRef:t,copyText:l(\"\"),oldData:k({}),...xe(e,n,{inputRef:t,eleRef:o})}}});const Ne=[\"action\"];$e.render=function(e,o,a,l,r,d){return c(),N(S,{to:\"body\"},[z(M,{name:\"dialog-fade\",onAfterEnter:e.afterEnter,onAfterLeave:e.afterLeave,persisted:\"\"},{default:D((()=>[n(f(\"div\",{style:b(e.position),onClick:o[1]||(o[1]=(...n)=>e.handleMenu&&e.handleMenu(...n)),ref:\"eleRef\",class:\"zm-tree-contextmenu\"},[f(\"ul\",null,[(c(!0),u(O,null,w(e.menus,(n=>(c(),u(O,null,[e.editable||![\"add\",\"edit\",\"delete\"].includes(n.command)?(c(),u(\"li\",{class:\"zm-tree-menu-item\",action:n.command,key:n.command},i(n.name),9,Ne)):p(\"v-if\",!0)],64)))),256))]),n(f(\"textarea\",{class:\"copy-textarea\",ref:\"inputRef\",\"onUpdate:modelValue\":o[0]||(o[0]=n=>e.copyText=n)},null,512),[[_,e.copyText]])],4),[[t,e.modelValue]])])),_:1},8,[\"onAfterEnter\",\"onAfterLeave\"])])},$e.__file=\"src/components/contextmenu/src/contextmenu.vue\";const Se=[{name:\"复制文本\",command:\"copy\"},{name:\"新增节点\",command:\"add\"},{name:\"编辑节点\",command:\"edit\"},{name:\"删除节点\",command:\"delete\"}],ze=ge({data:{type:Object,required:!0},center:Boolean,props:{type:ye(Object),default:()=>({id:\"id\",pid:\"pid\",label:\"label\",expand:\"expand\",children:\"children\"})},toolBar:{type:[Object,Boolean],default:()=>({expand:!0,scale:!0,zoom:!0,restore:!0,fullscreen:!0})},disabled:{type:Boolean,default:!1},scalable:{type:Boolean,default:!0},draggable:{type:Boolean,default:!0},draggableOnNode:{type:Boolean,default:!1},nodeDraggable:{type:Boolean,default:!0},cloneNodeDrag:{type:Boolean,default:!0},onlyOneNode:{type:Boolean,default:!0},clickDelay:{type:Number,default:260},lazy:Boolean,load:{type:ye(Function)},defaultExpandLevel:Number,defaultExpandKeys:{type:Array,default:()=>[]},beforeDragEnd:Function,horizontal:Boolean,selectedKey:{type:[Array,String,Number]},collapsable:Boolean,renderContent:Function,labelStyle:Object,labelClassName:{type:[Function,String]},selectedClassName:{type:[Function,String]},defineMenus:{type:ye([Array,Function]),default:()=>Se},nodeAdd:Function,nodeDelete:Function,nodeEdit:Function,nodeCopy:Function,filterNodeMethod:Function}),Ee=(e,{emit:n},t)=>{const o=l(0),a=l(0),d=l(!1);function s(n,l){const r=t.zoomRef.value,d=t.treeRef.value;let s=r.clientWidth/2;const c=r.clientHeight/2;let u=r.clientHeight-d.clientHeight,i=r.clientWidth-d.clientWidth;if(u>0&&(u=0),i>0&&(i=0),e.center){const e=(r.clientWidth-d.clientWidth)/2;i-=e,s-=e}o.value=n>s?s:n<i?i:n,a.value=l<u?u:l>c?c:l}function c(e,t){s(e,t),setTimeout((()=>{Y.set(!1)}),200),n(\"on-drag-stop\",{x:e,y:t})}const u=l(!1),i=k({value:{}});const p=l(!1),f=l(0),m=l(0),g=l({}),h=l([]);const b=l(1);function x(n){if(!e.scalable)return;const t=Number((Number(b.value)+n).toFixed(1));b.value=n>0?Math.min(3,t):Math.max(.3,t)}function $(n,t,l){if(d.value=!0,console.log(e.center,e.horizontal,n.offsetLeft,t),!e.center||e.horizontal){const e=n.offsetLeft-t;o.value-=e}const r=n.offsetTop-l;a.value-=r,s(o.value,a.value)}let N;const S=k(Object.assign({id:\"id\",pid:\"pid\",label:\"label\",expand:\"expand\",children:\"children\",isLeaf:\"isLeaf\"},e.props));const z=l(!1);function E(e){e.forEach((e=>{e.expand&&(e.expand=!1,T.delete(e.id)),e.children&&E(e.children)}))}const D=l(!1);function M(e,n){Array.isArray(e)?e.forEach((e=>{n&&T.add(e.id),e.expand=n,n&&T.add(e.id),e.children&&M(e.children,n)})):(n&&T.add(e.id),e.expand=n,n&&T.add(e.id),e.children&&M(e.children,n))}function O(e){V.value=j(e)}const w=r((()=>({width:100/b.value+\"%\",height:100/b.value+\"%\",transform:`scale(${b.value})`}))),_=r((()=>`${Math.round(100*b.value)}%`)),R=r((()=>e.draggableOnNode&&!e.nodeDraggable?\"\":`.tree-org-node__content:not(.is-root_${X})>.tree-org-node__inner`)),F=r((()=>D.value?\"收起全部节点\":\"展开全部节点\")),A=r((()=>D.value?\"收起全部节点\":\"展开全部节点\")),B=l({}),L=r((()=>{const{cloneNodeDrag:t,onlyOneNode:o,data:a}=e;return{drag:e.nodeDraggable,dragData:{keys:S,nodeMoving:u,stopClick:Y,parenNode:i,cloneNodeDrag:t,onlyOneNode:o,contextmenu:p,cloneData:B,data:a},beforeDragEnd:e.beforeDragEnd,initNodes:j,emit:n}}));y((()=>e.horizontal),(()=>{C((()=>{c(o.value,a.value)}))}));let T=new Set(e.defaultExpandKeys);function j(n){const{defaultExpandLevel:t=0}=e,o=(e,n)=>{const{id:a,label:l,pid:r,expand:d,children:s,isLeaf:c}=S,u={};Object.keys(e).map((n=>{[\"hidden\",\"disabled\",\"className\",\"style\"].includes(n)&&(u[n]=e[n])}));const i=e[s],p=n+1,f=e[d],m=e[a];return(f||void 0===f&&n<t)&&T.add(m),{...u,id:m,label:e[l],pid:e[r],expand:T.has(m),children:i?i.map((e=>o(e,p))):void 0,isLeaf:e[c],$$level:n,$$data:e,$$focused:e.focused||!1}};return o(n,0)}const V=l(j(e.data));y((()=>e.data),((n,t)=>{n!==t&&(T=new Set(e.defaultExpandKeys)),O(e.data)}),{deep:!0});const P=k({visible:!0,data:{expand:!0,scale:!0,zoom:!0,restore:!0,fullscreen:!0}});v((()=>{\"object\"==typeof e.toolBar?Object.assign(P.data,e.toolBar):e.toolBar||(P.visible=!1)}));const X=function(e){e=e||32;const n=\"ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678\",t=n.length;let o=\"\";for(let a=0;a<e;a++)o+=n.charAt(Math.floor(Math.random()*t));return o}(6);return{keys:S,left:o,top:a,menuX:f,menuY:m,suffix:X,nodeMoving:u,zoomStyle:w,tools:P,zoomPercent:_,dragCancel:R,expandTitle:F,fullTiltle:A,nodeargs:L,expanded:D,fullscreen:z,treeData:V,autoDragging:d,contextmenu:p,nodeMenus:h,menuData:g,cloneData:B,filter:function(n){const t=e.filterNodeMethod;if(!t)throw new Error(\"[Tree] filterNodeMethod is required when filter\");const o=function(e){const a=e.children||[];if(a.forEach((e=>{e.$$hidden=!t.call(e,n,e),o(e)})),e.$$hidden&&a.length){let n=!0;n=a.some((e=>!e.$$hidden)),e.$$hidden=!n}n&&!e.$$hidden&&e.children&&(e.expand=!0)};o(V.value)},setData:O,zoomWheel:function(t){e.scalable&&(t.preventDefault(),t.deltaY<0?x(.1):x(-.1),n(\"on-zoom\",b.value))},onDrag:function(e,t){Y.set(!0),d.value=!1,o.value=e,a.value=t,n(\"on-drag\",{x:e,y:t})},onDragStop:c,expandChange:function(){D.value=!D.value,D.value||(T.clear(),C((()=>{c(o.value,a.value)}))),M(V.value,D.value),n(\"on-expand-all\",D.value)},handleFullscreen:function(e){z.value=!z.value,\"esc\"!==e&&(z.value?function(){const e=t.eleRef.value;e.requestFullscreen&&e.requestFullscreen()}():document.exitFullscreen&&document.exitFullscreen())},zoomOrgchart:x,restoreOrgchart:function(){b.value=1,o.value=0,a.value=0,n(\"on-restore\")},handleExpand:function(t,o){t.stopPropagation();const a=document.querySelector(`.is-root_${X}`);if(a){const l=a.offsetLeft,r=a.offsetTop;o.expand=!o.expand;let d=!0;o.expand?(T.add(o.id),e.lazy&&e.load&&(d=!1,function(e,n,t){n(e,((n,o)=>{const{children:a}=S;e.isLeaf=!n.length,n.length&&(e.$$data[a]=n,o&&t())}))}(o,e.load,(()=>{C((()=>{$(a,l,r)}))})))):!o.expand&&o.children&&(T.delete(o.id),E(o.children)),C((()=>{d&&$(a,l,r)})),n(\"on-expand\",t,o.$$data,o)}},getExpandKeys:function(){return[...T]},setExpandKeys:function(n){T=new Set(n),O(e.data)},nodeMouseenter:function(e,t){return u.value&&(i.value=t),n(\"on-node-mouseenter\",e,t.$$data,t),!0},nodeMouseleave:function(e,t){return u.value&&(i.value=null),n(\"on-node-mouseleave\",e,t.$$data,t),!0},nodeContextmenu:function(n,t){n.stopPropagation(),n.preventDefault();const{defineMenus:o}=e;Array.isArray(o)?h.value=o:\"function\"==typeof o&&(h.value=o(n,t)||[]),p.value=!0,f.value=n.clientX,m.value=n.clientY,g.value=t},handleFocus:function(e,t,o){n(\"on-node-focus\",e,t,o)},handleBlur:function(e,t,o){const{id:a,label:l}=S,r=g.value.children||[];for(let e=r.length;e>0;e--){const n=r[e-1];if(\"\"===n[a]&&\"\"===n[l]){r.splice(e-1,1);break}}n(\"on-node-blur\",e,t,o)},handleClick:function(t,o){Y.get()||(clearTimeout(N),N=setTimeout((()=>{n(\"on-node-click\",t,o.$$data,o)}),e.clickDelay))},handleDblclick:function(e,t){clearTimeout(N),n(\"on-node-dblclick\",e,t.$$data,t)}}};var De=a({name:\"vue3TreeOrg\",components:{Tools:Q,CloneOrg:be,Draggable:he,Contextmenu:$e,TreeOrgNode:G},directives:{nodedrag:K},props:ze,emits:{\"on-drag\":({x:e,y:n})=>L(e)&&L(n),\"on-drag-stop\":({x:e,y:n})=>L(e)&&L(n),\"on-restore\":()=>!0,\"on-zoom\":e=>L(e),\"on-expand\":(e,n,t)=>e instanceof MouseEvent&&A(t)&&A(n),\"on-expand-all\":e=>\"boolean\"==typeof e,\"on-node-blur\":(e,n,t)=>e instanceof FocusEvent&&A(t)&&A(n),\"on-node-click\":(e,n,t)=>e instanceof MouseEvent&&A(t)&&A(n),\"on-node-dblclick\":(e,n,t)=>e instanceof MouseEvent&&A(t)&&A(n),\"on-node-mouseenter\":(e,n,t)=>e instanceof MouseEvent&&A(t)&&A(n),\"on-node-mouseleave\":(e,n,t)=>e instanceof MouseEvent&&A(t)&&A(n),\"on-contextmenu\":e=>A(e),\"on-node-copy\":e=>B(e),\"on-node-delete\":e=>A(e),\"on-node-drag-start\":e=>A(e),\"on-node-drag\":e=>A(e),\"on-node-drag-end\":(e,n)=>A(e)&&A(n),\"on-node-focus\":(e,n,t)=>e instanceof FocusEvent&&A(t)&&A(n)},setup(e,n){const t=!!n.slots.default,o=!!n.slots.expand,a=l(),r=l(),d=l();return{eleRef:a,treeRef:r,zoomRef:d,defaultSlot:t,expandSlot:o,...Ee(e,n,{eleRef:a,treeRef:r,zoomRef:d})}}});const Ce={ref:\"eleRef\",class:\"zm-tree-org\"},ke={class:\"tree-org-node__text\"},Me=f(\"span\",{class:\"tree-org-node__expand-btn\"},null,-1),Oe={class:\"tree-org-node__text\"},we=f(\"span\",{class:\"tree-org-node__expand-btn\"},null,-1);De.render=function(e,n,t,o,a,l){const r=$(\"tree-org-node\"),d=$(\"Draggable\"),s=$(\"Tools\"),v=$(\"clone-org\"),g=$(\"Contextmenu\");return c(),u(\"div\",Ce,[f(\"div\",{ref:\"zoomRef\",class:m([\"zoom-container\",{\"is-center\":e.center&&!e.horizontal}]),style:b(e.zoomStyle),onWheel:n[0]||(n[0]=(...n)=>e.zoomWheel&&e.zoomWheel(...n))},[z(d,{x:e.left,y:e.top,class:m({dragging:e.autoDragging}),onDragging:e.onDrag,onDragstop:e.onDragStop,draggable:e.draggable,\"drag-cancel\":e.dragCancel},{default:D((()=>[f(\"div\",{ref:\"treeRef\",class:m([\"tree-org\",{horizontal:e.horizontal,collapsable:e.collapsable}])},[z(r,{data:e.treeData,props:e.keys,lazy:e.lazy,suffix:e.suffix,horizontal:e.horizontal,labelStyle:e.labelStyle,collapsable:e.collapsable,renderContent:e.renderContent,selectedKey:e.selectedKey,defaultExpandLevel:e.defaultExpandLevel,selectedClassName:e.selectedClassName,labelClassName:e.labelClassName,vNodedrag:e.nodeargs,onOnExpand:e.handleExpand,onNodeClick:e.handleClick,onNodeDblclick:e.handleDblclick,onNodeMouseenter:e.nodeMouseenter,onNodeMouseleave:e.nodeMouseleave,onNodeContextmenu:e.nodeContextmenu,onNodeFocus:e.handleFocus,onNodeBlur:e.handleBlur},E({_:2},[e.defaultSlot?{name:\"default\",fn:D((({node:n})=>[x(e.$slots,\"default\",{node:n},(()=>[f(\"div\",ke,[f(\"span\",null,i(n.label),1)])]))]))}:void 0,e.expandSlot?{name:\"expand\",fn:D((({node:n})=>[x(e.$slots,\"expand\",{node:n},(()=>[Me]))]))}:void 0]),1032,[\"data\",\"props\",\"lazy\",\"suffix\",\"horizontal\",\"labelStyle\",\"collapsable\",\"renderContent\",\"selectedKey\",\"defaultExpandLevel\",\"selectedClassName\",\"labelClassName\",\"vNodedrag\",\"onOnExpand\",\"onNodeClick\",\"onNodeDblclick\",\"onNodeMouseenter\",\"onNodeMouseleave\",\"onNodeContextmenu\",\"onNodeFocus\",\"onNodeBlur\"])],2)])),_:3},8,[\"x\",\"y\",\"class\",\"onDragging\",\"onDragstop\",\"draggable\",\"drag-cancel\"])],38),e.tools.visible?(c(),N(s,{key:0,tools:e.tools.data,scale:e.zoomPercent,onOnExpand:e.expandChange,onOnScale:e.zoomOrgchart,onOnRestore:e.restoreOrgchart,onOnFullscreen:e.handleFullscreen},null,8,[\"tools\",\"scale\",\"onOnExpand\",\"onOnScale\",\"onOnRestore\",\"onOnFullscreen\"])):p(\"v-if\",!0),e.nodeDraggable?(c(),N(v,{key:1,modelValue:e.nodeMoving,\"onUpdate:modelValue\":n[1]||(n[1]=n=>e.nodeMoving=n),props:e.keys,data:e.cloneData,horizontal:e.horizontal,\"label-style\":e.labelStyle,collapsable:e.collapsable,\"render-content\":e.renderContent,\"label-class-name\":e.labelClassName},E({_:2},[e.defaultSlot?{name:\"default\",fn:D((({node:n})=>[x(e.$slots,\"default\",{node:n},(()=>[f(\"div\",Oe,[f(\"span\",null,i(n[e.keys.label]),1)])]))]))}:void 0,e.expandSlot?{name:\"expand\",fn:D((({node:n})=>[x(e.$slots,\"expand\",{node:n},(()=>[we]))]))}:void 0]),1032,[\"modelValue\",\"props\",\"data\",\"horizontal\",\"label-style\",\"collapsable\",\"render-content\",\"label-class-name\"])):p(\"v-if\",!0),e.nodeMenus.length?(c(),N(g,{key:2,modelValue:e.contextmenu,\"onUpdate:modelValue\":n[2]||(n[2]=n=>e.contextmenu=n),x:e.menuX,y:e.menuY,node:e.menuData,data:e.data,props:e.keys,menus:e.nodeMenus,disabled:e.disabled,\"node-add\":e.nodeAdd,\"node-delete\":e.nodeDelete,\"node-edit\":e.nodeEdit,\"node-copy\":e.nodeCopy,onContextmenu:n[3]||(n[3]=n=>{e.$emit(\"on-contextmenu\",n)}),onOnNodeCopy:n[4]||(n[4]=n=>{e.$emit(\"on-node-copy\",n)}),onOnNodeDelete:n[5]||(n[5]=n=>{e.$emit(\"on-node-delete\",n)})},null,8,[\"modelValue\",\"x\",\"y\",\"node\",\"data\",\"props\",\"menus\",\"disabled\",\"node-add\",\"node-delete\",\"node-edit\",\"node-copy\"])):p(\"v-if\",!0)],512)},De.__file=\"src/components/tree-org/src/tree.vue\",De.install=function(e){e.component(De.name,De)};const _e=[De];var Re={install:function(e){_e.forEach((n=>{e.component(n.name,n)})),e.use(V)},Vue3TreeOrg:De};export{De as Vue3TreeOrg,Re as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAwjB,IAAI,IAAE;AAAN,IAAsB,IAAE;AAAQ,IAAM,IAAE,SAAS,GAAE;AAAC,SAAM,sBAAoB,OAAO,UAAU,SAAS,KAAK,CAAC;AAAC;AAA/E,IAAiF,IAAE,SAAS,GAAE;AAAC,SAAM,YAAU,OAAO;AAAC;AAAvH,IAAyH,IAAE,SAAS,GAAE;AAAC,SAAM,YAAU,OAAO;AAAC;AAA/J,IAAiK,IAAE,EAAC,OAAM,CAAC,GAAE,GAAE,MAAI,EAAE,CAAC,KAAG,aAAW,OAAO,GAAE,QAAO,CAAC,GAAE,GAAE,MAAI,EAAE,CAAC,KAAG,EAAE,CAAC,KAAG,YAAU,OAAO,GAAE,SAAQ,CAAC,GAAE,MAAI,EAAE,CAAC,KAAG,aAAW,OAAO,GAAE,SAAQ,CAAC,GAAE,MAAI,EAAE,CAAC,KAAG,aAAW,OAAO,GAAE,MAAK,CAAC,GAAE,MAAI,EAAE,CAAC,KAAG,aAAW,OAAO,GAAE,SAAQ,CAAC,GAAE,MAAI,EAAE,CAAC,KAAG,aAAW,OAAO,GAAE,QAAO,CAAC,GAAE,MAAI,EAAE,CAAC,KAAG,aAAW,OAAO,EAAC;AAAE,SAAS,EAAE,IAAE,WAAU;AAAC,MAAI,IAAE;AAAG,UAAO,GAAE;AAAA,IAAC,KAAI;AAAU,UAAE;AAAU;AAAA,IAAM,KAAI;AAAU,UAAE;AAAU;AAAA,IAAM,KAAI;AAAO,UAAE;AAAU;AAAA,IAAM,KAAI;AAAU,UAAE;AAAU;AAAA,IAAM,KAAI;AAAS,UAAE;AAAU;AAAA,IAAM,KAAI;AAAU,UAAE;AAAU;AAAA,IAAM;AAAQ,UAAE;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,EAAE,QAAM,SAAS,GAAE,IAAE,WAAU,IAAE,OAAG;AAAC,SAAM,YAAU,OAAO,KAAG,QAAQ,IAAI,CAAC,GAAE,SAAK,IAAE,QAAQ,IAAI,MAAM,CAAC,KAAI,cAAc,EAAE,CAAC,CAAC,iDAAiD,IAAE,QAAQ,IAAI,MAAM,CAAC,KAAI,UAAU,EAAE,CAAC,CAAC,GAAG,GAAE;AAAG,GAAE,EAAE,SAAO,SAAS,GAAE,GAAE,IAAE,WAAU;AAAC,SAAO,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,OAAM,cAAc,EAAE,CAAC,CAAC,qBAAqB,EAAE,CAAC,CAAC,4DAA2D,oBAAoB,EAAE,CAAC,CAAC,sDAAsD,EAAE,CAAC,CAAC,KAAI,wBAAwB,GAAE;AAAE,GAAE,EAAE,UAAQ,SAAS,GAAE,IAAE,OAAG;AAAC,SAAO,KAAK,SAAO,KAAK,MAAM,GAAE,WAAU,CAAC,GAAE;AAAE,GAAE,EAAE,UAAQ,SAAS,GAAE,IAAE,OAAG;AAAC,SAAO,KAAK,SAAO,KAAK,MAAM,GAAE,WAAU,CAAC,GAAE;AAAE,GAAE,EAAE,OAAK,SAAS,GAAE,IAAE,OAAG;AAAC,SAAO,KAAK,SAAO,KAAK,MAAM,GAAE,QAAO,CAAC,GAAE;AAAE,GAAE,EAAE,UAAQ,SAAS,GAAE,IAAE,OAAG;AAAC,SAAO,KAAK,SAAO,KAAK,MAAM,GAAE,WAAU,CAAC,GAAE;AAAE,GAAE,EAAE,SAAO,SAAS,GAAE,IAAE,OAAG;AAAC,SAAO,KAAK,SAAO,KAAK,MAAM,GAAE,UAAS,CAAC,GAAE;AAAE;AAAE,IAAI,IAAE,EAAC,MAAM,QAAQ,GAAE;AAAC,IAAE,OAAO,iBAAiB,OAAK,GAAE,EAAE,OAAO,MAAI,IAAE,OAAK,GAAE,SAAS;AAAC,EAAC;AAAE,IAAM,IAAE,2BAAU;AAAC,MAAI,IAAE;AAAG,SAAM,EAAC,KAAI,MAAI,GAAE,KAAI,OAAG;AAAC,QAAE;AAAA,EAAC,EAAC;AAAC,EAAE;AAA5D,IAA8D,IAAE,SAAS,GAAE,GAAE,GAAE;AAAC,QAAK,EAAC,IAAG,GAAE,UAAS,EAAC,IAAE;AAAE,MAAG,EAAE,CAAC,MAAI,EAAE,QAAO;AAAE,MAAG,MAAM,QAAQ,EAAE,CAAC,CAAC,GAAE;AAAC,UAAMA,KAAE,EAAE,CAAC;AAAE,aAAQC,KAAE,GAAEC,KAAEF,GAAE,QAAOC,KAAEC,IAAED,MAAI;AAAC,YAAMC,KAAEF,GAAEC,EAAC,GAAE,IAAE,EAAEC,IAAE,GAAE,CAAC;AAAE,UAAG,EAAE,QAAO;AAAA,IAAC;AAAA,EAAC;AAAC;AAA1O,IAA4O,IAAE,SAAS,GAAE,GAAE;AAAC,QAAK,EAAC,WAAU,GAAE,aAAY,GAAE,eAAc,EAAC,IAAE;AAAE,MAAG,EAAE,OAAM;AAAC,UAAK,EAAC,MAAK,EAAC,IAAE,GAAE,EAAC,IAAG,GAAE,KAAI,GAAE,UAAS,EAAC,IAAE,GAAE,IAAE,EAAE,MAAM,QAAO,IAAE,KAAK,MAAM,KAAK,UAAU,EAAE,MAAM,CAAC;AAAE,SAAG,CAAC,SAASD,IAAEE,IAAEC,IAAE;AAAC,YAAK,EAAC,UAASJ,GAAC,IAAEG;AAAE,UAAG,EAAEF,EAAC,EAAE,CAAAC,GAAED,EAAC;AAAA,eAAU,MAAM,QAAQA,EAAC,EAAE,UAAQE,KAAE,GAAEC,KAAEH,GAAE,QAAOE,KAAEC,IAAED,KAAI,CAAAD,GAAED,GAAEE,EAAC,CAAC;AAAE,eAASD,GAAED,IAAE;AAAC,YAAGG,GAAEH,EAAC,GAAE,MAAM,QAAQA,GAAED,EAAC,CAAC,GAAE;AAAC,gBAAMG,KAAEF,GAAED,EAAC;AAAE,mBAAQC,KAAE,GAAEG,KAAED,GAAE,QAAOF,KAAEG,IAAEH,KAAI,CAAAC,GAAEC,GAAEF,EAAC,CAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,EAAE,GAAE,GAAG,SAASA,IAAE;AAAC,kBAAU,OAAOA,GAAE,CAAC,KAAG,OAAKA,GAAE,CAAC,EAAE,QAAQ,YAAY,MAAIA,GAAE,CAAC,IAAE,cAAcA,GAAE,CAAC,CAAC;AAAA,IAAG,CAAE,GAAE,KAAG,MAAM,QAAQ,EAAE,CAAC,CAAC,MAAI,EAAE,CAAC,IAAE,CAAC,IAAG,EAAE,EAAE,GAAG,IAAE,EAAE,EAAE,EAAE,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,EAAE,KAAK,CAAC,IAAE,EAAE,CAAC,IAAE,CAAC,CAAC,MAAI,CAAC,SAASA,IAAEE,IAAE;AAAC,YAAK,EAAC,MAAKC,IAAE,MAAKJ,IAAE,aAAYE,GAAC,IAAEC,IAAE,EAAC,IAAGE,IAAE,KAAIC,IAAE,UAASC,GAAC,IAAEH,IAAEI,KAAE,EAAER,IAAEI,IAAEH,GAAEK,EAAC,CAAC,GAAEG,KAAED,GAAED,EAAC;AAAE,UAAIG;AAAE,eAAQP,KAAE,GAAEC,KAAEK,GAAE,QAAON,KAAEC,IAAED,KAAI,KAAGM,GAAEN,EAAC,EAAEE,EAAC,MAAIJ,GAAEI,EAAC,GAAE;AAAC,QAAAI,GAAE,OAAON,IAAE,CAAC,GAAEO,KAAEP;AAAE;AAAA,MAAK;AAAC,YAAM,IAAEF,GAAEM,EAAC;AAAE,MAAAL,MAAG,WAASQ,MAAG,MAAIT,GAAEM,EAAC,IAAE,CAAC,GAAE,EAAE,QAAS,CAAAN,OAAG;AAAC,QAAAA,GAAEK,EAAC,IAAEE,GAAEH,EAAC;AAAA,MAAC,CAAE,GAAEG,GAAED,EAAC,EAAE,OAAOG,IAAE,GAAE,GAAG,CAAC;AAAA,IAAE,EAAE,GAAE,CAAC,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,GAAE,EAAE,CAAC,IAAE,EAAE,CAAC,EAAE,KAAK,CAAC,IAAE,EAAE,CAAC,IAAE,CAAC,EAAE,OAAO,CAAC;AAAA,EAAE;AAAC;AAA/mC,IAAinC,IAAE,EAAC,YAAY,GAAE,GAAE;AAAC,QAAK,EAAC,GAAE,GAAE,GAAE,EAAC,IAAE,EAAE,WAAU,EAAC,MAAK,GAAE,UAAS,GAAE,MAAK,GAAE,MAAK,GAAE,eAAc,GAAE,WAAU,EAAC,IAAE,EAAE,OAAM,EAAC,OAAM,EAAC,IAAE,GAAE,IAAE,EAAC,GAAG,EAAC;AAAE,IAAE,iBAAiB,aAAa,SAAST,IAAE;AAAC,UAAME,KAAEF,GAAE;AAAO,QAAG,UAAK,KAAG,MAAIA,GAAE,UAAQ,EAAE,WAAS,EAAE,OAAO,cAAYE,GAAE,UAAU,QAAQ,mBAAmB,IAAE,GAAG,QAAM;AAAG,KAAC,SAASF,IAAE;AAAC,MAAAA,GAAE,gBAAgB,GAAE,IAAEA,GAAE,SAAQ,IAAEA,GAAE,SAAQ,IAAE,GAAE,EAAE,YAAY,QAAM;AAAG,YAAK,EAAC,MAAKE,IAAE,aAAYC,GAAC,IAAE;AAAE,UAAGA,IAAE;AAAC,cAAK,EAAC,UAASH,GAAC,IAAEE,IAAEC,KAAE,EAAC,GAAG,EAAE,OAAM;AAAE,QAAAA,GAAEH,EAAC,IAAE,CAAC,GAAE,EAAE,UAAU,QAAM,EAAEG,EAAC;AAAA,MAAC,MAAM,GAAE,UAAU,QAAM,EAAE,EAAE,MAAM;AAAA,IAAC,GAAGH,EAAC,GAAE,SAAS,iBAAiB,aAAY,CAAC,GAAE,SAAS,iBAAiB,WAAUU,EAAC,GAAE,EAAE,OAAO;AAAA,EAAC,CAAE;AAAE,MAAI,GAAE,IAAE,GAAE,IAAE,OAAG,IAAE,GAAE,IAAE;AAAE,WAAS,EAAER,IAAE;AAAC,WAAOA,GAAE,eAAe,GAAE,EAAE,KAAK,IAAIA,GAAE,UAAQ,CAAC,IAAE,KAAG,KAAK,IAAIA,GAAE,UAAQ,CAAC,IAAE,OAAK,MAAI,IAAE,MAAG,SAASA,IAAE;AAAC,QAAE,IAAI,IAAE,GAAE,EAAE,WAAW,QAAM,MAAG,EAAE,SAAO;AAAG,UAAIC,KAAE;AAAE,aAAK,CAACA,GAAE,UAAU,SAAS,eAAe,IAAG,MAAGA,GAAE,YAAWA,KAAEA,GAAE;AAAa,WAAG,GAAE,IAAE,SAAS,cAAc,iBAAiB,GAAE,MAAI,EAAE,MAAM,QAAM,GAAGA,GAAE,WAAW,MAAK,EAAE,MAAM,UAAQ,OAAM,EAAE,MAAM,OAAKD,GAAE,UAAQ,IAAE,MAAK,EAAE,MAAM,MAAIA,GAAE,UAAQ,IAAE;AAAA,IAAK,EAAEA,EAAC,IAAG,KAAG,KAAG,KAAG,MAAI,EAAE,MAAM,OAAKA,GAAE,UAAQ,IAAE,MAAK,EAAE,MAAM,MAAIA,GAAE,UAAQ,IAAE,OAAM,KAAK,EAAE,MAAM,KAAG,KAAG,KAAG,EAAE,MAAM,OAAKA,GAAE,UAAQ,IAAE,MAAK,KAAK,EAAE,MAAM,KAAG,MAAK,KAAG,MAAI,EAAE,MAAM,MAAIA,GAAE,UAAQ,MAAK,EAAE,MAAM;AAAA,EAAI;AAAC,WAASQ,GAAEV,IAAE;AAAC,QAAG,SAAS,oBAAoB,aAAY,CAAC,GAAE,SAAS,oBAAoB,WAAUU,EAAC,GAAE,GAAE;AAAC,UAAG,cAAY,OAAO,GAAE;AAAC,cAAMR,KAAE,EAAE,GAAE,EAAE,UAAU,KAAK;AAAE,QAAAA,MAAGA,GAAE,OAAKA,GAAE,KAAM,MAAI;AAAC,YAAEF,EAAC;AAAA,QAAC,GAAI,MAAI;AAAA,QAAC,CAAE,IAAE,UAAKE,MAAG,EAAEF,EAAC;AAAA,MAAC,MAAM,GAAEA,EAAC;AAAE,UAAE,OAAG,IAAE,MAAK,EAAE,SAAO,OAAG,EAAE,WAAW,QAAM,OAAG,WAAY,MAAI;AAAC,UAAE,IAAI,KAAE;AAAA,MAAC,GAAG,GAAG;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAE;AAAC,UAAME,KAAE,SAAS,cAAc,wBAAwB;AAAE,QAAGA,MAAGA,GAAE,SAASF,GAAE,MAAM,EAAE,QAAO,EAAE,KAAK,GAAE;AAAG,MAAE,GAAE,CAAC,GAAE,EAAE,KAAK;AAAA,EAAC;AAAC,WAAS,EAAEA,IAAE;AAAC,gBAAUA,KAAE,WAASA,KAAE,UAAQA,MAAG,EAAE,oBAAmB,GAAE,EAAE,UAAU,KAAK,IAAE,EAAE,gBAAe,CAAC,IAAE,EAAE,sBAAqB,CAAC;AAAA,EAAC;AAAC,EAAC;AAAE,IAAM,IAAE,EAAC,SAAQ,eAAc,YAAW,kBAAiB,eAAc,qBAAoB,cAAa,oBAAmB,cAAa,mBAAkB;AAAE,SAAS,EAAE,GAAE,GAAE;AAAC,MAAG,cAAY,OAAO,EAAE,QAAO,SAAS,GAAE;AAAC,MAAE,OAAO,UAAU,QAAQ,mBAAmB,IAAE,MAAI,EAAE,GAAE,CAAC;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE,CAAC,GAAE,GAAE,MAAI,EAAE,MAAM,QAAQ,EAAE,CAAC,CAAC,KAAG,EAAE,CAAC,EAAE,SAAO,MAAI,CAAC,KAAG,EAAE;AAA9D,IAAqE,IAAE,CAAC,GAAE,GAAE,MAAI;AAAC,QAAK,EAAC,OAAM,EAAC,IAAE,GAAE,IAAE,CAAC,eAAe,GAAE,IAAE,CAAC,GAAE,EAAC,QAAO,GAAE,UAAS,GAAE,IAAG,EAAC,IAAE;AAAE,SAAO,EAAE,GAAE,YAAW,EAAE,IAAI,IAAE,EAAE,KAAK,SAAS,IAAE,EAAE,eAAa,CAAC,KAAG,EAAE,KAAK,WAAW,GAAE,EAAE,UAAQ,EAAE,KAAK,uBAAuB,GAAE,EAAE,KAAK,EAAE,GAAE,GAAE,CAAC,CAAC,GAAE,EAAE,eAAa,CAAC,KAAG,EAAE,KAAK,EAAE,GAAE,GAAE,CAAC,CAAC,GAAE,eAAE,EAAE,OAAM,EAAC,OAAM,GAAE,KAAI,EAAC,GAAE,CAAC,GAAE,CAAC,CAAC,OAAE,CAAC,EAAE,MAAM,CAAC,CAAC;AAAC;AAA3X,IAA6X,IAAE,CAAC,GAAE,GAAE,MAAI;AAAC,QAAK,EAAC,OAAM,EAAC,IAAE,GAAE,IAAE,EAAE,OAAM,IAAE,EAAE,eAAc,EAAC,OAAM,EAAC,IAAE,GAAE,IAAE,CAAC;AAAE,MAAG,EAAE,MAAM,QAAQ,GAAE,KAAK,EAAE,MAAM,QAAQ,EAAC,MAAK,EAAC,CAAC,CAAC;AAAA,WAAU,cAAY,OAAO,GAAE;AAAC,MAAE,QAAQ,oFAAoF;AAAE,UAAM,IAAE,EAAE,GAAE,CAAC;AAAE,SAAG,EAAE,KAAK,CAAC;AAAA,EAAC,MAAM,GAAE,KAAK,EAAE,OAAM,EAAC,OAAM,sBAAqB,GAAE,CAAC,CAAC;AAAE,IAAE,eAAa,CAAC,EAAE,GAAE,YAAW,EAAE,IAAI,KAAG,EAAE,MAAM,CAACA,IAAE,GAAE,MAAI;AAAC,UAAK,EAAC,OAAM,EAAC,IAAE,GAAEC,KAAE,EAAE,YAAWG,KAAE,CAAC,uBAAuB;AAAE,MAAE,UAAQA,GAAE,KAAK,UAAU;AAAE,UAAMC,KAAE,CAAC;AAAE,WAAO,EAAE,MAAM,SAAOA,GAAE,KAAK,EAAE,MAAM,OAAO,EAAC,MAAK,EAAC,CAAC,CAAC,IAAEA,GAAE,KAAKL,GAAE,QAAO,EAAC,OAAM,4BAA2B,CAAC,CAAC,GAAEA,GAAE,QAAO,EAAC,OAAMI,IAAE,aAAY,CAAAJ,OAAG;AAAC,MAAAA,GAAE,gBAAgB;AAAA,IAAC,GAAE,YAAW,CAAAA,OAAG;AAAC,MAAAA,GAAE,gBAAgB;AAAA,IAAC,GAAE,SAAQ,CAAAA,OAAG;AAAC,MAAAA,GAAE,gBAAgB,GAAEC,MAAGA,GAAED,IAAE,CAAC;AAAA,IAAC,EAAC,GAAEK,EAAC;AAAA,EAAC,GAAG,GAAE,GAAE,CAAC,CAAC;AAAE,QAAM,IAAE,CAAC,sBAAsB;AAAE,MAAG,EAAC,YAAW,GAAE,gBAAe,GAAE,mBAAkB,GAAE,aAAY,EAAC,IAAE;AAAE,gBAAY,OAAO,MAAI,IAAE,EAAE,CAAC,IAAG,KAAG,EAAE,KAAK,CAAC,GAAE,EAAE,aAAW,EAAE,KAAK,EAAE,SAAS,GAAE,cAAY,OAAO,MAAI,IAAE,EAAE,CAAC,IAAG,WAAS,MAAI,IAAE,MAAM,QAAQ,CAAC,IAAE,IAAE,CAAC,CAAC,IAAG,KAAG,KAAG,EAAE,SAAS,EAAE,EAAE,KAAG,EAAE,KAAK,CAAC;AAAE,QAAM,IAAE,CAAC,wBAAwB;AAAE,IAAE,UAAQ,EAAE,KAAK,WAAW,EAAE,MAAM,EAAE,GAAE,EAAE,SAAO,EAAE,KAAK,UAAU,GAAE,EAAE,aAAW,EAAE,KAAK,SAAS;AAAE,QAAM,IAAE,iBAAE,UAAU,GAAEK,KAAE,CAAC;AAAE,IAAE,aAAW,KAAG,CAAC,EAAE,UAAQA,GAAE,KAAK,CAAC,GAAE,OAAO,OAAO,EAAC,MAAK,EAAC,GAAE,EAAE,SAAS,GAAE,IAAG,EAAC,GAAE,MAAG,GAAE,KAAE,CAAC,CAAC;AAAE,QAAM,IAAE,CAAC;AAAE,aAAUV,MAAK,EAAE,KAAG,OAAO,UAAU,eAAe,KAAK,GAAEA,EAAC,GAAE;AAAC,UAAM,IAAE,EAAE,EAAEA,EAAC,CAAC;AAAE,UAAI,EAAEA,EAAC,IAAE,EAAE,GAAE,CAAC;AAAA,EAAE;AAAC,QAAM,IAAE,EAAE,aAAY,IAAE,EAAE,YAAW,IAAE,iBAAE,OAAO,GAAE,IAAE,CAAC,CAAC,OAAE,EAAE,SAAS,CAAC;AAAE,SAAO,KAAG,EAAE,KAAK,CAAC,GAAE,EAAE,SAAS,CAAC,GAAE,EAAE,OAAM,EAAC,OAAM,EAAC,GAAE,CAAC,eAAE,EAAE,OAAM,EAAC,OAAM,GAAE,OAAM,EAAE,QAAM,EAAE,QAAM,GAAE,GAAG,EAAC,GAAE,CAAC,GAAEU,EAAC,GAAE,eAAE,EAAE,YAAW,EAAC,OAAM,2BAA0B,aAAY,WAAU,OAAM,EAAE,OAAM,SAAQ,CAAAV,OAAG;AAAC,SAAG,EAAEA,IAAE,EAAE,QAAO,CAAC;AAAA,EAAC,GAAE,SAAQ,CAAAA,OAAG;AAAC,MAAE,QAAMA,GAAE,OAAO;AAAA,EAAK,GAAE,QAAO,CAAAA,OAAG;AAAC,eAAS,EAAE,OAAO,YAAU,EAAE,OAAO,UAAQ,QAAI,EAAE,OAAO,EAAE,KAAK,IAAEA,GAAE,OAAO,OAAM,EAAE,YAAU,OAAG,KAAG,EAAEA,IAAE,EAAE,QAAO,CAAC;AAAA,EAAC,GAAE,SAAQ,CAAAA,OAAGA,GAAE,gBAAgB,EAAC,CAAC,GAAE,CAAC,CAAC,CAAC;AAAC;AAA7tE,IAA+tE,IAAE,CAAC,GAAE,GAAE,MAAI;AAAC,MAAG,MAAM,QAAQ,CAAC,KAAG,EAAE,QAAO;AAAC,UAAM,IAAE,EAAE,OAAQ,CAAAA,OAAG,CAACA,GAAE,QAAS,EAAE,IAAK,CAAAE,OAAG,EAAE,GAAEA,IAAE,CAAC,CAAE;AAAE,WAAO,EAAE,OAAM,EAAC,OAAM,0BAAyB,GAAE,CAAC;AAAA,EAAC;AAAC,SAAM;AAAE;AAA53E,IAA83E,IAAE,CAAC,GAAE,MAAI,EAAE,QAAM,MAAI,OAAO,KAAK,EAAE,IAAI,EAAE,UAAQ,EAAE,KAAK,SAAO,CAAC,EAAE,SAAQ,EAAE,GAAE,EAAE,MAAK,CAAC,KAAG;AAAG,EAAE,aAAW,EAAC,OAAM,EAAC,QAAQ,GAAE,EAAC,OAAM,EAAC,GAAE;AAAC,OAAG,EAAE,MAAM;AAAC,GAAE,QAAQ,GAAE,EAAC,OAAM,EAAC,GAAE;AAAC,OAAG,EAAE,MAAM;AAAC,EAAC,GAAE,UAAS,EAAC;AAAE,IAAI,IAAE,gBAAE,EAAC,OAAM,EAAC,OAAM,QAAO,OAAM,OAAM,GAAE,MAAM,GAAE,EAAC,MAAK,EAAC,GAAE;AAAC,QAAM,IAAE,IAAE,KAAE,GAAE,IAAE,IAAE,KAAE;AAAE,WAAS,EAAEF,IAAE;AAAC,MAAE,QAAM,CAAC,EAAE,OAAM,EAAE,gBAAeA,EAAC;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,aAAS,gBAAc,SAAS,mBAAiB,SAAS,sBAAoB,CAAC,EAAE,SAAO,EAAE,KAAK;AAAA,EAAC;AAAC,QAAM,IAAE,SAAG,MAAI,EAAE,QAAM,SAAO,MAAO,GAAE,IAAE,SAAG,MAAI,EAAE,QAAM,SAAO,IAAK;AAAE,SAAO,UAAG,MAAI;AAAC,WAAO,iBAAiB,UAAS,CAAC;AAAA,EAAC,CAAE,GAAE,gBAAG,MAAI;AAAC,WAAO,oBAAoB,UAAS,CAAC;AAAA,EAAC,CAAE,GAAE,EAAC,UAAS,GAAE,YAAW,GAAE,aAAY,GAAE,YAAW,GAAE,cAAa,WAAU;AAAC,MAAE,QAAM,CAAC,EAAE,OAAM,EAAE,UAAU;AAAA,EAAC,GAAE,aAAY,SAASA,IAAE;AAAC,cAAQA,KAAE,EAAE,WAAU,GAAE,IAAE,SAAOA,KAAE,EAAE,WAAU,IAAG,IAAE,EAAE,WAAW;AAAA,EAAC,GAAE,kBAAiB,EAAC;AAAC,EAAC,CAAC;AAAE,IAAM,IAAE,EAAC,OAAM,iBAAgB;AAA/B,IAAiC,KAAG,EAAC,KAAI,GAAE,OAAM,kBAAiB;AAAlE,IAAoE,KAAG,CAAC,OAAO;AAA/E,IAAiF,KAAG,EAAC,OAAM,cAAa;AAAxG,IAA0G,KAAG,CAAC,gBAAE,QAAO,EAAC,OAAM,eAAc,GAAE,KAAI,EAAE,CAAC;AAArJ,IAAuJ,KAAG,CAAC,gBAAE,QAAO,EAAC,OAAM,eAAc,GAAE,KAAI,EAAE,CAAC;AAAlM,IAAoM,KAAG,CAAC,gBAAE,QAAO,EAAC,OAAM,kBAAiB,GAAE,MAAK,EAAE,CAAC;AAAnP,IAAqP,KAAG,CAAC,OAAO;AAAhQ,IAAkQ,KAAG,EAAC,OAAM,cAAa;AAAE,SAAS,GAAG,GAAE;AAAC,SAAM,cAAY,OAAO,KAAG,wBAAsB,OAAO,UAAU,SAAS,KAAK,CAAC;AAAC;AAAC,SAAS,GAAG,GAAE,GAAE,GAAE,IAAE,GAAE;AAAC,SAAM,EAAC,QAAO,KAAK,MAAM,IAAE,IAAE,EAAE,CAAC,CAAC,IAAE,EAAE,CAAC,GAAE,QAAO,KAAK,MAAM,IAAE,IAAE,EAAE,CAAC,CAAC,IAAE,EAAE,CAAC,EAAC;AAAC;AAAC,SAAS,GAAG,GAAE,GAAE,GAAE;AAAC,SAAO,IAAE,IAAE,IAAE,IAAE,IAAE,IAAE;AAAC;AAAC,EAAE,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAO,UAAE,GAAE,mBAAE,OAAM,GAAE,CAAC,EAAE,MAAM,SAAO,UAAE,GAAE,mBAAE,OAAM,IAAG,gBAAE,EAAE,KAAK,GAAE,CAAC,KAAG,mBAAE,QAAO,IAAE,GAAE,EAAE,MAAM,UAAQ,UAAE,GAAE,mBAAE,OAAM,EAAC,KAAI,GAAE,SAAQ,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,IAAIE,OAAI,EAAE,gBAAc,EAAE,aAAa,GAAGA,EAAC,IAAG,OAAM,EAAE,aAAY,OAAM,sBAAqB,GAAE,CAAC,gBAAE,QAAO,IAAG,CAAC,gBAAE,KAAI,EAAC,OAAM,eAAE,CAAC,YAAW,EAAE,WAAS,kBAAgB,aAAa,CAAC,EAAC,GAAE,MAAK,CAAC,CAAC,CAAC,CAAC,GAAE,GAAE,EAAE,KAAG,mBAAE,QAAO,IAAE,GAAE,EAAE,MAAM,QAAM,UAAE,GAAE,mBAAE,OAAM,EAAC,KAAI,GAAE,SAAQ,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,CAAAA,OAAG,EAAE,YAAY,KAAK,IAAG,OAAM,MAAK,OAAM,+BAA8B,GAAE,EAAE,KAAG,mBAAE,QAAO,IAAE,GAAE,EAAE,MAAM,QAAM,UAAE,GAAE,mBAAE,OAAM,EAAC,KAAI,GAAE,SAAQ,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,CAAAA,OAAG,EAAE,YAAY,IAAI,IAAG,OAAM,MAAK,OAAM,8BAA6B,GAAE,EAAE,KAAG,mBAAE,QAAO,IAAE,GAAE,EAAE,MAAM,WAAS,UAAE,GAAE,mBAAE,OAAM,EAAC,KAAI,GAAE,SAAQ,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,CAAAA,OAAG,EAAE,YAAY,SAAS,IAAG,OAAM,MAAK,OAAM,sBAAqB,GAAE,EAAE,KAAG,mBAAE,QAAO,IAAE,GAAE,EAAE,MAAM,cAAY,UAAE,GAAE,mBAAE,OAAM,EAAC,KAAI,GAAE,SAAQ,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,IAAIA,OAAI,EAAE,oBAAkB,EAAE,iBAAiB,GAAGA,EAAC,IAAG,OAAM,EAAE,YAAW,OAAM,sBAAqB,GAAE,CAAC,gBAAE,QAAO,IAAG,CAAC,gBAAE,KAAI,EAAC,OAAM,eAAE,CAAC,YAAW,EAAE,aAAW,sBAAoB,iBAAiB,CAAC,EAAC,GAAE,MAAK,CAAC,CAAC,CAAC,CAAC,GAAE,GAAE,EAAE,KAAG,mBAAE,QAAO,IAAE,CAAC,CAAC;AAAC,GAAE,EAAE,SAAO;AAAiC,IAAM,KAAG,SAAS,GAAE,GAAE,GAAE,IAAE,OAAG;AAAC,OAAG,KAAG,KAAG,EAAE,iBAAiB,GAAE,GAAE,CAAC;AAAC;AAAhE,IAAkE,KAAG,SAAS,GAAE,GAAE,GAAE,IAAE,OAAG;AAAC,OAAG,KAAG,KAAG,EAAE,oBAAoB,GAAE,GAAE,CAAC;AAAC;AAA/H,IAAiI,KAAG,CAAC,GAAE,EAAC,MAAK,EAAC,GAAE,MAAI;AAAC,QAAM,IAAE,EAAC,OAAM,aAAY,MAAK,aAAY,MAAK,UAAS,GAAE,IAAE,EAAC,OAAM,cAAa,MAAK,aAAY,MAAK,WAAU,GAAE,IAAE,EAAC,YAAW,QAAO,eAAc,QAAO,kBAAiB,QAAO,cAAa,OAAM,GAAE,IAAE,EAAC,YAAW,QAAO,eAAc,QAAO,kBAAiB,QAAO,cAAa,OAAM;AAAE,MAAI,IAAE;AAAE,QAAM,IAAE,IAAE,EAAE,CAAC,GAAE,IAAE,IAAE,EAAE,CAAC,GAAE,IAAE,IAAE,EAAE,CAAC,GAAE,IAAE,IAAE,EAAE,MAAM,GAAEQ,KAAE,IAAE,KAAE,GAAE,IAAE,IAAE,CAAC,GAAE,IAAE,IAAE,CAAC,GAAE,IAAE,IAAE,CAAC,GAAE,IAAE,IAAE,CAAC,GAAE,IAAE,IAAE,CAAC,GAAE,IAAE,IAAE,CAAC;AAAE,MAAI,IAAE,CAAC,GAAE,IAAE,CAAC;AAAE,WAAS,IAAG;AAAC,QAAE,EAAC,QAAO,GAAE,QAAO,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC,GAAE,IAAE,EAAC,SAAQ,KAAG,GAAE,SAAQ,IAAE,GAAE,UAAS,KAAG,GAAE,UAAS,IAAE,GAAE,QAAO,KAAG,GAAE,QAAO,IAAE,GAAE,WAAU,KAAG,GAAE,WAAU,IAAE,EAAC;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,QAAG,EAAE,QAAO;AAAC,YAAK,CAACV,IAAEE,EAAC,IAAE,EAAE;AAAE,QAAE,QAAMF,MAAG,GAAE,EAAE,QAAME,MAAG;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,QAAG,EAAE,UAAQ,EAAE,OAAO,OAAM;AAAC,YAAMF,KAAE,OAAO,iBAAiB,EAAE,OAAO,MAAM,YAAW,IAAI;AAAE,aAAM,CAAC,SAASA,GAAE,iBAAiB,OAAO,GAAE,EAAE,GAAE,SAASA,GAAE,iBAAiB,QAAQ,GAAE,EAAE,CAAC;AAAA,IAAC;AAAC,WAAM,CAAC,GAAE,CAAC;AAAA,EAAC;AAAC,WAAS,EAAED,IAAE;AAAC,QAAGA,cAAa,cAAY,MAAIA,GAAE,MAAM;AAAO,UAAME,KAAEF,GAAE,UAAQA,GAAE;AAAW,QAAGE,MAAG,EAAE,OAAO,SAAO,EAAE,OAAO,MAAM,SAASA,EAAC,GAAE;AAAC,UAAG,EAAE,cAAY,SAASD,IAAEE,IAAEC,IAAE;AAAC,YAAIJ,KAAEC;AAAE,cAAMC,KAAE,CAAC,WAAU,yBAAwB,sBAAqB,qBAAoB,kBAAkB,EAAE,KAAM,CAAAD,OAAGD,MAAG,GAAGA,GAAEC,EAAC,CAAC,CAAE,KAAG;AAAG,YAAG,CAAC,GAAGD,GAAEE,EAAC,CAAC,EAAE,QAAM;AAAG,WAAE;AAAC,cAAGF,GAAEE,EAAC,EAAEC,EAAC,EAAE,QAAM;AAAG,cAAGH,OAAII,GAAE,QAAM;AAAG,UAAAJ,KAAEA,GAAE;AAAA,QAAU,SAAOA;AAAG,eAAM;AAAA,MAAE,EAAEE,IAAE,EAAE,YAAW,EAAE,OAAO,KAAK,EAAE,QAAO,MAAKS,GAAE,QAAM;AAAI,QAAE,UAAQ,EAAE,QAAM,MAAG,EAAE,WAAW,GAAE,EAAE,iBAAgB,IAAE,IAAG,EAAE,cAAYA,GAAE,QAAM,OAAIX,cAAa,cAAY,EAAE,SAAOA,GAAE,OAAM,EAAE,SAAOA,GAAE,UAAQ,EAAE,SAAOA,GAAE,QAAQ,CAAC,EAAE,OAAM,EAAE,SAAOA,GAAE,QAAQ,CAAC,EAAE,QAAO,EAAE,OAAK,EAAE,OAAM,EAAE,QAAM,EAAE,OAAM,EAAE,MAAI,EAAE,OAAM,EAAE,SAAO,EAAE,OAAM,EAAE,WAAS,IAAE,EAAE,IAAG,GAAG,SAAS,iBAAgB,EAAE,MAAKY,EAAC,GAAE,GAAG,SAAS,iBAAgB,EAAE,MAAKC,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,UAAMV,KAAE,EAAE;AAAK,WAAM,EAAC,SAAQ,EAAE,QAAMA,GAAE,CAAC,GAAE,SAAQ,KAAK,OAAO,EAAE,QAAM,EAAE,QAAM,EAAE,SAAOA,GAAE,CAAC,CAAC,IAAEA,GAAE,CAAC,IAAE,EAAE,OAAM,UAAS,EAAE,QAAMA,GAAE,CAAC,GAAE,UAAS,KAAK,OAAO,EAAE,QAAM,EAAE,QAAM,EAAE,SAAOA,GAAE,CAAC,CAAC,IAAEA,GAAE,CAAC,IAAE,EAAE,OAAM,QAAO,EAAE,QAAMA,GAAE,CAAC,GAAE,QAAO,KAAK,OAAO,EAAE,QAAM,EAAE,QAAM,EAAE,SAAOA,GAAE,CAAC,CAAC,IAAEA,GAAE,CAAC,IAAE,EAAE,OAAM,WAAU,EAAE,QAAMA,GAAE,CAAC,GAAE,WAAU,KAAK,OAAO,EAAE,QAAM,EAAE,QAAM,EAAE,SAAOA,GAAE,CAAC,CAAC,IAAEA,GAAE,CAAC,IAAE,EAAE,MAAK;AAAA,EAAC;AAAC,WAAS,EAAEH,IAAE;AAAC,UAAME,KAAEF,GAAE,UAAQA,GAAE;AAAW,MAAE,OAAO,SAAO,CAAC,EAAE,OAAO,MAAM,SAASE,EAAC,KAAG,EAAE,SAAO,CAAC,EAAE,wBAAsB,EAAE,QAAM,OAAG,EAAE,aAAa,GAAE,EAAE,iBAAgB,KAAE;AAAA,EAAE;AAAC,WAASU,GAAER,IAAE;AAAC,IAAAO,GAAE,SAAO,SAASP,IAAE;AAAC,YAAMJ,KAAE,EAAE,MAAKE,KAAE,EAAE;AAAK,UAAI,IAAE,GAAE,IAAE;AAAE,MAAAE,cAAa,cAAY,IAAEA,GAAE,OAAM,IAAEA,GAAE,UAAQ,IAAEA,GAAE,QAAQ,CAAC,EAAE,OAAM,IAAEA,GAAE,QAAQ,CAAC,EAAE;AAAO,YAAM,IAAEJ,MAAG,QAAMA,KAAE,EAAE,SAAO,IAAE,GAAEQ,KAAER,MAAG,QAAMA,KAAE,EAAE,SAAO,IAAE,GAAE,EAAC,QAAOS,IAAE,QAAOC,GAAC,IAAE,GAAGR,IAAE,GAAEM,IAAE,EAAE,KAAK,GAAEM,KAAE,GAAG,EAAE,OAAKL,IAAE,EAAE,SAAQ,EAAE,OAAO,GAAEM,KAAE,GAAG,EAAE,MAAIL,IAAE,EAAE,QAAO,EAAE,MAAM,GAAE,IAAE,GAAG,EAAE,QAAMD,IAAE,EAAE,UAAS,EAAE,QAAQ,GAAE,IAAE,GAAG,EAAE,SAAOC,IAAE,EAAE,WAAU,EAAE,SAAS;AAAE,QAAE,QAAMI,IAAE,EAAE,QAAMC,IAAE,EAAE,QAAM,GAAE,EAAE,QAAM,GAAE,EAAE,YAAW,EAAE,OAAM,EAAE,KAAK;AAAA,IAAC,EAAEX,EAAC;AAAA,EAAC;AAAC,WAASS,KAAG;AAAC,MAAE,GAAEF,GAAE,UAAQA,GAAE,QAAM,OAAG,EAAE,YAAW,EAAE,OAAM,EAAE,KAAK;AAAA,EAAE;AAAC,gBAAG,MAAI;AAAC,MAAE;AAAA,EAAC,CAAE,GAAE,UAAG,MAAI;AAAC,KAAC,EAAE,oBAAkB,EAAE,OAAO,UAAQ,EAAE,OAAO,MAAM,cAAY,MAAI;AAAI,UAAK,CAACR,IAAEH,EAAC,IAAE,EAAE;AAAE,QAAG,EAAE,QAAMG,IAAE,EAAE,QAAMH,IAAE,EAAE,OAAO,OAAM;AAAC,YAAK,CAACC,IAAEE,EAAC,IAAE,SAASF,IAAE;AAAC,cAAME,KAAE,OAAO,iBAAiBF,EAAC;AAAE,eAAM,CAAC,WAAWE,GAAE,iBAAiB,OAAO,CAAC,GAAE,WAAWA,GAAE,iBAAiB,QAAQ,CAAC,CAAC;AAAA,MAAC,EAAE,EAAE,OAAO,KAAK;AAAE,QAAE,QAAMF,IAAE,EAAE,QAAME,IAAE,EAAE,QAAM,EAAE,QAAM,EAAE,QAAM,EAAE,OAAM,EAAE,QAAM,EAAE,QAAM,EAAE,QAAM,EAAE;AAAA,IAAK;AAAC,OAAG,SAAS,iBAAgB,aAAY,CAAC,GAAE,GAAG,SAAS,iBAAgB,wBAAuB,CAAC,GAAE,GAAG,QAAO,UAAS,CAAC;AAAA,EAAC,CAAE,GAAE,YAAG,MAAI;AAAC,OAAG,SAAS,iBAAgB,aAAY,CAAC,GAAE,GAAG,SAAS,iBAAgB,cAAaU,EAAC,GAAE,GAAG,SAAS,iBAAgB,aAAYD,EAAC,GAAE,GAAG,SAAS,iBAAgB,aAAYA,EAAC,GAAE,GAAG,SAAS,iBAAgB,WAAUC,EAAC,GAAE,GAAG,SAAS,iBAAgB,wBAAuB,CAAC,GAAE,GAAG,QAAO,UAAS,CAAC;AAAA,EAAC,CAAE;AAAE,QAAMG,KAAE,SAAG,OAAK,EAAC,WAAU,aAAa,EAAE,KAAK,OAAO,EAAE,KAAK,OAAM,QAAO,EAAE,OAAM,GAAGL,GAAE,SAAO,EAAE,oBAAkB,IAAE,EAAC,EAAG;AAAE,SAAO,MAAG,MAAI,EAAE,QAAS,CAAAV,OAAG;AAAC,MAAE,QAAMA,IAAE,EAAEA,KAAE,cAAY,aAAa;AAAA,EAAC,CAAE,GAAE,MAAG,MAAI,EAAE,GAAI,CAAAA,OAAG;AAAC,KAACA,MAAG,KAAG,WAASA,QAAK,EAAE,QAAMA;AAAA,EAAE,CAAE,GAAE,MAAG,MAAI,EAAE,GAAI,CAAAE,OAAG;AAAC,IAAAQ,GAAE,UAAQ,EAAE,WAAS,IAAE,EAAE,IAAG,SAASR,IAAE;AAAC,YAAK,EAAC,QAAOC,GAAC,IAAE,GAAG,EAAE,MAAKD,IAAE,EAAE,OAAM,EAAE,KAAK,GAAEH,KAAE,GAAGI,IAAE,EAAE,SAAQ,EAAE,OAAO;AAAE,QAAE,QAAMJ,IAAE,EAAE,QAAM,EAAE,QAAM,EAAE,QAAMA;AAAA,IAAC,EAAEG,EAAC;AAAA,EAAE,CAAE,GAAE,MAAG,MAAI,EAAE,GAAI,CAAAA,OAAG;AAAC,IAAAQ,GAAE,UAAQ,EAAE,WAAS,IAAE,EAAE,IAAG,SAASR,IAAE;AAAC,YAAK,EAAC,QAAOC,GAAC,IAAE,GAAG,EAAE,MAAK,EAAE,OAAMD,IAAE,EAAE,KAAK,GAAEH,KAAE,GAAGI,IAAE,EAAE,QAAO,EAAE,MAAM;AAAE,QAAE,QAAMJ,IAAE,EAAE,QAAM,EAAE,QAAM,EAAE,QAAMA;AAAA,IAAC,EAAEG,EAAC;AAAA,EAAE,CAAE,GAAE,EAAC,SAAQ,GAAE,UAASQ,IAAE,OAAMK,IAAE,0BAAyB,GAAE,kBAAiB,SAASf,IAAE;AAAC,QAAE,GAAE,EAAEA,EAAC;AAAA,EAAC,GAAE,kBAAiB,SAASA,IAAE;AAAC,QAAE,GAAE,EAAEA,EAAC;AAAA,EAAC,EAAC;AAAC;AAAuC,OAAO,OAAO,CAAC,CAAC,GAAuC,OAAO,OAAO,CAAC,CAAC;AAAE,IAAM,KAAG,OAAO,SAAS;AAAE,SAAS,GAAG,GAAE,GAAE;AAAC,MAAG,UAAQ,IAAE,MAAI,YAAU,OAAO,KAAG,EAAE,qBAAqB,QAAO;AAAE,MAAI;AAAE,QAAK,EAAC,QAAO,GAAE,UAAS,GAAE,SAAQ,GAAE,MAAK,GAAE,WAAU,EAAC,IAAE,GAAE,IAAE,KAAG,IAAE,CAAAA,OAAG;AAAC,QAAIG,KAAE,OAAGF,KAAE,CAAC;AAAE,QAAG,MAAIA,KAAE,CAAC,GAAG,GAAE,CAAC,GAAEE,KAAEA,MAAGF,GAAE,SAASD,EAAC,IAAG,MAAIG,KAAEA,MAAG,EAAEH,EAAC,IAAG,CAACG,MAAGF,GAAE,SAAO,GAAE;AAAC,YAAME,KAAE,CAAC,GAAG,IAAI,IAAIF,EAAC,CAAC,EAAE,IAAK,CAAAD,OAAG,KAAK,UAAUA,EAAC,CAAE,EAAE,KAAK,IAAI;AAAE,WAAE,kCAAkC,IAAE,cAAc,CAAC,MAAI,EAAE,sBAAsBG,EAAC,gBAAgB,KAAK,UAAUH,EAAC,CAAC,GAAG;AAAA,IAAC;AAAC,WAAOG;AAAA,EAAC,IAAE;AAAO,SAAM,EAAC,MAAK,YAAU,OAAO,KAAG,OAAO,sBAAsB,CAAC,EAAE,SAAS,EAAE,IAAE,EAAE,EAAE,IAAE,GAAE,WAAU,GAAE,sBAAqB,MAAG,SAAQ,GAAE,UAAS,CAAC,CAAC,EAAC;AAAC;AAAC,IAAM,KAAG,OAAG,SAASH,IAAE;AAAC,MAAI,IAAE;AAAG,QAAM,IAAEA,KAAEA,GAAE,SAAO,GAAE,IAAE,CAAC;AAAE,SAAK,EAAE,IAAE,KAAG;AAAC,UAAMG,KAAEH,GAAE,CAAC;AAAE,MAAEG,GAAE,CAAC,CAAC,IAAEA,GAAE,CAAC;AAAA,EAAC;AAAC,SAAO;AAAC,EAAE,OAAO,QAAQ,CAAC,EAAE,IAAK,CAAC,CAACH,IAAE,CAAC,MAAI,CAACA,IAAE,GAAG,GAAEA,EAAC,CAAC,CAAE,CAAC;AAAzJ,IAA2J,KAAG,QAAI,EAAC,CAAC,EAAE,GAAE,EAAC;AAAG,IAAI,KAAG,gBAAE,EAAC,SAAQ,MAAG,MAAK,aAAY,OAAM,GAAG,EAAC,WAAU,EAAC,MAAK,QAAO,SAAQ,eAAc,GAAE,oBAAmB,EAAC,MAAK,QAAO,SAAQ,YAAW,GAAE,mBAAkB,EAAC,MAAK,QAAO,SAAQ,WAAU,GAAE,iBAAgB,EAAC,MAAK,QAAO,SAAQ,SAAQ,GAAE,mBAAkB,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,kBAAiB,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,qBAAoB,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,WAAU,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,GAAE,EAAC,MAAK,QAAO,SAAQ,EAAC,GAAE,GAAE,EAAC,MAAK,QAAO,SAAQ,EAAC,GAAE,GAAE,EAAC,MAAK,CAAC,QAAO,MAAM,GAAE,SAAQ,QAAO,WAAU,OAAG,YAAU,OAAO,IAAE,WAAS,IAAE,KAAG,EAAC,GAAE,YAAW,QAAO,MAAK,EAAC,MAAK,QAAO,SAAQ,QAAO,WAAU,OAAG,CAAC,KAAI,KAAI,MAAM,EAAE,SAAS,CAAC,EAAC,GAAE,MAAK,EAAC,MAAK,OAAM,SAAQ,MAAI,CAAC,GAAE,CAAC,EAAC,GAAE,QAAO,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,OAAM,EAAC,MAAK,QAAO,SAAQ,GAAE,WAAU,OAAG,IAAE,EAAC,EAAC,CAAC,GAAE,OAAM,EAAC,WAAU,MAAI,MAAG,aAAY,MAAI,MAAG,UAAS,CAAC,GAAE,MAAI,EAAE,CAAC,KAAG,EAAE,CAAC,GAAE,UAAS,CAAC,GAAE,MAAI,EAAE,CAAC,KAAG,EAAE,CAAC,GAAE,iBAAgB,OAAG,aAAW,OAAO,EAAC,GAAE,MAAM,GAAE,GAAE;AAAC,QAAM,IAAE,IAAE,GAAE,IAAE,GAAG,GAAE,GAAE,EAAC,QAAO,EAAC,CAAC;AAAE,SAAM,EAAC,KAAQ,QAAO,GAAE,GAAG,EAAC;AAAC,EAAC,CAAC;AAAE,GAAG,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAO,UAAE,GAAE,mBAAE,OAAM,EAAC,OAAM,eAAE,EAAE,KAAK,GAAE,OAAM,eAAE,CAAC,EAAC,CAAC,EAAE,eAAe,GAAE,EAAE,SAAQ,CAAC,EAAE,iBAAiB,GAAE,EAAE,UAAS,CAAC,EAAE,kBAAkB,GAAE,EAAE,UAAS,GAAE,EAAE,SAAS,CAAC,GAAE,KAAI,UAAS,aAAY,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,IAAIE,OAAI,EAAE,oBAAkB,EAAE,iBAAiB,GAAGA,EAAC,IAAG,cAAa,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,IAAIA,OAAI,EAAE,oBAAkB,EAAE,iBAAiB,GAAGA,EAAC,GAAE,GAAE,CAAC,WAAE,EAAE,QAAO,SAAS,CAAC,GAAE,EAAE;AAAC,GAAE,GAAG,SAAO;AAA6C,IAAI,KAAG,gBAAE,EAAC,YAAW,EAAC,aAAY,EAAC,GAAE,OAAM,GAAG,EAAC,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,OAAM,EAAC,MAAK,GAAG,MAAM,EAAC,GAAE,YAAW,SAAQ,YAAW,SAAQ,aAAY,QAAO,aAAY,SAAQ,eAAc,UAAS,YAAW,QAAO,gBAAe,EAAC,MAAK,CAAC,UAAS,MAAM,EAAC,EAAC,CAAC,GAAE,OAAM,CAAC,GAAE,OAAK,EAAC,aAAY,CAAC,CAAC,EAAE,MAAM,SAAQ,YAAW,CAAC,CAAC,EAAE,MAAM,OAAM,GAAE,CAAC;AAAE,GAAG,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,QAAM,IAAE,iBAAE,eAAe;AAAE,SAAO,UAAE,GAAE,YAAE,UAAE,EAAC,IAAG,OAAM,GAAE,CAAC,eAAE,gBAAE,OAAM,EAAC,IAAG,kBAAiB,OAAM,eAAE,CAAC,2BAA0B,EAAC,YAAW,EAAE,YAAW,aAAY,EAAE,YAAW,CAAC,CAAC,EAAC,GAAE,CAAC,YAAE,GAAE,EAAC,MAAK,EAAE,MAAK,OAAM,EAAE,OAAM,SAAQ,OAAG,YAAW,EAAE,YAAW,YAAW,EAAE,YAAW,aAAY,EAAE,aAAY,eAAc,EAAE,eAAc,gBAAe,EAAE,eAAc,GAAE,YAAE,EAAC,GAAE,EAAC,GAAE,CAAC,EAAE,cAAY,EAAC,MAAK,WAAU,IAAG,QAAG,CAAC,EAAC,MAAK,EAAC,MAAI,CAAC,WAAE,EAAE,QAAO,WAAU,EAAC,MAAK,EAAC,CAAC,CAAC,CAAE,EAAC,IAAE,QAAO,EAAE,aAAW,EAAC,MAAK,UAAS,IAAG,QAAG,CAAC,EAAC,MAAK,EAAC,MAAI,CAAC,WAAE,EAAE,QAAO,UAAS,EAAC,MAAK,EAAC,CAAC,CAAC,CAAE,EAAC,IAAE,MAAM,CAAC,GAAE,MAAK,CAAC,QAAO,SAAQ,cAAa,cAAa,eAAc,iBAAgB,gBAAgB,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,OAAE,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC;AAAC,GAAE,GAAG,SAAO;AAAyC,IAAM,KAAG,CAAC,GAAE,EAAC,MAAK,EAAC,GAAE,MAAI;AAAC,QAAM,IAAE,IAAE,EAAE;AAAE,WAAS,EAAEF,IAAEE,IAAEC,IAAE;AAAC,QAAGH,GAAEE,GAAE,EAAE,MAAIC,GAAE,QAAOH;AAAE,QAAG,MAAM,QAAQA,GAAEE,GAAE,QAAQ,CAAC,GAAE;AAAC,YAAMH,KAAEC,GAAEE,GAAE,QAAQ;AAAE,eAAQF,KAAE,GAAE,IAAED,GAAE,QAAOC,KAAE,GAAEA,MAAI;AAAC,cAAMI,KAAE,EAAEL,GAAEC,EAAC,GAAEE,IAAEC,EAAC;AAAE,YAAGC,GAAE,QAAOA;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,UAAK,EAAC,OAAMD,IAAE,MAAKJ,IAAE,MAAK,EAAC,IAAE;AAAE,QAAG,EAAE,WAAW,QAAO,KAAK,EAAE,WAAW,EAAE,MAAM;AAAE,QAAG,EAAE,OAAO,QAAO,KAAK,EAAE,OAAO,SAAQ,YAAW,QAAQ;AAAE,UAAK,EAAC,IAAG,GAAE,UAAS,EAAC,IAAEI,IAAEI,KAAE,EAAER,IAAEI,IAAE,EAAE,GAAG;AAAE,QAAGI,IAAE;AAAC,YAAMP,KAAEO,GAAE,CAAC;AAAE,eAAQJ,KAAE,GAAEJ,KAAEC,GAAE,QAAOG,KAAEJ,IAAEI,KAAI,KAAGH,GAAEG,EAAC,EAAE,CAAC,MAAI,EAAE,IAAG;AAAC,QAAAH,GAAE,OAAOG,IAAE,CAAC,GAAE,EAAE,gBAAe,EAAE,QAAO,CAAC;AAAE;AAAA,MAAK;AAAA,IAAC;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,UAAK,EAAC,UAASA,IAAE,MAAKJ,GAAC,IAAE;AAAE,IAAAI,KAAEA,GAAEJ,GAAE,MAAM,KAAGA,GAAE,YAAU,MAAG,EAAE,eAAcA,GAAE,MAAM;AAAA,EAAE;AAAC,WAAS,EAAEA,IAAE;AAAC,QAAG,EAAE,YAAW;AAAC,UAAG,EAAE,OAAO,SAAO,EAAE,OAAO,MAAM,SAASA,GAAE,MAAM,EAAE,QAAM;AAAG,QAAE,qBAAoB,KAAE;AAAA,IAAC;AAAA,EAAC;AAAC,QAAM,IAAE,SAAG,OAAK,EAAC,MAAK,GAAG,EAAE,CAAC,MAAK,KAAI,GAAG,EAAE,CAAC,KAAI,EAAG,GAAE,IAAE,SAAG,MAAI,CAAC,EAAE,YAAU,CAAC,EAAE,KAAK,QAAS;AAAE,SAAO,UAAG,MAAI;AAAC,aAAS,iBAAiB,aAAY,CAAC;AAAA,EAAC,CAAE,GAAE,YAAG,MAAI;AAAC,aAAS,oBAAoB,aAAY,CAAC;AAAA,EAAC,CAAE,GAAE,EAAC,UAAS,GAAE,UAAS,GAAE,YAAW,SAASE,IAAE;AAAC,UAAM,IAAEA,GAAE;AAAO,QAAG,wBAAsB,EAAE,WAAU;AAAC,YAAMA,KAAE,EAAE,aAAa,QAAQ;AAAE,cAAOA,IAAE;AAAA,QAAC,KAAI;AAAO,WAAC,WAAU;AAAC,gBAAG,EAAE,SAAS,QAAO,KAAK,EAAE,SAAS,EAAE,KAAK,MAAM;AAAE,cAAE,QAAM,EAAE,KAAK,OAAM,SAAG,MAAI;AAAC,gBAAE,YAAU,EAAE,SAAS,UAAQ,EAAE,YAAU,EAAE,SAAS,MAAM,OAAO,GAAE,UAAU,UAAU,UAAU,EAAE,KAAK,EAAE,KAAM,WAAU;AAAC,kBAAE,OAAO,SAAQ,UAAS,SAAS;AAAA,cAAC,GAAI,WAAU;AAAC,kBAAE,OAAO,SAAQ,UAAS,QAAQ;AAAA,cAAC,CAAE,GAAE,EAAE,cAAa,EAAE,KAAK;AAAA,YAAE,CAAE;AAAA,UAAC,EAAE;AAAE;AAAA,QAAM,KAAI;AAAM,WAAC,WAAU;AAAC,gBAAG,EAAE,QAAQ,QAAO,KAAK,EAAE,QAAQ,EAAE,KAAK,MAAM;AAAE,kBAAK,EAAC,IAAGE,IAAE,KAAIJ,IAAE,OAAME,IAAE,QAAOG,IAAE,UAAS,EAAC,IAAE,EAAE,OAAM,EAAC,MAAK,EAAC,IAAE,GAAEG,KAAE,EAAC,CAACJ,EAAC,GAAE,QAAQ,oBAAI,QAAM,QAAQ,CAAC,GAAE,CAACJ,EAAC,GAAE,EAAE,IAAG,CAACE,EAAC,GAAE,IAAG,CAACG,EAAC,GAAE,OAAG,CAAC,CAAC,GAAE,CAAC,GAAE,SAAQ,MAAG,SAAQ,KAAE;AAAE,kBAAM,QAAQ,EAAE,QAAQ,IAAE,EAAE,OAAO,CAAC,EAAE,KAAKG,EAAC,IAAE,EAAE,OAAO,CAAC,IAAE,CAACA,EAAC;AAAE,cAAE,eAAcA,EAAC;AAAA,UAAC,EAAE;AAAE;AAAA,QAAM,KAAI;AAAO,YAAE;AAAE;AAAA,QAAM,KAAI;AAAS,YAAE;AAAA,MAAC;AAAC,QAAE,eAAc,EAAC,SAAQN,IAAE,MAAK,EAAE,MAAK,MAAK,EAAE,KAAK,OAAM,CAAC,GAAE,EAAE,qBAAoB,KAAE;AAAA,IAAC;AAAA,EAAC,GAAE,YAAW,GAAE,aAAY,GAAE,YAAW,WAAU;AAAC,MAAE,QAAQ;AAAA,EAAC,GAAE,YAAW,WAAU;AAAC,MAAE,QAAQ;AAAA,EAAC,EAAC;AAAC;AAAE,IAAI,KAAG,gBAAE,EAAC,MAAK,eAAc,OAAM,GAAG,EAAC,YAAW,EAAC,MAAK,SAAQ,UAAS,KAAE,GAAE,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,MAAK,EAAC,MAAK,GAAG,MAAM,GAAE,UAAS,KAAE,GAAE,OAAM,EAAC,MAAK,GAAG,MAAM,EAAC,GAAE,GAAE,QAAO,GAAE,QAAO,OAAM,EAAC,MAAK,GAAG,KAAK,EAAC,GAAE,SAAQ,UAAS,YAAW,UAAS,UAAS,UAAS,UAAS,UAAS,UAAS,QAAO,CAAC,GAAE,OAAM,EAAC,cAAa,CAAC,GAAE,MAAI,EAAE,CAAC,KAAG,EAAE,CAAC,GAAE,YAAW,OAAG,EAAE,CAAC,GAAE,aAAY,OAAG,EAAE,CAAC,GAAE,aAAY,OAAG,EAAE,CAAC,GAAE,QAAO,MAAI,MAAG,QAAO,MAAI,MAAG,qBAAoB,OAAG,aAAW,OAAO,EAAC,GAAE,MAAM,GAAE,GAAE;AAAC,QAAM,IAAE,IAAE,GAAE,IAAE,IAAE;AAAE,SAAM,EAAC,QAAO,GAAE,UAAS,GAAE,UAAS,IAAE,EAAE,GAAE,SAAQ,SAAE,CAAC,CAAC,GAAE,GAAG,GAAG,GAAE,GAAE,EAAC,UAAS,GAAE,QAAO,EAAC,CAAC,EAAC;AAAC,EAAC,CAAC;AAAE,IAAM,KAAG,CAAC,QAAQ;AAAE,GAAG,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAO,UAAE,GAAE,YAAE,UAAE,EAAC,IAAG,OAAM,GAAE,CAAC,YAAE,YAAE,EAAC,MAAK,eAAc,cAAa,EAAE,YAAW,cAAa,EAAE,YAAW,WAAU,GAAE,GAAE,EAAC,SAAQ,QAAG,MAAI,CAAC,eAAE,gBAAE,OAAM,EAAC,OAAM,eAAE,EAAE,QAAQ,GAAE,SAAQ,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,IAAI,MAAI,EAAE,cAAY,EAAE,WAAW,GAAG,CAAC,IAAG,KAAI,UAAS,OAAM,sBAAqB,GAAE,CAAC,gBAAE,MAAK,MAAK,EAAE,UAAE,IAAE,GAAE,mBAAE,UAAE,MAAK,WAAE,EAAE,OAAO,QAAI,UAAE,GAAE,mBAAE,UAAE,MAAK,CAAC,EAAE,YAAU,CAAC,CAAC,OAAM,QAAO,QAAQ,EAAE,SAAS,EAAE,OAAO,KAAG,UAAE,GAAE,mBAAE,MAAK,EAAC,OAAM,qBAAoB,QAAO,EAAE,SAAQ,KAAI,EAAE,QAAO,GAAE,gBAAE,EAAE,IAAI,GAAE,GAAE,EAAE,KAAG,mBAAE,QAAO,IAAE,CAAC,GAAE,EAAE,EAAG,GAAE,GAAG,EAAE,CAAC,GAAE,eAAE,gBAAE,YAAW,EAAC,OAAM,iBAAgB,KAAI,YAAW,uBAAsB,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,OAAG,EAAE,WAAS,GAAE,GAAE,MAAK,GAAG,GAAE,CAAC,CAAC,YAAE,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,OAAE,EAAE,UAAU,CAAC,CAAC,CAAC,CAAE,GAAE,GAAE,EAAC,GAAE,GAAE,CAAC,gBAAe,cAAc,CAAC,CAAC,CAAC;AAAC,GAAE,GAAG,SAAO;AAAiD,IAAM,KAAG,CAAC,EAAC,MAAK,QAAO,SAAQ,OAAM,GAAE,EAAC,MAAK,QAAO,SAAQ,MAAK,GAAE,EAAC,MAAK,QAAO,SAAQ,OAAM,GAAE,EAAC,MAAK,QAAO,SAAQ,SAAQ,CAAC;AAA9H,IAAgI,KAAG,GAAG,EAAC,MAAK,EAAC,MAAK,QAAO,UAAS,KAAE,GAAE,QAAO,SAAQ,OAAM,EAAC,MAAK,GAAG,MAAM,GAAE,SAAQ,OAAK,EAAC,IAAG,MAAK,KAAI,OAAM,OAAM,SAAQ,QAAO,UAAS,UAAS,WAAU,GAAE,GAAE,SAAQ,EAAC,MAAK,CAAC,QAAO,OAAO,GAAE,SAAQ,OAAK,EAAC,QAAO,MAAG,OAAM,MAAG,MAAK,MAAG,SAAQ,MAAG,YAAW,KAAE,GAAE,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,WAAU,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,iBAAgB,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,eAAc,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,eAAc,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,aAAY,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,YAAW,EAAC,MAAK,QAAO,SAAQ,IAAG,GAAE,MAAK,SAAQ,MAAK,EAAC,MAAK,GAAG,QAAQ,EAAC,GAAE,oBAAmB,QAAO,mBAAkB,EAAC,MAAK,OAAM,SAAQ,MAAI,CAAC,EAAC,GAAE,eAAc,UAAS,YAAW,SAAQ,aAAY,EAAC,MAAK,CAAC,OAAM,QAAO,MAAM,EAAC,GAAE,aAAY,SAAQ,eAAc,UAAS,YAAW,QAAO,gBAAe,EAAC,MAAK,CAAC,UAAS,MAAM,EAAC,GAAE,mBAAkB,EAAC,MAAK,CAAC,UAAS,MAAM,EAAC,GAAE,aAAY,EAAC,MAAK,GAAG,CAAC,OAAM,QAAQ,CAAC,GAAE,SAAQ,MAAI,GAAE,GAAE,SAAQ,UAAS,YAAW,UAAS,UAAS,UAAS,UAAS,UAAS,kBAAiB,SAAQ,CAAC;AAA/pC,IAAiqC,KAAG,CAAC,GAAE,EAAC,MAAK,EAAC,GAAE,MAAI;AAAC,QAAM,IAAE,IAAE,CAAC,GAAE,IAAE,IAAE,CAAC,GAAE,IAAE,IAAE,KAAE;AAAE,WAAS,EAAEC,IAAE,GAAE;AAAC,UAAM,IAAE,EAAE,QAAQ,OAAMI,KAAE,EAAE,QAAQ;AAAM,QAAIC,KAAE,EAAE,cAAY;AAAE,UAAMC,KAAE,EAAE,eAAa;AAAE,QAAIC,KAAE,EAAE,eAAaH,GAAE,cAAaU,KAAE,EAAE,cAAYV,GAAE;AAAY,QAAGG,KAAE,MAAIA,KAAE,IAAGO,KAAE,MAAIA,KAAE,IAAG,EAAE,QAAO;AAAC,YAAMhB,MAAG,EAAE,cAAYM,GAAE,eAAa;AAAE,MAAAU,MAAGhB,IAAEO,MAAGP;AAAA,IAAC;AAAC,MAAE,QAAME,KAAEK,KAAEA,KAAEL,KAAEc,KAAEA,KAAEd,IAAE,EAAE,QAAM,IAAEO,KAAEA,KAAE,IAAED,KAAEA,KAAE;AAAA,EAAC;AAAC,WAAS,EAAER,IAAEG,IAAE;AAAC,MAAEH,IAAEG,EAAC,GAAE,WAAY,MAAI;AAAC,QAAE,IAAI,KAAE;AAAA,IAAC,GAAG,GAAG,GAAE,EAAE,gBAAe,EAAC,GAAEH,IAAE,GAAEG,GAAC,CAAC;AAAA,EAAC;AAAC,QAAM,IAAE,IAAE,KAAE,GAAE,IAAE,SAAE,EAAC,OAAM,CAAC,EAAC,CAAC;AAAE,QAAM,IAAE,IAAE,KAAE,GAAE,IAAE,IAAE,CAAC,GAAE,IAAE,IAAE,CAAC,GAAE,IAAE,IAAE,CAAC,CAAC,GAAEO,KAAE,IAAE,CAAC,CAAC;AAAE,QAAM,IAAE,IAAE,CAAC;AAAE,WAAS,EAAER,IAAE;AAAC,QAAG,CAAC,EAAE,SAAS;AAAO,UAAMC,KAAE,QAAQ,OAAO,EAAE,KAAK,IAAED,IAAG,QAAQ,CAAC,CAAC;AAAE,MAAE,QAAMA,KAAE,IAAE,KAAK,IAAI,GAAEC,EAAC,IAAE,KAAK,IAAI,KAAGA,EAAC;AAAA,EAAC;AAAC,WAAS,EAAED,IAAEC,IAAE,GAAE;AAAC,QAAG,EAAE,QAAM,MAAG,QAAQ,IAAI,EAAE,QAAO,EAAE,YAAWD,GAAE,YAAWC,EAAC,GAAE,CAAC,EAAE,UAAQ,EAAE,YAAW;AAAC,YAAMH,KAAEE,GAAE,aAAWC;AAAE,QAAE,SAAOH;AAAA,IAAC;AAAC,UAAM,IAAEE,GAAE,YAAU;AAAE,MAAE,SAAO,GAAE,EAAE,EAAE,OAAM,EAAE,KAAK;AAAA,EAAC;AAAC,MAAI;AAAE,QAAM,IAAE,SAAE,OAAO,OAAO,EAAC,IAAG,MAAK,KAAI,OAAM,OAAM,SAAQ,QAAO,UAAS,UAAS,YAAW,QAAO,SAAQ,GAAE,EAAE,KAAK,CAAC;AAAE,QAAM,IAAE,IAAE,KAAE;AAAE,WAAS,EAAEF,IAAE;AAAC,IAAAA,GAAE,QAAS,CAAAA,OAAG;AAAC,MAAAA,GAAE,WAASA,GAAE,SAAO,OAAGiB,GAAE,OAAOjB,GAAE,EAAE,IAAGA,GAAE,YAAU,EAAEA,GAAE,QAAQ;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,QAAM,IAAE,IAAE,KAAE;AAAE,WAAS,EAAEA,IAAEE,IAAE;AAAC,UAAM,QAAQF,EAAC,IAAEA,GAAE,QAAS,CAAAA,OAAG;AAAC,MAAAE,MAAGe,GAAE,IAAIjB,GAAE,EAAE,GAAEA,GAAE,SAAOE,IAAEA,MAAGe,GAAE,IAAIjB,GAAE,EAAE,GAAEA,GAAE,YAAU,EAAEA,GAAE,UAASE,EAAC;AAAA,IAAC,CAAE,KAAGA,MAAGe,GAAE,IAAIjB,GAAE,EAAE,GAAEA,GAAE,SAAOE,IAAEA,MAAGe,GAAE,IAAIjB,GAAE,EAAE,GAAEA,GAAE,YAAU,EAAEA,GAAE,UAASE,EAAC;AAAA,EAAE;AAAC,WAAS,EAAEF,IAAE;AAAC,IAAAkB,GAAE,QAAMC,GAAEnB,EAAC;AAAA,EAAC;AAAC,QAAM,IAAE,SAAG,OAAK,EAAC,OAAM,MAAI,EAAE,QAAM,KAAI,QAAO,MAAI,EAAE,QAAM,KAAI,WAAU,SAAS,EAAE,KAAK,IAAG,EAAG,GAAE,IAAE,SAAG,MAAI,GAAG,KAAK,MAAM,MAAI,EAAE,KAAK,CAAC,GAAI,GAAEW,KAAE,SAAG,MAAI,EAAE,mBAAiB,CAAC,EAAE,gBAAc,KAAG,wCAAwCS,EAAC,yBAA0B,GAAER,KAAE,SAAG,MAAI,EAAE,QAAM,WAAS,QAAS,GAAEG,KAAE,SAAG,MAAI,EAAE,QAAM,WAAS,QAAS,GAAEM,KAAE,IAAE,CAAC,CAAC,GAAEC,KAAE,SAAG,MAAI;AAAC,UAAK,EAAC,eAAcnB,IAAE,aAAYJ,IAAE,MAAKE,GAAC,IAAE;AAAE,WAAM,EAAC,MAAK,EAAE,eAAc,UAAS,EAAC,MAAK,GAAE,YAAW,GAAE,WAAU,GAAE,WAAU,GAAE,eAAcE,IAAE,aAAYJ,IAAE,aAAY,GAAE,WAAUsB,IAAE,MAAKpB,GAAC,GAAE,eAAc,EAAE,eAAc,WAAUkB,IAAE,MAAK,EAAC;AAAA,EAAC,CAAE;AAAE,QAAG,MAAI,EAAE,YAAa,MAAI;AAAC,aAAG,MAAI;AAAC,QAAE,EAAE,OAAM,EAAE,KAAK;AAAA,IAAC,CAAE;AAAA,EAAC,CAAE;AAAE,MAAIF,KAAE,IAAI,IAAI,EAAE,iBAAiB;AAAE,WAASE,GAAEjB,IAAE;AAAC,UAAK,EAAC,oBAAmBC,KAAE,EAAC,IAAE,GAAEJ,KAAE,CAACC,IAAEE,OAAI;AAAC,YAAK,EAAC,IAAGD,IAAE,OAAM,GAAE,KAAI,GAAE,QAAOK,IAAE,UAASC,IAAE,QAAOC,GAAC,IAAE,GAAEC,KAAE,CAAC;AAAE,aAAO,KAAKT,EAAC,EAAE,IAAK,CAAAE,OAAG;AAAC,SAAC,UAAS,YAAW,aAAY,OAAO,EAAE,SAASA,EAAC,MAAIO,GAAEP,EAAC,IAAEF,GAAEE,EAAC;AAAA,MAAE,CAAE;AAAE,YAAMc,KAAEhB,GAAEO,EAAC,GAAEgB,KAAErB,KAAE,GAAEW,KAAEb,GAAEM,EAAC,GAAEQ,KAAEd,GAAEC,EAAC;AAAE,cAAOY,MAAG,WAASA,MAAGX,KAAEC,OAAIc,GAAE,IAAIH,EAAC,GAAE,EAAC,GAAGL,IAAE,IAAGK,IAAE,OAAMd,GAAE,CAAC,GAAE,KAAIA,GAAE,CAAC,GAAE,QAAOiB,GAAE,IAAIH,EAAC,GAAE,UAASE,KAAEA,GAAE,IAAK,CAAAhB,OAAGD,GAAEC,IAAEuB,EAAC,CAAE,IAAE,QAAO,QAAOvB,GAAEQ,EAAC,GAAE,SAAQN,IAAE,QAAOF,IAAE,WAAUA,GAAE,WAAS,MAAE;AAAA,IAAC;AAAE,WAAOD,GAAEG,IAAE,CAAC;AAAA,EAAC;AAAC,QAAMgB,KAAE,IAAEC,GAAE,EAAE,IAAI,CAAC;AAAE,QAAG,MAAI,EAAE,MAAO,CAACjB,IAAEC,OAAI;AAAC,IAAAD,OAAIC,OAAIc,KAAE,IAAI,IAAI,EAAE,iBAAiB,IAAG,EAAE,EAAE,IAAI;AAAA,EAAC,GAAG,EAAC,MAAK,KAAE,CAAC;AAAE,QAAMO,KAAE,SAAE,EAAC,SAAQ,MAAG,MAAK,EAAC,QAAO,MAAG,OAAM,MAAG,MAAK,MAAG,SAAQ,MAAG,YAAW,KAAE,EAAC,CAAC;AAAE,gBAAG,MAAI;AAAC,gBAAU,OAAO,EAAE,UAAQ,OAAO,OAAOA,GAAE,MAAK,EAAE,OAAO,IAAE,EAAE,YAAUA,GAAE,UAAQ;AAAA,EAAG,CAAE;AAAE,QAAMJ,KAAE,SAASpB,IAAE;AAAC,IAAAA,KAAEA,MAAG;AAAG,UAAME,KAAE,oDAAmDC,KAAED,GAAE;AAAO,QAAIH,KAAE;AAAG,aAAQE,KAAE,GAAEA,KAAED,IAAEC,KAAI,CAAAF,MAAGG,GAAE,OAAO,KAAK,MAAM,KAAK,OAAO,IAAEC,EAAC,CAAC;AAAE,WAAOJ;AAAA,EAAC,EAAE,CAAC;AAAE,SAAM,EAAC,MAAK,GAAE,MAAK,GAAE,KAAI,GAAE,OAAM,GAAE,OAAM,GAAE,QAAOqB,IAAE,YAAW,GAAE,WAAU,GAAE,OAAMI,IAAE,aAAY,GAAE,YAAWb,IAAE,aAAYC,IAAE,YAAWG,IAAE,UAASO,IAAE,UAAS,GAAE,YAAW,GAAE,UAASJ,IAAE,cAAa,GAAE,aAAY,GAAE,WAAUR,IAAE,UAAS,GAAE,WAAUW,IAAE,QAAO,SAASnB,IAAE;AAAC,UAAMC,KAAE,EAAE;AAAiB,QAAG,CAACA,GAAE,OAAM,IAAI,MAAM,iDAAiD;AAAE,UAAMJ,KAAE,SAASC,IAAE;AAAC,YAAMC,KAAED,GAAE,YAAU,CAAC;AAAE,UAAGC,GAAE,QAAS,CAAAD,OAAG;AAAC,QAAAA,GAAE,WAAS,CAACG,GAAE,KAAKH,IAAEE,IAAEF,EAAC,GAAED,GAAEC,EAAC;AAAA,MAAC,CAAE,GAAEA,GAAE,YAAUC,GAAE,QAAO;AAAC,YAAIC,KAAE;AAAG,QAAAA,KAAED,GAAE,KAAM,CAAAD,OAAG,CAACA,GAAE,QAAS,GAAEA,GAAE,WAAS,CAACE;AAAA,MAAC;AAAC,MAAAA,MAAG,CAACF,GAAE,YAAUA,GAAE,aAAWA,GAAE,SAAO;AAAA,IAAG;AAAE,IAAAD,GAAEmB,GAAE,KAAK;AAAA,EAAC,GAAE,SAAQ,GAAE,WAAU,SAASf,IAAE;AAAC,MAAE,aAAWA,GAAE,eAAe,GAAEA,GAAE,SAAO,IAAE,EAAE,GAAE,IAAE,EAAE,IAAG,GAAE,EAAE,WAAU,EAAE,KAAK;AAAA,EAAE,GAAE,QAAO,SAASH,IAAEG,IAAE;AAAC,MAAE,IAAI,IAAE,GAAE,EAAE,QAAM,OAAG,EAAE,QAAMH,IAAE,EAAE,QAAMG,IAAE,EAAE,WAAU,EAAC,GAAEH,IAAE,GAAEG,GAAC,CAAC;AAAA,EAAC,GAAE,YAAW,GAAE,cAAa,WAAU;AAAC,MAAE,QAAM,CAAC,EAAE,OAAM,EAAE,UAAQc,GAAE,MAAM,GAAE,SAAG,MAAI;AAAC,QAAE,EAAE,OAAM,EAAE,KAAK;AAAA,IAAC,CAAE,IAAG,EAAEC,GAAE,OAAM,EAAE,KAAK,GAAE,EAAE,iBAAgB,EAAE,KAAK;AAAA,EAAC,GAAE,kBAAiB,SAASlB,IAAE;AAAC,MAAE,QAAM,CAAC,EAAE,OAAM,UAAQA,OAAI,EAAE,QAAM,WAAU;AAAC,YAAMA,KAAE,EAAE,OAAO;AAAM,MAAAA,GAAE,qBAAmBA,GAAE,kBAAkB;AAAA,IAAC,EAAE,IAAE,SAAS,kBAAgB,SAAS,eAAe;AAAA,EAAE,GAAE,cAAa,GAAE,iBAAgB,WAAU;AAAC,MAAE,QAAM,GAAE,EAAE,QAAM,GAAE,EAAE,QAAM,GAAE,EAAE,YAAY;AAAA,EAAC,GAAE,cAAa,SAASG,IAAEJ,IAAE;AAAC,IAAAI,GAAE,gBAAgB;AAAE,UAAMF,KAAE,SAAS,cAAc,YAAYmB,EAAC,EAAE;AAAE,QAAGnB,IAAE;AAAC,YAAM,IAAEA,GAAE,YAAW,IAAEA,GAAE;AAAU,MAAAF,GAAE,SAAO,CAACA,GAAE;AAAO,UAAIO,KAAE;AAAG,MAAAP,GAAE,UAAQkB,GAAE,IAAIlB,GAAE,EAAE,GAAE,EAAE,QAAM,EAAE,SAAOO,KAAE,OAAG,SAASN,IAAEE,IAAEC,IAAE;AAAC,QAAAD,GAAEF,IAAG,CAACE,IAAEH,OAAI;AAAC,gBAAK,EAAC,UAASE,GAAC,IAAE;AAAE,UAAAD,GAAE,SAAO,CAACE,GAAE,QAAOA,GAAE,WAASF,GAAE,OAAOC,EAAC,IAAEC,IAAEH,MAAGI,GAAE;AAAA,QAAE,CAAE;AAAA,MAAC,EAAEJ,IAAE,EAAE,MAAM,MAAI;AAAC,iBAAG,MAAI;AAAC,YAAEE,IAAE,GAAE,CAAC;AAAA,QAAC,CAAE;AAAA,MAAC,CAAE,MAAI,CAACF,GAAE,UAAQA,GAAE,aAAWkB,GAAE,OAAOlB,GAAE,EAAE,GAAE,EAAEA,GAAE,QAAQ,IAAG,SAAG,MAAI;AAAC,QAAAO,MAAG,EAAEL,IAAE,GAAE,CAAC;AAAA,MAAC,CAAE,GAAE,EAAE,aAAYE,IAAEJ,GAAE,QAAOA,EAAC;AAAA,IAAC;AAAA,EAAC,GAAE,eAAc,WAAU;AAAC,WAAM,CAAC,GAAGkB,EAAC;AAAA,EAAC,GAAE,eAAc,SAASf,IAAE;AAAC,IAAAe,KAAE,IAAI,IAAIf,EAAC,GAAE,EAAE,EAAE,IAAI;AAAA,EAAC,GAAE,gBAAe,SAASF,IAAEG,IAAE;AAAC,WAAO,EAAE,UAAQ,EAAE,QAAMA,KAAG,EAAE,sBAAqBH,IAAEG,GAAE,QAAOA,EAAC,GAAE;AAAA,EAAE,GAAE,gBAAe,SAASH,IAAEG,IAAE;AAAC,WAAO,EAAE,UAAQ,EAAE,QAAM,OAAM,EAAE,sBAAqBH,IAAEG,GAAE,QAAOA,EAAC,GAAE;AAAA,EAAE,GAAE,iBAAgB,SAASD,IAAEC,IAAE;AAAC,IAAAD,GAAE,gBAAgB,GAAEA,GAAE,eAAe;AAAE,UAAK,EAAC,aAAYH,GAAC,IAAE;AAAE,UAAM,QAAQA,EAAC,IAAEW,GAAE,QAAMX,KAAE,cAAY,OAAOA,OAAIW,GAAE,QAAMX,GAAEG,IAAEC,EAAC,KAAG,CAAC,IAAG,EAAE,QAAM,MAAG,EAAE,QAAMD,GAAE,SAAQ,EAAE,QAAMA,GAAE,SAAQ,EAAE,QAAMC;AAAA,EAAC,GAAE,aAAY,SAASH,IAAEG,IAAEJ,IAAE;AAAC,MAAE,iBAAgBC,IAAEG,IAAEJ,EAAC;AAAA,EAAC,GAAE,YAAW,SAASC,IAAEG,IAAEJ,IAAE;AAAC,UAAK,EAAC,IAAGE,IAAE,OAAM,EAAC,IAAE,GAAE,IAAE,EAAE,MAAM,YAAU,CAAC;AAAE,aAAQD,KAAE,EAAE,QAAOA,KAAE,GAAEA,MAAI;AAAC,YAAME,KAAE,EAAEF,KAAE,CAAC;AAAE,UAAG,OAAKE,GAAED,EAAC,KAAG,OAAKC,GAAE,CAAC,GAAE;AAAC,UAAE,OAAOF,KAAE,GAAE,CAAC;AAAE;AAAA,MAAK;AAAA,IAAC;AAAC,MAAE,gBAAeA,IAAEG,IAAEJ,EAAC;AAAA,EAAC,GAAE,aAAY,SAASI,IAAEJ,IAAE;AAAC,MAAE,IAAI,MAAI,aAAa,CAAC,GAAE,IAAE,WAAY,MAAI;AAAC,QAAE,iBAAgBI,IAAEJ,GAAE,QAAOA,EAAC;AAAA,IAAC,GAAG,EAAE,UAAU;AAAA,EAAE,GAAE,gBAAe,SAASC,IAAEG,IAAE;AAAC,iBAAa,CAAC,GAAE,EAAE,oBAAmBH,IAAEG,GAAE,QAAOA,EAAC;AAAA,EAAC,EAAC;AAAC;AAAE,IAAI,KAAG,gBAAE,EAAC,MAAK,eAAc,YAAW,EAAC,OAAM,GAAE,UAAS,IAAG,WAAU,IAAG,aAAY,IAAG,aAAY,EAAC,GAAE,YAAW,EAAC,UAAS,EAAC,GAAE,OAAM,IAAG,OAAM,EAAC,WAAU,CAAC,EAAC,GAAE,GAAE,GAAE,EAAC,MAAI,EAAE,CAAC,KAAG,EAAE,CAAC,GAAE,gBAAe,CAAC,EAAC,GAAE,GAAE,GAAE,EAAC,MAAI,EAAE,CAAC,KAAG,EAAE,CAAC,GAAE,cAAa,MAAI,MAAG,WAAU,OAAG,EAAE,CAAC,GAAE,aAAY,CAAC,GAAE,GAAE,MAAI,aAAa,cAAY,EAAE,CAAC,KAAG,EAAE,CAAC,GAAE,iBAAgB,OAAG,aAAW,OAAO,GAAE,gBAAe,CAAC,GAAE,GAAE,MAAI,aAAa,cAAY,EAAE,CAAC,KAAG,EAAE,CAAC,GAAE,iBAAgB,CAAC,GAAE,GAAE,MAAI,aAAa,cAAY,EAAE,CAAC,KAAG,EAAE,CAAC,GAAE,oBAAmB,CAAC,GAAE,GAAE,MAAI,aAAa,cAAY,EAAE,CAAC,KAAG,EAAE,CAAC,GAAE,sBAAqB,CAAC,GAAE,GAAE,MAAI,aAAa,cAAY,EAAE,CAAC,KAAG,EAAE,CAAC,GAAE,sBAAqB,CAAC,GAAE,GAAE,MAAI,aAAa,cAAY,EAAE,CAAC,KAAG,EAAE,CAAC,GAAE,kBAAiB,OAAG,EAAE,CAAC,GAAE,gBAAe,OAAG,EAAE,CAAC,GAAE,kBAAiB,OAAG,EAAE,CAAC,GAAE,sBAAqB,OAAG,EAAE,CAAC,GAAE,gBAAe,OAAG,EAAE,CAAC,GAAE,oBAAmB,CAAC,GAAE,MAAI,EAAE,CAAC,KAAG,EAAE,CAAC,GAAE,iBAAgB,CAAC,GAAE,GAAE,MAAI,aAAa,cAAY,EAAE,CAAC,KAAG,EAAE,CAAC,EAAC,GAAE,MAAM,GAAE,GAAE;AAAC,QAAM,IAAE,CAAC,CAAC,EAAE,MAAM,SAAQ,IAAE,CAAC,CAAC,EAAE,MAAM,QAAO,IAAE,IAAE,GAAE,IAAE,IAAE,GAAE,IAAE,IAAE;AAAE,SAAM,EAAC,QAAO,GAAE,SAAQ,GAAE,SAAQ,GAAE,aAAY,GAAE,YAAW,GAAE,GAAG,GAAG,GAAE,GAAE,EAAC,QAAO,GAAE,SAAQ,GAAE,SAAQ,EAAC,CAAC,EAAC;AAAC,EAAC,CAAC;AAAE,IAAM,KAAG,EAAC,KAAI,UAAS,OAAM,cAAa;AAA1C,IAA4C,KAAG,EAAC,OAAM,sBAAqB;AAA3E,IAA6E,KAAG,gBAAE,QAAO,EAAC,OAAM,4BAA2B,GAAE,MAAK,EAAE;AAApI,IAAsI,KAAG,EAAC,OAAM,sBAAqB;AAArK,IAAuK,KAAG,gBAAE,QAAO,EAAC,OAAM,4BAA2B,GAAE,MAAK,EAAE;AAAE,GAAG,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,QAAM,IAAE,iBAAE,eAAe,GAAE,IAAE,iBAAE,WAAW,GAAE,IAAE,iBAAE,OAAO,GAAE,IAAE,iBAAE,WAAW,GAAE,IAAE,iBAAE,aAAa;AAAE,SAAO,UAAE,GAAE,mBAAE,OAAM,IAAG,CAAC,gBAAE,OAAM,EAAC,KAAI,WAAU,OAAM,eAAE,CAAC,kBAAiB,EAAC,aAAY,EAAE,UAAQ,CAAC,EAAE,WAAU,CAAC,CAAC,GAAE,OAAM,eAAE,EAAE,SAAS,GAAE,SAAQ,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,IAAID,OAAI,EAAE,aAAW,EAAE,UAAU,GAAGA,EAAC,GAAE,GAAE,CAAC,YAAE,GAAE,EAAC,GAAE,EAAE,MAAK,GAAE,EAAE,KAAI,OAAM,eAAE,EAAC,UAAS,EAAE,aAAY,CAAC,GAAE,YAAW,EAAE,QAAO,YAAW,EAAE,YAAW,WAAU,EAAE,WAAU,eAAc,EAAE,WAAU,GAAE,EAAC,SAAQ,QAAG,MAAI,CAAC,gBAAE,OAAM,EAAC,KAAI,WAAU,OAAM,eAAE,CAAC,YAAW,EAAC,YAAW,EAAE,YAAW,aAAY,EAAE,YAAW,CAAC,CAAC,EAAC,GAAE,CAAC,YAAE,GAAE,EAAC,MAAK,EAAE,UAAS,OAAM,EAAE,MAAK,MAAK,EAAE,MAAK,QAAO,EAAE,QAAO,YAAW,EAAE,YAAW,YAAW,EAAE,YAAW,aAAY,EAAE,aAAY,eAAc,EAAE,eAAc,aAAY,EAAE,aAAY,oBAAmB,EAAE,oBAAmB,mBAAkB,EAAE,mBAAkB,gBAAe,EAAE,gBAAe,WAAU,EAAE,UAAS,YAAW,EAAE,cAAa,aAAY,EAAE,aAAY,gBAAe,EAAE,gBAAe,kBAAiB,EAAE,gBAAe,kBAAiB,EAAE,gBAAe,mBAAkB,EAAE,iBAAgB,aAAY,EAAE,aAAY,YAAW,EAAE,WAAU,GAAE,YAAE,EAAC,GAAE,EAAC,GAAE,CAAC,EAAE,cAAY,EAAC,MAAK,WAAU,IAAG,QAAG,CAAC,EAAC,MAAKA,GAAC,MAAI,CAAC,WAAE,EAAE,QAAO,WAAU,EAAC,MAAKA,GAAC,GAAG,MAAI,CAAC,gBAAE,OAAM,IAAG,CAAC,gBAAE,QAAO,MAAK,gBAAEA,GAAE,KAAK,GAAE,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC,CAAE,EAAC,IAAE,QAAO,EAAE,aAAW,EAAC,MAAK,UAAS,IAAG,QAAG,CAAC,EAAC,MAAKA,GAAC,MAAI,CAAC,WAAE,EAAE,QAAO,UAAS,EAAC,MAAKA,GAAC,GAAG,MAAI,CAAC,EAAE,CAAE,CAAC,CAAE,EAAC,IAAE,MAAM,CAAC,GAAE,MAAK,CAAC,QAAO,SAAQ,QAAO,UAAS,cAAa,cAAa,eAAc,iBAAgB,eAAc,sBAAqB,qBAAoB,kBAAiB,aAAY,cAAa,eAAc,kBAAiB,oBAAmB,oBAAmB,qBAAoB,eAAc,YAAY,CAAC,CAAC,GAAE,CAAC,CAAC,CAAE,GAAE,GAAE,EAAC,GAAE,GAAE,CAAC,KAAI,KAAI,SAAQ,cAAa,cAAa,aAAY,aAAa,CAAC,CAAC,GAAE,EAAE,GAAE,EAAE,MAAM,WAAS,UAAE,GAAE,YAAE,GAAE,EAAC,KAAI,GAAE,OAAM,EAAE,MAAM,MAAK,OAAM,EAAE,aAAY,YAAW,EAAE,cAAa,WAAU,EAAE,cAAa,aAAY,EAAE,iBAAgB,gBAAe,EAAE,iBAAgB,GAAE,MAAK,GAAE,CAAC,SAAQ,SAAQ,cAAa,aAAY,eAAc,gBAAgB,CAAC,KAAG,mBAAE,QAAO,IAAE,GAAE,EAAE,iBAAe,UAAE,GAAE,YAAE,GAAE,EAAC,KAAI,GAAE,YAAW,EAAE,YAAW,uBAAsB,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,CAAAA,OAAG,EAAE,aAAWA,KAAG,OAAM,EAAE,MAAK,MAAK,EAAE,WAAU,YAAW,EAAE,YAAW,eAAc,EAAE,YAAW,aAAY,EAAE,aAAY,kBAAiB,EAAE,eAAc,oBAAmB,EAAE,eAAc,GAAE,YAAE,EAAC,GAAE,EAAC,GAAE,CAAC,EAAE,cAAY,EAAC,MAAK,WAAU,IAAG,QAAG,CAAC,EAAC,MAAKA,GAAC,MAAI,CAAC,WAAE,EAAE,QAAO,WAAU,EAAC,MAAKA,GAAC,GAAG,MAAI,CAAC,gBAAE,OAAM,IAAG,CAAC,gBAAE,QAAO,MAAK,gBAAEA,GAAE,EAAE,KAAK,KAAK,CAAC,GAAE,CAAC,CAAC,CAAC,CAAC,CAAE,CAAC,CAAE,EAAC,IAAE,QAAO,EAAE,aAAW,EAAC,MAAK,UAAS,IAAG,QAAG,CAAC,EAAC,MAAKA,GAAC,MAAI,CAAC,WAAE,EAAE,QAAO,UAAS,EAAC,MAAKA,GAAC,GAAG,MAAI,CAAC,EAAE,CAAE,CAAC,CAAE,EAAC,IAAE,MAAM,CAAC,GAAE,MAAK,CAAC,cAAa,SAAQ,QAAO,cAAa,eAAc,eAAc,kBAAiB,kBAAkB,CAAC,KAAG,mBAAE,QAAO,IAAE,GAAE,EAAE,UAAU,UAAQ,UAAE,GAAE,YAAE,GAAE,EAAC,KAAI,GAAE,YAAW,EAAE,aAAY,uBAAsB,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,CAAAA,OAAG,EAAE,cAAYA,KAAG,GAAE,EAAE,OAAM,GAAE,EAAE,OAAM,MAAK,EAAE,UAAS,MAAK,EAAE,MAAK,OAAM,EAAE,MAAK,OAAM,EAAE,WAAU,UAAS,EAAE,UAAS,YAAW,EAAE,SAAQ,eAAc,EAAE,YAAW,aAAY,EAAE,UAAS,aAAY,EAAE,UAAS,eAAc,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,CAAAA,OAAG;AAAC,MAAE,MAAM,kBAAiBA,EAAC;AAAA,EAAC,IAAG,cAAa,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,CAAAA,OAAG;AAAC,MAAE,MAAM,gBAAeA,EAAC;AAAA,EAAC,IAAG,gBAAe,EAAE,CAAC,MAAI,EAAE,CAAC,IAAE,CAAAA,OAAG;AAAC,MAAE,MAAM,kBAAiBA,EAAC;AAAA,EAAC,GAAE,GAAE,MAAK,GAAE,CAAC,cAAa,KAAI,KAAI,QAAO,QAAO,SAAQ,SAAQ,YAAW,YAAW,eAAc,aAAY,WAAW,CAAC,KAAG,mBAAE,QAAO,IAAE,CAAC,GAAE,GAAG;AAAC,GAAE,GAAG,SAAO,wCAAuC,GAAG,UAAQ,SAAS,GAAE;AAAC,IAAE,UAAU,GAAG,MAAK,EAAE;AAAC;AAAE,IAAM,KAAG,CAAC,EAAE;AAAE,IAAI,KAAG,EAAC,SAAQ,SAAS,GAAE;AAAC,KAAG,QAAS,OAAG;AAAC,MAAE,UAAU,EAAE,MAAK,CAAC;AAAA,EAAC,CAAE,GAAE,EAAE,IAAI,CAAC;AAAC,GAAE,aAAY,GAAE;", "names": ["o", "e", "a", "n", "t", "l", "r", "d", "s", "c", "u", "h", "R", "F", "f", "m", "A", "i", "T", "V", "j", "X", "B", "L", "p", "P"]}