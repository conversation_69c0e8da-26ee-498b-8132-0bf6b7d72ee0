/* tslint:disable */
 
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { Assembly } from './assembly';
import { ConstructorInfo } from './constructor-info';
import { CustomAttributeData } from './custom-attribute-data';
import { GenericParameterAttributes } from './generic-parameter-attributes';
import { MemberTypes } from './member-types';
import { MethodBase } from './method-base';
import { Module } from './module';
import { RuntimeTypeHandle } from './runtime-type-handle';
import { StructLayoutAttribute } from './struct-layout-attribute';
import { Type } from './type';
import { TypeAttributes } from './type-attributes';
 /**
 * 
 *
 * @export
 * @interface Type
 */
export interface Type {

    /**
     * @type {string}
     * @memberof Type
     */
    name?: string | null;

    /**
     * @type {Array<CustomAttributeData>}
     * @memberof Type
     */
    customAttributes?: Array<CustomAttributeData> | null;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isCollectible?: boolean;

    /**
     * @type {number}
     * @memberof Type
     */
    metadataToken?: number;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isInterface?: boolean;

    /**
     * @type {MemberTypes}
     * @memberof Type
     */
    memberType?: MemberTypes;

    /**
     * @type {string}
     * @memberof Type
     */
    namespace?: string | null;

    /**
     * @type {string}
     * @memberof Type
     */
    assemblyQualifiedName?: string | null;

    /**
     * @type {string}
     * @memberof Type
     */
    fullName?: string | null;

    /**
     * @type {Assembly}
     * @memberof Type
     */
    assembly?: Assembly;

    /**
     * @type {Module}
     * @memberof Type
     */
    module?: Module;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isNested?: boolean;

    /**
     * @type {Type}
     * @memberof Type
     */
    declaringType?: Type;

    /**
     * @type {MethodBase}
     * @memberof Type
     */
    declaringMethod?: MethodBase;

    /**
     * @type {Type}
     * @memberof Type
     */
    reflectedType?: Type;

    /**
     * @type {Type}
     * @memberof Type
     */
    underlyingSystemType?: Type;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isTypeDefinition?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isArray?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isByRef?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isPointer?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isConstructedGenericType?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isGenericParameter?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isGenericTypeParameter?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isGenericMethodParameter?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isGenericType?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isGenericTypeDefinition?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isSZArray?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isVariableBoundArray?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isByRefLike?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isFunctionPointer?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isUnmanagedFunctionPointer?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    hasElementType?: boolean;

    /**
     * @type {Array<Type>}
     * @memberof Type
     */
    genericTypeArguments?: Array<Type> | null;

    /**
     * @type {number}
     * @memberof Type
     */
    genericParameterPosition?: number;

    /**
     * @type {GenericParameterAttributes}
     * @memberof Type
     */
    genericParameterAttributes?: GenericParameterAttributes;

    /**
     * @type {TypeAttributes}
     * @memberof Type
     */
    attributes?: TypeAttributes;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isAbstract?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isImport?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isSealed?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isSpecialName?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isClass?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isNestedAssembly?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isNestedFamANDAssem?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isNestedFamily?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isNestedFamORAssem?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isNestedPrivate?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isNestedPublic?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isNotPublic?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isPublic?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isAutoLayout?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isExplicitLayout?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isLayoutSequential?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isAnsiClass?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isAutoClass?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isUnicodeClass?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isCOMObject?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isContextful?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isEnum?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isMarshalByRef?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isPrimitive?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isValueType?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isSignatureType?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isSecurityCritical?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isSecuritySafeCritical?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isSecurityTransparent?: boolean;

    /**
     * @type {StructLayoutAttribute}
     * @memberof Type
     */
    structLayoutAttribute?: StructLayoutAttribute;

    /**
     * @type {ConstructorInfo}
     * @memberof Type
     */
    typeInitializer?: ConstructorInfo;

    /**
     * @type {RuntimeTypeHandle}
     * @memberof Type
     */
    typeHandle?: RuntimeTypeHandle;

    /**
     * @type {string}
     * @memberof Type
     */
    guid?: string;

    /**
     * @type {Type}
     * @memberof Type
     */
    baseType?: Type;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isSerializable?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    containsGenericParameters?: boolean;

    /**
     * @type {boolean}
     * @memberof Type
     */
    isVisible?: boolean;
}
