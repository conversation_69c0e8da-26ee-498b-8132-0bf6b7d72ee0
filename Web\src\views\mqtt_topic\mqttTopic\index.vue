<script lang="ts" setup name="mqttTopic">
import { ref, reactive, onMounted } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { downloadStreamFile } from "/@/utils/download";
import { useMqttTopicApi } from '/@/api/mqtt_topic/mqttTopic';
import { useEmqxRealtimeApi } from '/@/api/emqxRealtime/emqxRealtime';
import editDialog from '/@/views/mqtt_topic/mqttTopic/component/editDialog.vue'
import printDialog from '/@/views/system/print/component/hiprint/preview.vue'
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import {
  Search,
  Plus,
  Delete,
  Edit,
  Refresh,
  Download,
  Upload,
  Connection,
  SwitchButton,
  Monitor,
  View,
  Warning,
  CircleCheck,
  CircleClose,
  Link,
  Unlock,
  Bell,
  Promotion,
  Message,
  DataAnalysis,
  Setting,
  Timer
} from '@element-plus/icons-vue';

const mqttTopicApi = useMqttTopicApi();
const emqxRealtimeApi = useEmqxRealtimeApi();
const printDialogRef = ref();
const editDialogRef = ref();
const publishDialogRef = ref();
const subscribeDialogRef = ref();
const state = reactive({
  exportLoading: false,
  tableLoading: false,
  stores: {},
  showAdvanceQueryUI: false,
  dropdownData: {} as any,
  selectData: [] as any[],
  tableQueryParams: {} as any,
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 0,
    field: 'createTime', // 默认的排序字段
    order: 'descending', // 排序方向
    descStr: 'descending', // 降序排序的关键字符
  },
  tableData: [],
  // 新增消息发布和订阅相关
  publishDialog: {
    visible: false,
    loading: false,
    form: {
      instanceId: '',
      topic: '',
      payload: '',
      qos: 0,
      retain: false
    }
  },
  subscribeDialog: {
    visible: false,
    loading: false,
    form: {
      instanceId: '',
      topic: '',
      qos: 0
    }
  },
  messageMonitor: {
    visible: false,
    messages: [] as any[],
    subscribedTopics: new Set(),
    autoScroll: true
  },
  topicStats: new Map(),
  refreshingStats: false,
});

// 页面加载时
onMounted(async () => {
});

// 查询操作
const handleQuery = async (params: any = {}) => {
  state.tableLoading = true;
  state.tableParams = Object.assign(state.tableParams, params);
  const result = await mqttTopicApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then(res => res.data.result);
  state.tableParams.total = result?.total;
  state.tableData = result?.items ?? [];
  state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};

// 删除
const delMqttTopic = (row: any) => {
  ElMessageBox.confirm(`确定要删除吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await mqttTopicApi.delete({ id: row.id });
    handleQuery();
    ElMessage.success("删除成功");
  }).catch(() => {});
};

// 批量删除
const batchDelMqttTopic = () => {
  ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await mqttTopicApi.batchDelete(state.selectData.map(u => ({ id: u.id }) )).then(res => {
      ElMessage.success(`成功批量删除${res.data.result}条记录`);
      handleQuery();
    });
  }).catch(() => {});
};

// 打开发布消息对话框
const openPublishDialog = (row: any) => {
  state.publishDialog.form.instanceId = row.instanceId || '';
  state.publishDialog.form.topic = row.topicName || '';
  state.publishDialog.form.payload = '';
  state.publishDialog.form.qos = row.qosLevel || 0;
  state.publishDialog.form.retain = row.retainMessage || false;
  state.publishDialog.visible = true;
};

// 发布消息
const publishMessage = async () => {
  if (!state.publishDialog.form.instanceId || !state.publishDialog.form.topic) {
    ElMessage.warning('实例ID和主题名称不能为空');
    return;
  }
  
  state.publishDialog.loading = true;
  try {
    const result = await emqxRealtimeApi.publish({
      instanceId: state.publishDialog.form.instanceId,
      topic: state.publishDialog.form.topic,
      payload: state.publishDialog.form.payload,
      qos: state.publishDialog.form.qos,
      retain: state.publishDialog.form.retain
    });
    
    if (result.data.success) {
      ElMessage.success('消息发布成功');
      state.publishDialog.visible = false;
      await refreshTopicStats();
    } else {
      ElMessage.error(`消息发布失败: ${result.data.message}`);
    }
  } catch (error) {
    console.error('消息发布失败:', error);
    ElMessage.error('消息发布失败');
  } finally {
    state.publishDialog.loading = false;
  }
};

// 打开订阅对话框
const openSubscribeDialog = (row: any) => {
  state.subscribeDialog.form.instanceId = row.instanceId || '';
  state.subscribeDialog.form.topic = row.topicName || '';
  state.subscribeDialog.form.qos = row.qosLevel || 0;
  state.subscribeDialog.visible = true;
};

// 订阅主题
const subscribeTopic = async () => {
  if (!state.subscribeDialog.form.instanceId || !state.subscribeDialog.form.topic) {
    ElMessage.warning('实例ID和主题名称不能为空');
    return;
  }
  
  state.subscribeDialog.loading = true;
  try {
    const result = await emqxRealtimeApi.subscribe({
      instanceId: state.subscribeDialog.form.instanceId,
      topic: state.subscribeDialog.form.topic,
      qos: state.subscribeDialog.form.qos
    });
    
    if (result.data.success) {
      ElMessage.success('主题订阅成功');
      state.subscribeDialog.visible = false;
      state.messageMonitor.subscribedTopics.add(state.subscribeDialog.form.topic);
      await refreshTopicStats();
    } else {
      ElMessage.error(`主题订阅失败: ${result.data.message}`);
    }
  } catch (error) {
    console.error('主题订阅失败:', error);
    ElMessage.error('主题订阅失败');
  } finally {
    state.subscribeDialog.loading = false;
  }
};

// 取消订阅主题
const unsubscribeTopic = async (row: any) => {
  if (!row.instanceId || !row.topicName) {
    ElMessage.warning('实例ID和主题名称不能为空');
    return;
  }
  
  try {
    const result = await emqxRealtimeApi.unsubscribe({
      instanceId: row.instanceId,
      topic: row.topicName
    });
    
    if (result.data.success) {
      ElMessage.success('取消订阅成功');
      state.messageMonitor.subscribedTopics.delete(row.topicName);
      await refreshTopicStats();
    } else {
      ElMessage.error(`取消订阅失败: ${result.data.message}`);
    }
  } catch (error) {
    console.error('取消订阅失败:', error);
    ElMessage.error('取消订阅失败');
  }
};

// 打开消息监控
const openMessageMonitor = () => {
  state.messageMonitor.visible = true;
  state.messageMonitor.messages = [];
};

// 刷新主题统计信息
const refreshTopicStats = async () => {
  state.refreshingStats = true;
  try {
    // 模拟获取主题统计信息
    for (const row of state.tableData) {
      if (row.instanceId && row.topicName) {
        // 这里可以调用实际的统计API
        state.topicStats.set(row.id, {
          messageCount: Math.floor(Math.random() * 1000),
          subscriptionCount: Math.floor(Math.random() * 50),
          lastActivity: new Date().toLocaleString()
        });
      }
    }
    ElMessage.success('主题统计信息刷新完成');
  } catch (error) {
    console.error('刷新主题统计失败:', error);
    ElMessage.error('刷新主题统计失败');
  } finally {
    state.refreshingStats = false;
  }
};

// 批量发布消息
const batchPublishMessage = () => {
  if (state.selectData.length === 0) {
    ElMessage.warning('请选择要发布消息的主题');
    return;
  }
  
  // 打开批量发布对话框
  state.publishDialog.form.instanceId = '';
  state.publishDialog.form.topic = state.selectData.map(item => item.topicName).join(',');
  state.publishDialog.form.payload = '';
  state.publishDialog.form.qos = 0;
  state.publishDialog.form.retain = false;
  state.publishDialog.visible = true;
};

// 批量订阅主题
const batchSubscribeTopics = async () => {
  if (state.selectData.length === 0) {
    ElMessage.warning('请选择要订阅的主题');
    return;
  }
  
  const topics = state.selectData.filter(item => item.instanceId && item.topicName);
  if (topics.length === 0) {
    ElMessage.warning('所选主题中没有有效的实例ID和主题名称');
    return;
  }
  
  let successCount = 0;
  for (const topic of topics) {
    try {
      const result = await emqxRealtimeApi.subscribe({
        instanceId: topic.instanceId,
        topic: topic.topicName,
        qos: topic.qosLevel || 0
      });
      
      if (result.data.success) {
        successCount++;
        state.messageMonitor.subscribedTopics.add(topic.topicName);
      }
    } catch (error) {
      console.error(`订阅主题 ${topic.topicName} 失败:`, error);
    }
  }
  
  ElMessage.success(`批量订阅完成，成功订阅 ${successCount} 个主题`);
  await refreshTopicStats();
};

// 获取主题统计信息
const getTopicStats = (rowId: string) => {
  return state.topicStats.get(rowId) || {
    messageCount: 0,
    subscriptionCount: 0,
    lastActivity: '未知'
  };
};

// 获取主题类型颜色
const getTopicTypeColor = (type: string) => {
  const typeMap: Record<string, string> = {
    'device_data': 'success',
    'system_event': 'warning',
    'alert': 'danger',
    'control': 'info'
  };
  return typeMap[type] || '';
};

// 获取主题类型图标
const getTopicTypeIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    'device_data': DataAnalysis,
    'system_event': Setting,
    'alert': Warning,
    'control': SwitchButton
  };
  return iconMap[type] || Message;
};

// 获取主题类型标签
const getTopicTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    'device_data': '设备数据',
    'system_event': '系统事件',
    'alert': '告警信息',
    'control': '控制指令'
  };
  return labelMap[type] || type || '未知';
};

// 获取QoS等级颜色
const getQosLevelColor = (level: number) => {
  const colorMap: Record<number, string> = {
    0: 'info',
    1: 'warning',
    2: 'danger'
  };
  return colorMap[level] || 'info';
};

// 查看主题详情
const viewTopicDetails = (row: any) => {
  const details = `
    <div style="text-align: left; line-height: 1.8;">
      <h4 style="margin-bottom: 16px; color: #409EFF;">主题详细信息</h4>
      <p><strong>主题名称：</strong>${row.topicName || '未设置'}</p>
      <p><strong>主题类型：</strong>${getTopicTypeLabel(row.topicType)}</p>
      <p><strong>QoS等级：</strong>${row.qosLevel || 0}</p>
      <p><strong>保留消息：</strong>${row.retainMessage ? '是' : '否'}</p>
      <p><strong>状态：</strong>${row.isEnabled ? '启用' : '禁用'}</p>
      <p><strong>系统主题：</strong>${row.isSystemTopic ? '是' : '否'}</p>
      <p><strong>描述：</strong>${row.description || '无'}</p>
    </div>
  `;
  
  ElMessageBox.alert(details, '主题详情', {
    dangerouslyUseHTMLString: true,
    confirmButtonText: '确定'
  });
};

handleQuery();

// 页面加载完成后刷新统计信息
onMounted(() => {
  setTimeout(() => {
    refreshTopicStats();
  }, 1000);
});
</script>
<template>
  <div class="mqtt-topic-container" v-loading="state.exportLoading">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">MQTT 主题管理</h1>
      <div class="stats-grid">
        <div class="stat-card active">
          <div class="stat-value">{{ state.tableData.filter(item => item.isEnabled).length }}</div>
          <div class="stat-label">活跃主题</div>
        </div>
        <div class="stat-card subscribed">
          <div class="stat-value">{{ state.messageMonitor.subscribedTopics.size }}</div>
          <div class="stat-label">已订阅主题</div>
        </div>
        <div class="stat-card total">
          <div class="stat-value">{{ state.tableData.length }}</div>
          <div class="stat-label">总主题数</div>
        </div>
      </div>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="hover">
      <el-form :model="state.tableQueryParams" class="search-form">
        <el-row :gutter="16">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="关键字">
              <el-input 
                v-model="state.tableQueryParams.keyword" 
                placeholder="搜索主题名称、类型等"
                :prefix-icon="Search"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="实例ID">
              <el-input 
                v-model="state.tableQueryParams.instanceId" 
                placeholder="请输入实例ID"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="主题状态">
              <el-select v-model="state.tableQueryParams.isEnabled" placeholder="选择状态" clearable>
                <el-option label="启用" :value="true" />
                <el-option label="禁用" :value="false" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <div class="action-buttons">
        <el-button type="primary" :icon="Search" @click="handleQuery" v-auth="'mqttTopic:page'">
          查询
        </el-button>
        <el-button :icon="Refresh" @click="() => state.tableQueryParams = {}">
          重置
        </el-button>
        <el-button :icon="Plus" type="success" @click="editDialogRef.openDialog(null, '新增MQTT主题')" v-auth="'mqttTopic:add'">
          新增主题
        </el-button>
        <el-button :icon="Delete" type="danger" @click="batchDelMqttTopic" :disabled="state.selectData.length == 0" v-auth="'mqttTopic:batchDelete'">
          批量删除
        </el-button>
        <el-button :icon="Promotion" type="warning" @click="batchPublishMessage" :disabled="state.selectData.length == 0">
          批量发布
        </el-button>
        <el-button :icon="Bell" type="info" @click="batchSubscribeTopics" :disabled="state.selectData.length == 0">
          批量订阅
        </el-button>
        <el-button :icon="Monitor" @click="openMessageMonitor">
          消息监控
        </el-button>
        <el-button :icon="DataAnalysis" @click="refreshTopicStats" :loading="state.refreshingStats">
          刷新统计
        </el-button>
      </div>
    </el-card>

    <!-- 高级搜索区域 -->
    <el-card class="advanced-search-card" shadow="hover">
      <template #header>
        <span>高级搜索</span>
      </template>
      <el-form :model="state.tableQueryParams" label-width="120px">
        <el-row :gutter="16">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="主题类型">
              <el-select v-model="state.tableQueryParams.topicType" placeholder="选择主题类型" clearable>
                <el-option label="设备数据" value="device_data" />
                <el-option label="系统事件" value="system_event" />
                <el-option label="告警信息" value="alert" />
                <el-option label="控制指令" value="control" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="QoS等级">
              <el-select v-model="state.tableQueryParams.qosLevel" placeholder="选择QoS等级" clearable>
                <el-option label="0 - 最多一次" :value="0" />
                <el-option label="1 - 至少一次" :value="1" />
                <el-option label="2 - 恰好一次" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="保留消息">
              <el-select v-model="state.tableQueryParams.retainMessage" placeholder="选择保留消息" clearable>
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="系统主题">
              <el-select v-model="state.tableQueryParams.isSystemTopic" placeholder="选择系统主题" clearable>
                <el-option label="是" :value="true" />
                <el-option label="否" :value="false" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card" shadow="hover">
      <el-table 
        :data="state.tableData" 
        @selection-change="(val: any[]) => { state.selectData = val; }" 
        v-loading="state.tableLoading" 
        row-key="id" 
        @sort-change="sortChange" 
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="50" align="center" v-if="auth('mqttTopic:batchDelete')" />
        <el-table-column type="index" label="序号" width="60" align="center"/>
        <el-table-column prop="instanceId" label="实例ID" width="120" show-overflow-tooltip />
        <el-table-column prop="topicName" label="主题名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="topicType" label="主题类型" width="120">
          <template #default="scope">
            <el-tag 
              :type="getTopicTypeColor(scope.row.topicType)" 
              :icon="getTopicTypeIcon(scope.row.topicType)"
              class="status-tag"
            >
              <el-icon class="status-icon"><component :is="getTopicTypeIcon(scope.row.topicType)" /></el-icon>
              {{ getTopicTypeLabel(scope.row.topicType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="qosLevel" label="QoS等级" width="100">
          <template #default="scope">
            <el-tag 
              :type="getQosLevelColor(scope.row.qosLevel)"
              class="status-tag"
            >
              {{ scope.row.qosLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="retainMessage" label="保留消息" width="100">
          <template #default="scope">
            <el-tag 
              :type="scope.row.retainMessage ? 'success' : 'info'"
              :icon="scope.row.retainMessage ? CircleCheck : CircleClose"
              class="status-tag"
            >
              <el-icon class="status-icon"><component :is="scope.row.retainMessage ? CircleCheck : CircleClose" /></el-icon>
              {{ scope.row.retainMessage ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="实时统计" width="180">
          <template #default="scope">
            <div class="stats-info">
              <div class="stat-item">
                <el-icon><Message /></el-icon>
                <span>消息: {{ getTopicStats(scope.row.id).messageCount }}</span>
              </div>
              <div class="stat-item">
                <el-icon><Bell /></el-icon>
                <span>订阅: {{ getTopicStats(scope.row.id).subscriptionCount }}</span>
              </div>
              <div class="stat-time">{{ getTopicStats(scope.row.id).lastActivity }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="isEnabled" label="状态" width="80">
          <template #default="scope">
            <el-tag 
              :type="scope.row.isEnabled ? 'success' : 'danger'"
              :icon="scope.row.isEnabled ? CircleCheck : CircleClose"
              class="status-tag"
            >
              <el-icon class="status-icon"><component :is="scope.row.isEnabled ? CircleCheck : CircleClose" /></el-icon>
              {{ scope.row.isEnabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
        <el-table-column label="操作" width="280" align="center" fixed="right">
          <template #default="scope">
            <div class="operation-buttons">
              <el-tooltip content="编辑" placement="top">
                <button 
                  class="operation-btn edit" 
                  @click="editDialogRef.openDialog(scope.row, '编辑MQTT主题')" 
                  v-auth="'mqttTopic:update'"
                >
                  <el-icon><Edit /></el-icon>
                </button>
              </el-tooltip>
              <el-tooltip content="发布消息" placement="top">
                <button class="operation-btn publish" @click="openPublishDialog(scope.row)">
                  <el-icon><Promotion /></el-icon>
                </button>
              </el-tooltip>
              <el-tooltip content="订阅主题" placement="top">
                <button class="operation-btn subscribe" @click="openSubscribeDialog(scope.row)">
                  <el-icon><Bell /></el-icon>
                </button>
              </el-tooltip>
              <el-tooltip content="查看详情" placement="top">
                <button class="operation-btn view" @click="viewTopicDetails(scope.row)">
                  <el-icon><View /></el-icon>
                </button>
              </el-tooltip>
              <el-tooltip content="取消订阅" placement="top">
                <button class="operation-btn unsubscribe" @click="unsubscribeTopic(scope.row)">
                  <el-icon><SwitchButton /></el-icon>
                </button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <button 
                  class="operation-btn delete" 
                  @click="delMqttTopic(scope.row)" 
                  v-auth="'mqttTopic:delete'"
                >
                  <el-icon><Delete /></el-icon>
                </button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <el-pagination 
        v-model:currentPage="state.tableParams.page"
        v-model:page-size="state.tableParams.pageSize"
        @size-change="(val: any) => handleQuery({ pageSize: val })"
        @current-change="(val: any) => handleQuery({ page: val })"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :total="state.tableParams.total"
        background
        style="margin-top: 20px; justify-content: center;"
      />
    </el-card>
    <!-- 表格卡片 -->
    <el-card class="table-card" shadow="hover">
      <el-table 
        :data="state.tableData" 
        @selection-change="(val: any[]) => { state.selectData = val; }" 
        style="width: 100%" 
        v-loading="state.tableLoading" 
        tooltip-effect="light" 
        row-key="id" 
        @sort-change="sortChange" 
        stripe
        size="default">
        
        <el-table-column type="selection" width="50" align="center" v-if="auth('mqttTopic:batchDelete') || auth('mqttTopic:export')" />
        <el-table-column type="index" label="序号" width="60" align="center"/>
        
        <el-table-column prop="topicName" label="主题名称" min-width="200" show-overflow-tooltip>
          <template #default="scope">
            <div class="topic-name">
              <el-icon class="mr-1"><Message /></el-icon>
              <span class="font-medium">{{ scope.row.topicName }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="topicType" label="主题类型" width="120" align="center">
          <template #default="scope">
            <el-tag 
              :type="getTopicTypeColor(scope.row.topicType)" 
              :icon="getTopicTypeIcon(scope.row.topicType)"
              size="small">
              {{ getTopicTypeLabel(scope.row.topicType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="qosLevel" label="QoS等级" width="100" align="center">
          <template #default="scope">
            <el-tag 
              :type="getQosLevelColor(scope.row.qosLevel)" 
              size="small">
              QoS {{ scope.row.qosLevel || 0 }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="retainMessage" label="保留消息" width="100" align="center">
          <template #default="scope">
            <el-tag 
              :type="scope.row.retainMessage ? 'success' : 'info'" 
              :icon="scope.row.retainMessage ? CircleCheck : CircleClose"
              size="small">
              {{ scope.row.retainMessage ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="实时统计" width="180" align="center">
          <template #default="scope">
            <div class="stats-container">
              <div class="stat-item">
                <el-icon class="stat-icon"><DataAnalysis /></el-icon>
                <span class="stat-value">{{ getTopicStats(scope.row.id).messageCount }}</span>
                <span class="stat-label">消息</span>
              </div>
              <div class="stat-item">
                <el-icon class="stat-icon"><Connection /></el-icon>
                <span class="stat-value">{{ getTopicStats(scope.row.id).subscriptionCount }}</span>
                <span class="stat-label">订阅</span>
              </div>
              <div class="stat-time">{{ getTopicStats(scope.row.id).lastActivity }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="isEnabled" label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag 
              :type="scope.row.isEnabled ? 'success' : 'danger'" 
              :icon="scope.row.isEnabled ? CircleCheck : CircleClose"
              size="small">
              {{ scope.row.isEnabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="isSystemTopic" label="系统主题" width="100" align="center">
          <template #default="scope">
            <el-tag 
              :type="scope.row.isSystemTopic ? 'warning' : 'info'" 
              :icon="scope.row.isSystemTopic ? Setting : Message"
              size="small">
              {{ scope.row.isSystemTopic ? '系统' : '用户' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
        
        <el-table-column label="操作" width="200" align="center" fixed="right" v-if="auth('mqttTopic:update') || auth('mqttTopic:delete')">
          <template #default="scope">
            <div class="operation-buttons">
              <el-tooltip content="发布消息" placement="top">
                <el-button 
                  class="op-btn publish-btn" 
                  circle 
                  size="small" 
                  @click="openPublishDialog(scope.row)">
                  <el-icon><Promotion /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip content="订阅主题" placement="top">
                <el-button 
                  class="op-btn subscribe-btn" 
                  circle 
                  size="small" 
                  @click="openSubscribeDialog(scope.row)">
                  <el-icon><Bell /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip content="查看详情" placement="top">
                <el-button 
                  class="op-btn view-btn" 
                  circle 
                  size="small" 
                  @click="viewTopicDetails(scope.row)">
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip content="编辑" placement="top">
                <el-button 
                  class="op-btn edit-btn" 
                  circle 
                  size="small" 
                  @click="editDialogRef.openDialog(scope.row, '编辑MQTT 主题信息表')" 
                  v-auth="'mqttTopic:update'">
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              
              <el-tooltip content="删除" placement="top">
                <el-button 
                  class="op-btn delete-btn" 
                  circle 
                  size="small" 
                  @click="delMqttTopic(scope.row)" 
                  v-auth="'mqttTopic:delete'">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination 
          v-model:currentPage="state.tableParams.page"
          v-model:page-size="state.tableParams.pageSize"
          @size-change="(val: any) => handleQuery({ pageSize: val })"
          @current-change="(val: any) => handleQuery({ page: val })"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :total="state.tableParams.total"
          size="default"
          background />
      </div>
    </el-card>
    
    <printDialog ref="printDialogRef" :title="'打印MQTT 主题信息表'" @reloadTable="handleQuery" />
    <editDialog ref="editDialogRef" @reloadTable="handleQuery" />
  </div>

  <!-- 发布消息对话框 -->
  <el-dialog v-model="state.publishDialog.visible" title="发布消息" width="600px" destroy-on-close>
    <el-form :model="state.publishDialog.form" label-width="100px">
      <el-form-item label="实例ID">
        <el-input v-model="state.publishDialog.form.instanceId" placeholder="请输入实例ID" />
      </el-form-item>
      <el-form-item label="主题">
        <el-input v-model="state.publishDialog.form.topic" placeholder="请输入主题名称" />
      </el-form-item>
      <el-form-item label="消息内容">
        <el-input v-model="state.publishDialog.form.payload" type="textarea" :rows="4" placeholder="请输入消息内容" />
      </el-form-item>
      <el-form-item label="QoS等级">
        <el-select v-model="state.publishDialog.form.qos" placeholder="请选择QoS等级">
          <el-option label="0 - 最多一次" :value="0" />
          <el-option label="1 - 至少一次" :value="1" />
          <el-option label="2 - 恰好一次" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="保留消息">
        <el-switch v-model="state.publishDialog.form.retain" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="state.publishDialog.visible = false">取消</el-button>
      <el-button type="primary" @click="publishMessage" :loading="state.publishDialog.loading">发布</el-button>
    </template>
  </el-dialog>

  <!-- 订阅对话框 -->
  <el-dialog v-model="state.subscribeDialog.visible" title="订阅主题" width="500px" destroy-on-close>
    <el-form :model="state.subscribeDialog.form" label-width="100px">
      <el-form-item label="实例ID">
        <el-input v-model="state.subscribeDialog.form.instanceId" placeholder="请输入实例ID" />
      </el-form-item>
      <el-form-item label="主题">
        <el-input v-model="state.subscribeDialog.form.topic" placeholder="请输入主题名称" />
      </el-form-item>
      <el-form-item label="QoS等级">
        <el-select v-model="state.subscribeDialog.form.qos" placeholder="请选择QoS等级">
          <el-option label="0 - 最多一次" :value="0" />
          <el-option label="1 - 至少一次" :value="1" />
          <el-option label="2 - 恰好一次" :value="2" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="state.subscribeDialog.visible = false">取消</el-button>
      <el-button type="primary" @click="subscribeTopic" :loading="state.subscribeDialog.loading">订阅</el-button>
    </template>
  </el-dialog>

  <!-- 消息监控对话框 -->
  <el-dialog v-model="state.messageMonitor.visible" title="消息监控" width="800px" destroy-on-close>
    <div class="message-monitor">
      <div class="monitor-header">
        <el-tag v-for="topic in Array.from(state.messageMonitor.subscribedTopics)" :key="topic" class="mr-2 mb-2">
          {{ topic }}
        </el-tag>
      </div>
      <el-divider>实时消息</el-divider>
      <div class="message-list" style="height: 300px; overflow-y: auto;">
        <div v-for="(message, index) in state.messageMonitor.messages" :key="index" class="message-item">
          <div class="message-header">
            <span class="topic">{{ message.topic }}</span>
            <span class="time">{{ message.timestamp }}</span>
          </div>
          <div class="message-content">{{ message.payload }}</div>
        </div>
        <el-empty v-if="state.messageMonitor.messages.length === 0" description="暂无消息" />
      </div>
    </div>
    <template #footer>
      <el-button @click="state.messageMonitor.visible = false">关闭</el-button>
      <el-button type="primary" @click="state.messageMonitor.messages = []">清空消息</el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
.mqtt-topic-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 12px;
  padding: 20px;
  color: white;
  position: relative;
  overflow: hidden;
  text-align: center;
}

.stat-card.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.subscribed {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-card.total {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  margin: 8px 0 4px 0;
  position: relative;
  z-index: 1;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
  margin: 0;
  position: relative;
  z-index: 1;
}

.search-card {
  margin-bottom: 16px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin-bottom: 0;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.advanced-search-card {
  margin-bottom: 16px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.table-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.topic-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.topic-name .font-medium {
  font-weight: 500;
  color: #303133;
}

.stats-container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.stat-icon {
  font-size: 14px;
  color: #409eff;
}

.stat-value {
  font-weight: 600;
  color: #303133;
}

.stat-label {
  color: #909399;
}

.stat-time {
  font-size: 11px;
  color: #c0c4cc;
  text-align: center;
}

.operation-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

.operation-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.operation-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.operation-btn.edit {
  background: linear-gradient(135deg, #909399, #a6a9ad);
}

.operation-btn.publish {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.operation-btn.subscribe {
  background: linear-gradient(135deg, #e6a23c, #f0a020);
}

.operation-btn.view {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.operation-btn.unsubscribe {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.operation-btn.delete {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.op-btn {
  width: 32px;
  height: 32px;
  border: none;
  transition: all 0.3s ease;
}

.op-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.publish-btn {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
}

.publish-btn:hover {
  background: linear-gradient(135deg, #85ce61, #67c23a);
}

.subscribe-btn {
  background: linear-gradient(135deg, #e6a23c, #f0a020);
  color: white;
}

.subscribe-btn:hover {
  background: linear-gradient(135deg, #f0a020, #e6a23c);
}

.view-btn {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  color: white;
}

.view-btn:hover {
  background: linear-gradient(135deg, #66b1ff, #409eff);
}

.edit-btn {
  background: linear-gradient(135deg, #909399, #a6a9ad);
  color: white;
}

.edit-btn:hover {
  background: linear-gradient(135deg, #a6a9ad, #909399);
}

.delete-btn {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  color: white;
}

.delete-btn:hover {
  background: linear-gradient(135deg, #f78989, #f56c6c);
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 16px 0;
}

.status-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  border-radius: 6px;
  font-weight: 500;
}

.status-icon {
  font-size: 12px;
}

.stats-info {
  font-size: 12px;
  line-height: 1.4;
}

.message-monitor .monitor-header {
  margin-bottom: 16px;
}

.message-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 8px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.message-header .topic {
  font-weight: bold;
  color: #409eff;
}

.message-header .time {
  color: #909399;
}

.message-content {
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mqtt-topic-container {
    padding: 12px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .operation-buttons {
    gap: 2px;
  }
  
  .operation-btn, .op-btn {
    width: 28px;
    height: 28px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 20px;
  }
  
  .stat-value {
    font-size: 24px;
  }
}

/* 全局样式优化 */
:deep(.el-input), :deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background: #fafbfc;
}

:deep(.el-table th) {
  background: #fafbfc;
  color: #606266;
  font-weight: 600;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f5f7fa;
}

:deep(.el-table__row:hover) {
  background: #f8f9fa;
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff, #66b1ff);
  border: none;
}

:deep(.el-button--success) {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border: none;
}

:deep(.el-button--warning) {
  background: linear-gradient(135deg, #e6a23c, #f0a020);
  border: none;
}

:deep(.el-button--danger) {
  background: linear-gradient(135deg, #f56c6c, #f78989);
  border: none;
}

:deep(.el-button--info) {
  background: linear-gradient(135deg, #909399, #a6a9ad);
  border: none;
}

:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
}
</style>