import {
  require_vue
} from "./chunk-VLKDW56T.js";
import "./chunk-YRXSRAMJ.js";
import {
  __commonJS
} from "./chunk-PLDDJCW6.js";

// node_modules/vue-grid-layout/dist/vue-grid-layout.common.js
var require_vue_grid_layout_common = __commonJS({
  "node_modules/vue-grid-layout/dist/vue-grid-layout.common.js"(exports, module) {
    module.exports = /******/
    function(modules) {
      var installedModules = {};
      function __webpack_require__(moduleId) {
        if (installedModules[moduleId]) {
          return installedModules[moduleId].exports;
        }
        var module2 = installedModules[moduleId] = {
          /******/
          i: moduleId,
          /******/
          l: false,
          /******/
          exports: {}
          /******/
        };
        modules[moduleId].call(module2.exports, module2, module2.exports, __webpack_require__);
        module2.l = true;
        return module2.exports;
      }
      __webpack_require__.m = modules;
      __webpack_require__.c = installedModules;
      __webpack_require__.d = function(exports2, name, getter) {
        if (!__webpack_require__.o(exports2, name)) {
          Object.defineProperty(exports2, name, { enumerable: true, get: getter });
        }
      };
      __webpack_require__.r = function(exports2) {
        if (typeof Symbol !== "undefined" && Symbol.toStringTag) {
          Object.defineProperty(exports2, Symbol.toStringTag, { value: "Module" });
        }
        Object.defineProperty(exports2, "__esModule", { value: true });
      };
      __webpack_require__.t = function(value, mode) {
        if (mode & 1) value = __webpack_require__(value);
        if (mode & 8) return value;
        if (mode & 4 && typeof value === "object" && value && value.__esModule) return value;
        var ns = /* @__PURE__ */ Object.create(null);
        __webpack_require__.r(ns);
        Object.defineProperty(ns, "default", { enumerable: true, value });
        if (mode & 2 && typeof value != "string") for (var key in value) __webpack_require__.d(ns, key, (function(key2) {
          return value[key2];
        }).bind(null, key));
        return ns;
      };
      __webpack_require__.n = function(module2) {
        var getter = module2 && module2.__esModule ? (
          /******/
          function getDefault() {
            return module2["default"];
          }
        ) : (
          /******/
          function getModuleExports() {
            return module2;
          }
        );
        __webpack_require__.d(getter, "a", getter);
        return getter;
      };
      __webpack_require__.o = function(object, property) {
        return Object.prototype.hasOwnProperty.call(object, property);
      };
      __webpack_require__.p = "";
      return __webpack_require__(__webpack_require__.s = "fb15");
    }({
      /***/
      "01f9": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var LIBRARY = __webpack_require__("2d00");
          var $export = __webpack_require__("5ca1");
          var redefine = __webpack_require__("2aba");
          var hide = __webpack_require__("32e9");
          var Iterators = __webpack_require__("84f2");
          var $iterCreate = __webpack_require__("41a0");
          var setToStringTag = __webpack_require__("7f20");
          var getPrototypeOf = __webpack_require__("38fd");
          var ITERATOR = __webpack_require__("2b4c")("iterator");
          var BUGGY = !([].keys && "next" in [].keys());
          var FF_ITERATOR = "@@iterator";
          var KEYS = "keys";
          var VALUES = "values";
          var returnThis = function() {
            return this;
          };
          module2.exports = function(Base, NAME, Constructor, next, DEFAULT, IS_SET, FORCED) {
            $iterCreate(Constructor, NAME, next);
            var getMethod = function(kind) {
              if (!BUGGY && kind in proto) return proto[kind];
              switch (kind) {
                case KEYS:
                  return function keys() {
                    return new Constructor(this, kind);
                  };
                case VALUES:
                  return function values() {
                    return new Constructor(this, kind);
                  };
              }
              return function entries() {
                return new Constructor(this, kind);
              };
            };
            var TAG = NAME + " Iterator";
            var DEF_VALUES = DEFAULT == VALUES;
            var VALUES_BUG = false;
            var proto = Base.prototype;
            var $native = proto[ITERATOR] || proto[FF_ITERATOR] || DEFAULT && proto[DEFAULT];
            var $default = $native || getMethod(DEFAULT);
            var $entries = DEFAULT ? !DEF_VALUES ? $default : getMethod("entries") : void 0;
            var $anyNative = NAME == "Array" ? proto.entries || $native : $native;
            var methods, key, IteratorPrototype;
            if ($anyNative) {
              IteratorPrototype = getPrototypeOf($anyNative.call(new Base()));
              if (IteratorPrototype !== Object.prototype && IteratorPrototype.next) {
                setToStringTag(IteratorPrototype, TAG, true);
                if (!LIBRARY && typeof IteratorPrototype[ITERATOR] != "function") hide(IteratorPrototype, ITERATOR, returnThis);
              }
            }
            if (DEF_VALUES && $native && $native.name !== VALUES) {
              VALUES_BUG = true;
              $default = function values() {
                return $native.call(this);
              };
            }
            if ((!LIBRARY || FORCED) && (BUGGY || VALUES_BUG || !proto[ITERATOR])) {
              hide(proto, ITERATOR, $default);
            }
            Iterators[NAME] = $default;
            Iterators[TAG] = returnThis;
            if (DEFAULT) {
              methods = {
                values: DEF_VALUES ? $default : getMethod(VALUES),
                keys: IS_SET ? $default : getMethod(KEYS),
                entries: $entries
              };
              if (FORCED) for (key in methods) {
                if (!(key in proto)) redefine(proto, key, methods[key]);
              }
              else $export($export.P + $export.F * (BUGGY || VALUES_BUG), NAME, methods);
            }
            return methods;
          };
        }
      ),
      /***/
      "02f4": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var toInteger = __webpack_require__("4588");
          var defined = __webpack_require__("be13");
          module2.exports = function(TO_STRING) {
            return function(that, pos) {
              var s = String(defined(that));
              var i = toInteger(pos);
              var l = s.length;
              var a, b;
              if (i < 0 || i >= l) return TO_STRING ? "" : void 0;
              a = s.charCodeAt(i);
              return a < 55296 || a > 56319 || i + 1 === l || (b = s.charCodeAt(i + 1)) < 56320 || b > 57343 ? TO_STRING ? s.charAt(i) : a : TO_STRING ? s.slice(i, i + 2) : (a - 55296 << 10) + (b - 56320) + 65536;
            };
          };
        }
      ),
      /***/
      "0390": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var at = __webpack_require__("02f4")(true);
          module2.exports = function(S, index, unicode) {
            return index + (unicode ? at(S, index).length : 1);
          };
        }
      ),
      /***/
      "083e": (
        /***/
        function(module2, __webpack_exports__, __webpack_require__) {
          "use strict";
          var _node_modules_vue_style_loader_index_js_ref_6_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_node_modules_vue_loader_v16_dist_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_v16_dist_index_js_ref_0_1_GridItem_vue_vue_type_style_index_0_id_46ff2fc8_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("c541");
          var _node_modules_vue_style_loader_index_js_ref_6_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_node_modules_vue_loader_v16_dist_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_v16_dist_index_js_ref_0_1_GridItem_vue_vue_type_style_index_0_id_46ff2fc8_lang_css__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_6_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_node_modules_vue_loader_v16_dist_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_v16_dist_index_js_ref_0_1_GridItem_vue_vue_type_style_index_0_id_46ff2fc8_lang_css__WEBPACK_IMPORTED_MODULE_0__);
        }
      ),
      /***/
      "0bfb": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var anObject = __webpack_require__("cb7c");
          module2.exports = function() {
            var that = anObject(this);
            var result = "";
            if (that.global) result += "g";
            if (that.ignoreCase) result += "i";
            if (that.multiline) result += "m";
            if (that.unicode) result += "u";
            if (that.sticky) result += "y";
            return result;
          };
        }
      ),
      /***/
      "0d58": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var $keys = __webpack_require__("ce10");
          var enumBugKeys = __webpack_require__("e11e");
          module2.exports = Object.keys || function keys(O) {
            return $keys(O, enumBugKeys);
          };
        }
      ),
      /***/
      "11e9": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var pIE = __webpack_require__("52a7");
          var createDesc = __webpack_require__("4630");
          var toIObject = __webpack_require__("6821");
          var toPrimitive = __webpack_require__("6a99");
          var has = __webpack_require__("69a8");
          var IE8_DOM_DEFINE = __webpack_require__("c69a");
          var gOPD = Object.getOwnPropertyDescriptor;
          exports2.f = __webpack_require__("9e1e") ? gOPD : function getOwnPropertyDescriptor(O, P) {
            O = toIObject(O);
            P = toPrimitive(P, true);
            if (IE8_DOM_DEFINE) try {
              return gOPD(O, P);
            } catch (e) {
            }
            if (has(O, P)) return createDesc(!pIE.f.call(O, P), O[P]);
          };
        }
      ),
      /***/
      "1495": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var dP = __webpack_require__("86cc");
          var anObject = __webpack_require__("cb7c");
          var getKeys = __webpack_require__("0d58");
          module2.exports = __webpack_require__("9e1e") ? Object.defineProperties : function defineProperties(O, Properties) {
            anObject(O);
            var keys = getKeys(Properties);
            var length = keys.length;
            var i = 0;
            var P;
            while (length > i) dP.f(O, P = keys[i++], Properties[P]);
            return O;
          };
        }
      ),
      /***/
      "18d2": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var browserDetector = __webpack_require__("18e9");
          module2.exports = function(options) {
            options = options || {};
            var reporter = options.reporter;
            var batchProcessor = options.batchProcessor;
            var getState = options.stateHandler.getState;
            if (!reporter) {
              throw new Error("Missing required dependency: reporter.");
            }
            function addListener(element, listener) {
              function listenerProxy() {
                listener(element);
              }
              if (browserDetector.isIE(8)) {
                getState(element).object = {
                  proxy: listenerProxy
                };
                element.attachEvent("onresize", listenerProxy);
              } else {
                var object = getObject(element);
                if (!object) {
                  throw new Error("Element is not detectable by this strategy.");
                }
                object.contentDocument.defaultView.addEventListener("resize", listenerProxy);
              }
            }
            function buildCssTextString(rules) {
              var seperator = options.important ? " !important; " : "; ";
              return (rules.join(seperator) + seperator).trim();
            }
            function makeDetectable(options2, element, callback) {
              if (!callback) {
                callback = element;
                element = options2;
                options2 = null;
              }
              options2 = options2 || {};
              var debug = options2.debug;
              function injectObject(element2, callback2) {
                var OBJECT_STYLE = buildCssTextString(["display: block", "position: absolute", "top: 0", "left: 0", "width: 100%", "height: 100%", "border: none", "padding: 0", "margin: 0", "opacity: 0", "z-index: -1000", "pointer-events: none"]);
                var positionCheckPerformed = false;
                var style = window.getComputedStyle(element2);
                var width = element2.offsetWidth;
                var height = element2.offsetHeight;
                getState(element2).startSize = {
                  width,
                  height
                };
                function mutateDom() {
                  function alterPositionStyles() {
                    if (style.position === "static") {
                      element2.style.setProperty("position", "relative", options2.important ? "important" : "");
                      var removeRelativeStyles = function(reporter2, element3, style2, property) {
                        function getNumericalValue(value2) {
                          return value2.replace(/[^-\d\.]/g, "");
                        }
                        var value = style2[property];
                        if (value !== "auto" && getNumericalValue(value) !== "0") {
                          reporter2.warn("An element that is positioned static has style." + property + "=" + value + " which is ignored due to the static positioning. The element will need to be positioned relative, so the style." + property + " will be set to 0. Element: ", element3);
                          element3.style.setProperty(property, "0", options2.important ? "important" : "");
                        }
                      };
                      removeRelativeStyles(reporter, element2, style, "top");
                      removeRelativeStyles(reporter, element2, style, "right");
                      removeRelativeStyles(reporter, element2, style, "bottom");
                      removeRelativeStyles(reporter, element2, style, "left");
                    }
                  }
                  function onObjectLoad() {
                    if (!positionCheckPerformed) {
                      alterPositionStyles();
                    }
                    function getDocument(element3, callback3) {
                      if (!element3.contentDocument) {
                        var state = getState(element3);
                        if (state.checkForObjectDocumentTimeoutId) {
                          window.clearTimeout(state.checkForObjectDocumentTimeoutId);
                        }
                        state.checkForObjectDocumentTimeoutId = setTimeout(function checkForObjectDocument() {
                          state.checkForObjectDocumentTimeoutId = 0;
                          getDocument(element3, callback3);
                        }, 100);
                        return;
                      }
                      callback3(element3.contentDocument);
                    }
                    var objectElement = this;
                    getDocument(objectElement, function onObjectDocumentReady(objectDocument) {
                      callback2(element2);
                    });
                  }
                  if (style.position !== "") {
                    alterPositionStyles(style);
                    positionCheckPerformed = true;
                  }
                  var object = document.createElement("object");
                  object.style.cssText = OBJECT_STYLE;
                  object.tabIndex = -1;
                  object.type = "text/html";
                  object.setAttribute("aria-hidden", "true");
                  object.onload = onObjectLoad;
                  if (!browserDetector.isIE()) {
                    object.data = "about:blank";
                  }
                  if (!getState(element2)) {
                    return;
                  }
                  element2.appendChild(object);
                  getState(element2).object = object;
                  if (browserDetector.isIE()) {
                    object.data = "about:blank";
                  }
                }
                if (batchProcessor) {
                  batchProcessor.add(mutateDom);
                } else {
                  mutateDom();
                }
              }
              if (browserDetector.isIE(8)) {
                callback(element);
              } else {
                injectObject(element, callback);
              }
            }
            function getObject(element) {
              return getState(element).object;
            }
            function uninstall(element) {
              if (!getState(element)) {
                return;
              }
              var object = getObject(element);
              if (!object) {
                return;
              }
              if (browserDetector.isIE(8)) {
                element.detachEvent("onresize", object.proxy);
              } else {
                element.removeChild(object);
              }
              if (getState(element).checkForObjectDocumentTimeoutId) {
                window.clearTimeout(getState(element).checkForObjectDocumentTimeoutId);
              }
              delete getState(element).object;
            }
            return {
              makeDetectable,
              addListener,
              uninstall
            };
          };
        }
      ),
      /***/
      "18e9": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var detector = module2.exports = {};
          detector.isIE = function(version) {
            function isAnyIeVersion() {
              var agent = navigator.userAgent.toLowerCase();
              return agent.indexOf("msie") !== -1 || agent.indexOf("trident") !== -1 || agent.indexOf(" edge/") !== -1;
            }
            if (!isAnyIeVersion()) {
              return false;
            }
            if (!version) {
              return true;
            }
            var ieVersion = function() {
              var undef, v = 3, div = document.createElement("div"), all = div.getElementsByTagName("i");
              do {
                div.innerHTML = "<!--[if gt IE " + ++v + "]><i></i><![endif]-->";
              } while (all[0]);
              return v > 4 ? v : undef;
            }();
            return version === ieVersion;
          };
          detector.isLegacyOpera = function() {
            return !!window.opera;
          };
        }
      ),
      /***/
      "214f": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          __webpack_require__("b0c5");
          var redefine = __webpack_require__("2aba");
          var hide = __webpack_require__("32e9");
          var fails = __webpack_require__("79e5");
          var defined = __webpack_require__("be13");
          var wks = __webpack_require__("2b4c");
          var regexpExec = __webpack_require__("520a");
          var SPECIES = wks("species");
          var REPLACE_SUPPORTS_NAMED_GROUPS = !fails(function() {
            var re = /./;
            re.exec = function() {
              var result = [];
              result.groups = { a: "7" };
              return result;
            };
            return "".replace(re, "$<a>") !== "7";
          });
          var SPLIT_WORKS_WITH_OVERWRITTEN_EXEC = function() {
            var re = /(?:)/;
            var originalExec = re.exec;
            re.exec = function() {
              return originalExec.apply(this, arguments);
            };
            var result = "ab".split(re);
            return result.length === 2 && result[0] === "a" && result[1] === "b";
          }();
          module2.exports = function(KEY, length, exec) {
            var SYMBOL = wks(KEY);
            var DELEGATES_TO_SYMBOL = !fails(function() {
              var O = {};
              O[SYMBOL] = function() {
                return 7;
              };
              return ""[KEY](O) != 7;
            });
            var DELEGATES_TO_EXEC = DELEGATES_TO_SYMBOL ? !fails(function() {
              var execCalled = false;
              var re = /a/;
              re.exec = function() {
                execCalled = true;
                return null;
              };
              if (KEY === "split") {
                re.constructor = {};
                re.constructor[SPECIES] = function() {
                  return re;
                };
              }
              re[SYMBOL]("");
              return !execCalled;
            }) : void 0;
            if (!DELEGATES_TO_SYMBOL || !DELEGATES_TO_EXEC || KEY === "replace" && !REPLACE_SUPPORTS_NAMED_GROUPS || KEY === "split" && !SPLIT_WORKS_WITH_OVERWRITTEN_EXEC) {
              var nativeRegExpMethod = /./[SYMBOL];
              var fns = exec(
                defined,
                SYMBOL,
                ""[KEY],
                function maybeCallNative(nativeMethod, regexp, str, arg2, forceStringMethod) {
                  if (regexp.exec === regexpExec) {
                    if (DELEGATES_TO_SYMBOL && !forceStringMethod) {
                      return { done: true, value: nativeRegExpMethod.call(regexp, str, arg2) };
                    }
                    return { done: true, value: nativeMethod.call(str, regexp, arg2) };
                  }
                  return { done: false };
                }
              );
              var strfn = fns[0];
              var rxfn = fns[1];
              redefine(String.prototype, KEY, strfn);
              hide(
                RegExp.prototype,
                SYMBOL,
                length == 2 ? function(string, arg) {
                  return rxfn.call(string, this, arg);
                } : function(string) {
                  return rxfn.call(string, this);
                }
              );
            }
          };
        }
      ),
      /***/
      "230e": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var isObject = __webpack_require__("d3f4");
          var document2 = __webpack_require__("7726").document;
          var is = isObject(document2) && isObject(document2.createElement);
          module2.exports = function(it) {
            return is ? document2.createElement(it) : {};
          };
        }
      ),
      /***/
      "23c6": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var cof = __webpack_require__("2d95");
          var TAG = __webpack_require__("2b4c")("toStringTag");
          var ARG = cof(/* @__PURE__ */ function() {
            return arguments;
          }()) == "Arguments";
          var tryGet = function(it, key) {
            try {
              return it[key];
            } catch (e) {
            }
          };
          module2.exports = function(it) {
            var O, T, B;
            return it === void 0 ? "Undefined" : it === null ? "Null" : typeof (T = tryGet(O = Object(it), TAG)) == "string" ? T : ARG ? cof(O) : (B = cof(O)) == "Object" && typeof O.callee == "function" ? "Arguments" : B;
          };
        }
      ),
      /***/
      "24fb": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          module2.exports = function(useSourceMap) {
            var list = [];
            list.toString = function toString() {
              return this.map(function(item) {
                var content = cssWithMappingToString(item, useSourceMap);
                if (item[2]) {
                  return "@media ".concat(item[2], " {").concat(content, "}");
                }
                return content;
              }).join("");
            };
            list.i = function(modules, mediaQuery, dedupe) {
              if (typeof modules === "string") {
                modules = [[null, modules, ""]];
              }
              var alreadyImportedModules = {};
              if (dedupe) {
                for (var i = 0; i < this.length; i++) {
                  var id = this[i][0];
                  if (id != null) {
                    alreadyImportedModules[id] = true;
                  }
                }
              }
              for (var _i = 0; _i < modules.length; _i++) {
                var item = [].concat(modules[_i]);
                if (dedupe && alreadyImportedModules[item[0]]) {
                  continue;
                }
                if (mediaQuery) {
                  if (!item[2]) {
                    item[2] = mediaQuery;
                  } else {
                    item[2] = "".concat(mediaQuery, " and ").concat(item[2]);
                  }
                }
                list.push(item);
              }
            };
            return list;
          };
          function cssWithMappingToString(item, useSourceMap) {
            var content = item[1] || "";
            var cssMapping = item[3];
            if (!cssMapping) {
              return content;
            }
            if (useSourceMap && typeof btoa === "function") {
              var sourceMapping = toComment(cssMapping);
              var sourceURLs = cssMapping.sources.map(function(source) {
                return "/*# sourceURL=".concat(cssMapping.sourceRoot || "").concat(source, " */");
              });
              return [content].concat(sourceURLs).concat([sourceMapping]).join("\n");
            }
            return [content].join("\n");
          }
          function toComment(sourceMap) {
            var base64 = btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap))));
            var data = "sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(base64);
            return "/*# ".concat(data, " */");
          }
        }
      ),
      /***/
      "2621": (
        /***/
        function(module2, exports2) {
          exports2.f = Object.getOwnPropertySymbols;
        }
      ),
      /***/
      "2aba": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var global = __webpack_require__("7726");
          var hide = __webpack_require__("32e9");
          var has = __webpack_require__("69a8");
          var SRC = __webpack_require__("ca5a")("src");
          var $toString = __webpack_require__("fa5b");
          var TO_STRING = "toString";
          var TPL = ("" + $toString).split(TO_STRING);
          __webpack_require__("8378").inspectSource = function(it) {
            return $toString.call(it);
          };
          (module2.exports = function(O, key, val, safe) {
            var isFunction = typeof val == "function";
            if (isFunction) has(val, "name") || hide(val, "name", key);
            if (O[key] === val) return;
            if (isFunction) has(val, SRC) || hide(val, SRC, O[key] ? "" + O[key] : TPL.join(String(key)));
            if (O === global) {
              O[key] = val;
            } else if (!safe) {
              delete O[key];
              hide(O, key, val);
            } else if (O[key]) {
              O[key] = val;
            } else {
              hide(O, key, val);
            }
          })(Function.prototype, TO_STRING, function toString() {
            return typeof this == "function" && this[SRC] || $toString.call(this);
          });
        }
      ),
      /***/
      "2aeb": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var anObject = __webpack_require__("cb7c");
          var dPs = __webpack_require__("1495");
          var enumBugKeys = __webpack_require__("e11e");
          var IE_PROTO = __webpack_require__("613b")("IE_PROTO");
          var Empty = function() {
          };
          var PROTOTYPE = "prototype";
          var createDict = function() {
            var iframe = __webpack_require__("230e")("iframe");
            var i = enumBugKeys.length;
            var lt = "<";
            var gt = ">";
            var iframeDocument;
            iframe.style.display = "none";
            __webpack_require__("fab2").appendChild(iframe);
            iframe.src = "javascript:";
            iframeDocument = iframe.contentWindow.document;
            iframeDocument.open();
            iframeDocument.write(lt + "script" + gt + "document.F=Object" + lt + "/script" + gt);
            iframeDocument.close();
            createDict = iframeDocument.F;
            while (i--) delete createDict[PROTOTYPE][enumBugKeys[i]];
            return createDict();
          };
          module2.exports = Object.create || function create(O, Properties) {
            var result;
            if (O !== null) {
              Empty[PROTOTYPE] = anObject(O);
              result = new Empty();
              Empty[PROTOTYPE] = null;
              result[IE_PROTO] = O;
            } else result = createDict();
            return Properties === void 0 ? result : dPs(result, Properties);
          };
        }
      ),
      /***/
      "2b4c": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var store = __webpack_require__("5537")("wks");
          var uid = __webpack_require__("ca5a");
          var Symbol2 = __webpack_require__("7726").Symbol;
          var USE_SYMBOL = typeof Symbol2 == "function";
          var $exports = module2.exports = function(name) {
            return store[name] || (store[name] = USE_SYMBOL && Symbol2[name] || (USE_SYMBOL ? Symbol2 : uid)("Symbol." + name));
          };
          $exports.store = store;
        }
      ),
      /***/
      "2cef": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          module2.exports = function() {
            var idCount = 1;
            function generate() {
              return idCount++;
            }
            return {
              generate
            };
          };
        }
      ),
      /***/
      "2d00": (
        /***/
        function(module2, exports2) {
          module2.exports = false;
        }
      ),
      /***/
      "2d95": (
        /***/
        function(module2, exports2) {
          var toString = {}.toString;
          module2.exports = function(it) {
            return toString.call(it).slice(8, -1);
          };
        }
      ),
      /***/
      "2f21": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var fails = __webpack_require__("79e5");
          module2.exports = function(method, arg) {
            return !!method && fails(function() {
              arg ? method.call(null, function() {
              }, 1) : method.call(null);
            });
          };
        }
      ),
      /***/
      "32e9": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var dP = __webpack_require__("86cc");
          var createDesc = __webpack_require__("4630");
          module2.exports = __webpack_require__("9e1e") ? function(object, key, value) {
            return dP.f(object, key, createDesc(1, value));
          } : function(object, key, value) {
            object[key] = value;
            return object;
          };
        }
      ),
      /***/
      "38fd": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var has = __webpack_require__("69a8");
          var toObject = __webpack_require__("4bf8");
          var IE_PROTO = __webpack_require__("613b")("IE_PROTO");
          var ObjectProto = Object.prototype;
          module2.exports = Object.getPrototypeOf || function(O) {
            O = toObject(O);
            if (has(O, IE_PROTO)) return O[IE_PROTO];
            if (typeof O.constructor == "function" && O instanceof O.constructor) {
              return O.constructor.prototype;
            }
            return O instanceof Object ? ObjectProto : null;
          };
        }
      ),
      /***/
      "41a0": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var create = __webpack_require__("2aeb");
          var descriptor = __webpack_require__("4630");
          var setToStringTag = __webpack_require__("7f20");
          var IteratorPrototype = {};
          __webpack_require__("32e9")(IteratorPrototype, __webpack_require__("2b4c")("iterator"), function() {
            return this;
          });
          module2.exports = function(Constructor, NAME, next) {
            Constructor.prototype = create(IteratorPrototype, { next: descriptor(1, next) });
            setToStringTag(Constructor, NAME + " Iterator");
          };
        }
      ),
      /***/
      "456d": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var toObject = __webpack_require__("4bf8");
          var $keys = __webpack_require__("0d58");
          __webpack_require__("5eda")("keys", function() {
            return function keys(it) {
              return $keys(toObject(it));
            };
          });
        }
      ),
      /***/
      "4588": (
        /***/
        function(module2, exports2) {
          var ceil = Math.ceil;
          var floor = Math.floor;
          module2.exports = function(it) {
            return isNaN(it = +it) ? 0 : (it > 0 ? floor : ceil)(it);
          };
        }
      ),
      /***/
      "4630": (
        /***/
        function(module2, exports2) {
          module2.exports = function(bitmap, value) {
            return {
              enumerable: !(bitmap & 1),
              configurable: !(bitmap & 2),
              writable: !(bitmap & 4),
              value
            };
          };
        }
      ),
      /***/
      "4917": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var anObject = __webpack_require__("cb7c");
          var toLength = __webpack_require__("9def");
          var advanceStringIndex = __webpack_require__("0390");
          var regExpExec = __webpack_require__("5f1b");
          __webpack_require__("214f")("match", 1, function(defined, MATCH, $match, maybeCallNative) {
            return [
              // `String.prototype.match` method
              // https://tc39.github.io/ecma262/#sec-string.prototype.match
              function match(regexp) {
                var O = defined(this);
                var fn = regexp == void 0 ? void 0 : regexp[MATCH];
                return fn !== void 0 ? fn.call(regexp, O) : new RegExp(regexp)[MATCH](String(O));
              },
              // `RegExp.prototype[@@match]` method
              // https://tc39.github.io/ecma262/#sec-regexp.prototype-@@match
              function(regexp) {
                var res = maybeCallNative($match, regexp, this);
                if (res.done) return res.value;
                var rx = anObject(regexp);
                var S = String(this);
                if (!rx.global) return regExpExec(rx, S);
                var fullUnicode = rx.unicode;
                rx.lastIndex = 0;
                var A = [];
                var n = 0;
                var result;
                while ((result = regExpExec(rx, S)) !== null) {
                  var matchStr = String(result[0]);
                  A[n] = matchStr;
                  if (matchStr === "") rx.lastIndex = advanceStringIndex(S, toLength(rx.lastIndex), fullUnicode);
                  n++;
                }
                return n === 0 ? null : A;
              }
            ];
          });
        }
      ),
      /***/
      "493e": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var ___CSS_LOADER_API_IMPORT___ = __webpack_require__("24fb");
          exports2 = ___CSS_LOADER_API_IMPORT___(false);
          exports2.push([module2.i, '.vue-grid-item{-webkit-transition:all .2s ease;transition:all .2s ease;-webkit-transition-property:left,top,right;transition-property:left,top,right}.vue-grid-item,.vue-grid-item.no-touch{-ms-touch-action:none;touch-action:none}.vue-grid-item.cssTransforms{-webkit-transition-property:-webkit-transform;transition-property:-webkit-transform;transition-property:transform;transition-property:transform,-webkit-transform;left:0;right:auto}.vue-grid-item.cssTransforms.render-rtl{left:auto;right:0}.vue-grid-item.resizing{opacity:.6;z-index:3}.vue-grid-item.vue-draggable-dragging{-webkit-transition:none;transition:none;z-index:3}.vue-grid-item.vue-grid-placeholder{background:red;opacity:.2;-webkit-transition-duration:.1s;transition-duration:.1s;z-index:2;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;-o-user-select:none;user-select:none}.vue-grid-item>.vue-resizable-handle{position:absolute;width:20px;height:20px;bottom:0;right:0;background:url("data:image/svg+xml;base64,PHN2ZyBzdHlsZT0iYmFja2dyb3VuZC1jb2xvcjojZmZmZmZmMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjYiIGhlaWdodD0iNiI+PHBhdGggZD0iTTYgNkgwVjQuMmg0LjJWMEg2djZ6IiBvcGFjaXR5PSIuMzAyIi8+PC9zdmc+");background-position:100% 100%;padding:0 3px 3px 0;background-repeat:no-repeat;background-origin:content-box;-webkit-box-sizing:border-box;box-sizing:border-box;cursor:se-resize}.vue-grid-item>.vue-rtl-resizable-handle{bottom:0;left:0;background:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTS0xLTFoMTJ2MTJILTF6Ii8+PGc+PHBhdGggc3Ryb2tlLWxpbmVjYXA9InVuZGVmaW5lZCIgc3Ryb2tlLWxpbmVqb2luPSJ1bmRlZmluZWQiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2U9IiMwMDAiIGZpbGw9Im5vbmUiIGQ9Ik0xNDQuODIxLTM4LjM5M2wtMjAuMzU3LTMxLjc4NSIvPjxwYXRoIHN0cm9rZT0iIzY2NiIgc3Ryb2tlLWxpbmVjYXA9InVuZGVmaW5lZCIgc3Ryb2tlLWxpbmVqb2luPSJ1bmRlZmluZWQiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIgZD0iTS45NDctLjAxOHY5LjEyNU0tLjY1NiA5aDEwLjczIi8+PC9nPjwvc3ZnPg==);background-position:0 100%;padding-left:3px;background-repeat:no-repeat;background-origin:content-box;cursor:sw-resize;right:auto}.vue-grid-item.disable-userselect{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}', ""]);
          module2.exports = exports2;
        }
      ),
      /***/
      "499e": (
        /***/
        function(module2, __webpack_exports__, __webpack_require__) {
          "use strict";
          __webpack_require__.r(__webpack_exports__);
          __webpack_require__.d(__webpack_exports__, "default", function() {
            return (
              /* binding */
              addStylesClient
            );
          });
          function listToStyles(parentId, list) {
            var styles = [];
            var newStyles = {};
            for (var i = 0; i < list.length; i++) {
              var item = list[i];
              var id = item[0];
              var css = item[1];
              var media = item[2];
              var sourceMap = item[3];
              var part = {
                id: parentId + ":" + i,
                css,
                media,
                sourceMap
              };
              if (!newStyles[id]) {
                styles.push(newStyles[id] = { id, parts: [part] });
              } else {
                newStyles[id].parts.push(part);
              }
            }
            return styles;
          }
          var hasDocument = typeof document !== "undefined";
          if (typeof DEBUG !== "undefined" && DEBUG) {
            if (!hasDocument) {
              throw new Error(
                "vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment."
              );
            }
          }
          var stylesInDom = {
            /*
              [id: number]: {
                id: number,
                refs: number,
                parts: Array<(obj?: StyleObjectPart) => void>
              }
            */
          };
          var head = hasDocument && (document.head || document.getElementsByTagName("head")[0]);
          var singletonElement = null;
          var singletonCounter = 0;
          var isProduction = false;
          var noop = function() {
          };
          var options = null;
          var ssrIdKey = "data-vue-ssr-id";
          var isOldIE = typeof navigator !== "undefined" && /msie [6-9]\b/.test(navigator.userAgent.toLowerCase());
          function addStylesClient(parentId, list, _isProduction, _options) {
            isProduction = _isProduction;
            options = _options || {};
            var styles = listToStyles(parentId, list);
            addStylesToDom(styles);
            return function update(newList) {
              var mayRemove = [];
              for (var i = 0; i < styles.length; i++) {
                var item = styles[i];
                var domStyle = stylesInDom[item.id];
                domStyle.refs--;
                mayRemove.push(domStyle);
              }
              if (newList) {
                styles = listToStyles(parentId, newList);
                addStylesToDom(styles);
              } else {
                styles = [];
              }
              for (var i = 0; i < mayRemove.length; i++) {
                var domStyle = mayRemove[i];
                if (domStyle.refs === 0) {
                  for (var j = 0; j < domStyle.parts.length; j++) {
                    domStyle.parts[j]();
                  }
                  delete stylesInDom[domStyle.id];
                }
              }
            };
          }
          function addStylesToDom(styles) {
            for (var i = 0; i < styles.length; i++) {
              var item = styles[i];
              var domStyle = stylesInDom[item.id];
              if (domStyle) {
                domStyle.refs++;
                for (var j = 0; j < domStyle.parts.length; j++) {
                  domStyle.parts[j](item.parts[j]);
                }
                for (; j < item.parts.length; j++) {
                  domStyle.parts.push(addStyle(item.parts[j]));
                }
                if (domStyle.parts.length > item.parts.length) {
                  domStyle.parts.length = item.parts.length;
                }
              } else {
                var parts = [];
                for (var j = 0; j < item.parts.length; j++) {
                  parts.push(addStyle(item.parts[j]));
                }
                stylesInDom[item.id] = { id: item.id, refs: 1, parts };
              }
            }
          }
          function createStyleElement() {
            var styleElement = document.createElement("style");
            styleElement.type = "text/css";
            head.appendChild(styleElement);
            return styleElement;
          }
          function addStyle(obj) {
            var update, remove;
            var styleElement = document.querySelector("style[" + ssrIdKey + '~="' + obj.id + '"]');
            if (styleElement) {
              if (isProduction) {
                return noop;
              } else {
                styleElement.parentNode.removeChild(styleElement);
              }
            }
            if (isOldIE) {
              var styleIndex = singletonCounter++;
              styleElement = singletonElement || (singletonElement = createStyleElement());
              update = applyToSingletonTag.bind(null, styleElement, styleIndex, false);
              remove = applyToSingletonTag.bind(null, styleElement, styleIndex, true);
            } else {
              styleElement = createStyleElement();
              update = applyToTag.bind(null, styleElement);
              remove = function() {
                styleElement.parentNode.removeChild(styleElement);
              };
            }
            update(obj);
            return function updateStyle(newObj) {
              if (newObj) {
                if (newObj.css === obj.css && newObj.media === obj.media && newObj.sourceMap === obj.sourceMap) {
                  return;
                }
                update(obj = newObj);
              } else {
                remove();
              }
            };
          }
          var replaceText = /* @__PURE__ */ function() {
            var textStore = [];
            return function(index, replacement) {
              textStore[index] = replacement;
              return textStore.filter(Boolean).join("\n");
            };
          }();
          function applyToSingletonTag(styleElement, index, remove, obj) {
            var css = remove ? "" : obj.css;
            if (styleElement.styleSheet) {
              styleElement.styleSheet.cssText = replaceText(index, css);
            } else {
              var cssNode = document.createTextNode(css);
              var childNodes = styleElement.childNodes;
              if (childNodes[index]) styleElement.removeChild(childNodes[index]);
              if (childNodes.length) {
                styleElement.insertBefore(cssNode, childNodes[index]);
              } else {
                styleElement.appendChild(cssNode);
              }
            }
          }
          function applyToTag(styleElement, obj) {
            var css = obj.css;
            var media = obj.media;
            var sourceMap = obj.sourceMap;
            if (media) {
              styleElement.setAttribute("media", media);
            }
            if (options.ssrId) {
              styleElement.setAttribute(ssrIdKey, obj.id);
            }
            if (sourceMap) {
              css += "\n/*# sourceURL=" + sourceMap.sources[0] + " */";
              css += "\n/*# sourceMappingURL=data:application/json;base64," + btoa(unescape(encodeURIComponent(JSON.stringify(sourceMap)))) + " */";
            }
            if (styleElement.styleSheet) {
              styleElement.styleSheet.cssText = css;
            } else {
              while (styleElement.firstChild) {
                styleElement.removeChild(styleElement.firstChild);
              }
              styleElement.appendChild(document.createTextNode(css));
            }
          }
        }
      ),
      /***/
      "49ad": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          module2.exports = function(idHandler) {
            var eventListeners = {};
            function getListeners(element) {
              var id = idHandler.get(element);
              if (id === void 0) {
                return [];
              }
              return eventListeners[id] || [];
            }
            function addListener(element, listener) {
              var id = idHandler.get(element);
              if (!eventListeners[id]) {
                eventListeners[id] = [];
              }
              eventListeners[id].push(listener);
            }
            function removeListener(element, listener) {
              var listeners = getListeners(element);
              for (var i = 0, len = listeners.length; i < len; ++i) {
                if (listeners[i] === listener) {
                  listeners.splice(i, 1);
                  break;
                }
              }
            }
            function removeAllListeners(element) {
              var listeners = getListeners(element);
              if (!listeners) {
                return;
              }
              listeners.length = 0;
            }
            return {
              get: getListeners,
              add: addListener,
              removeListener,
              removeAllListeners
            };
          };
        }
      ),
      /***/
      "4bf8": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var defined = __webpack_require__("be13");
          module2.exports = function(it) {
            return Object(defined(it));
          };
        }
      ),
      /***/
      "5058": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          module2.exports = function(options) {
            var idGenerator = options.idGenerator;
            var getState = options.stateHandler.getState;
            function getId(element) {
              var state = getState(element);
              if (state && state.id !== void 0) {
                return state.id;
              }
              return null;
            }
            function setId(element) {
              var state = getState(element);
              if (!state) {
                throw new Error("setId required the element to have a resize detection state.");
              }
              var id = idGenerator.generate();
              state.id = id;
              return id;
            }
            return {
              get: getId,
              set: setId
            };
          };
        }
      ),
      /***/
      "50bf": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var utils = module2.exports = {};
          utils.getOption = getOption;
          function getOption(options, name, defaultValue) {
            var value = options[name];
            if ((value === void 0 || value === null) && defaultValue !== void 0) {
              return defaultValue;
            }
            return value;
          }
        }
      ),
      /***/
      "520a": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var regexpFlags = __webpack_require__("0bfb");
          var nativeExec = RegExp.prototype.exec;
          var nativeReplace = String.prototype.replace;
          var patchedExec = nativeExec;
          var LAST_INDEX = "lastIndex";
          var UPDATES_LAST_INDEX_WRONG = function() {
            var re1 = /a/, re2 = /b*/g;
            nativeExec.call(re1, "a");
            nativeExec.call(re2, "a");
            return re1[LAST_INDEX] !== 0 || re2[LAST_INDEX] !== 0;
          }();
          var NPCG_INCLUDED = /()??/.exec("")[1] !== void 0;
          var PATCH = UPDATES_LAST_INDEX_WRONG || NPCG_INCLUDED;
          if (PATCH) {
            patchedExec = function exec(str) {
              var re = this;
              var lastIndex, reCopy, match, i;
              if (NPCG_INCLUDED) {
                reCopy = new RegExp("^" + re.source + "$(?!\\s)", regexpFlags.call(re));
              }
              if (UPDATES_LAST_INDEX_WRONG) lastIndex = re[LAST_INDEX];
              match = nativeExec.call(re, str);
              if (UPDATES_LAST_INDEX_WRONG && match) {
                re[LAST_INDEX] = re.global ? match.index + match[0].length : lastIndex;
              }
              if (NPCG_INCLUDED && match && match.length > 1) {
                nativeReplace.call(match[0], reCopy, function() {
                  for (i = 1; i < arguments.length - 2; i++) {
                    if (arguments[i] === void 0) match[i] = void 0;
                  }
                });
              }
              return match;
            };
          }
          module2.exports = patchedExec;
        }
      ),
      /***/
      "52a7": (
        /***/
        function(module2, exports2) {
          exports2.f = {}.propertyIsEnumerable;
        }
      ),
      /***/
      "5537": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var core = __webpack_require__("8378");
          var global = __webpack_require__("7726");
          var SHARED = "__core-js_shared__";
          var store = global[SHARED] || (global[SHARED] = {});
          (module2.exports = function(key, value) {
            return store[key] || (store[key] = value !== void 0 ? value : {});
          })("versions", []).push({
            version: core.version,
            mode: __webpack_require__("2d00") ? "pure" : "global",
            copyright: "© 2020 Denis Pushkarev (zloirock.ru)"
          });
        }
      ),
      /***/
      "55dd": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var $export = __webpack_require__("5ca1");
          var aFunction = __webpack_require__("d8e8");
          var toObject = __webpack_require__("4bf8");
          var fails = __webpack_require__("79e5");
          var $sort = [].sort;
          var test = [1, 2, 3];
          $export($export.P + $export.F * (fails(function() {
            test.sort(void 0);
          }) || !fails(function() {
            test.sort(null);
          }) || !__webpack_require__("2f21")($sort)), "Array", {
            // 22.1.3.25 Array.prototype.sort(comparefn)
            sort: function sort(comparefn) {
              return comparefn === void 0 ? $sort.call(toObject(this)) : $sort.call(toObject(this), aFunction(comparefn));
            }
          });
        }
      ),
      /***/
      "5be5": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          module2.exports = function(options) {
            var getState = options.stateHandler.getState;
            function isDetectable(element) {
              var state = getState(element);
              return state && !!state.isDetectable;
            }
            function markAsDetectable(element) {
              getState(element).isDetectable = true;
            }
            function isBusy(element) {
              return !!getState(element).busy;
            }
            function markBusy(element, busy) {
              getState(element).busy = !!busy;
            }
            return {
              isDetectable,
              markAsDetectable,
              isBusy,
              markBusy
            };
          };
        }
      ),
      /***/
      "5ca1": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var global = __webpack_require__("7726");
          var core = __webpack_require__("8378");
          var hide = __webpack_require__("32e9");
          var redefine = __webpack_require__("2aba");
          var ctx = __webpack_require__("9b43");
          var PROTOTYPE = "prototype";
          var $export = function(type, name, source) {
            var IS_FORCED = type & $export.F;
            var IS_GLOBAL = type & $export.G;
            var IS_STATIC = type & $export.S;
            var IS_PROTO = type & $export.P;
            var IS_BIND = type & $export.B;
            var target = IS_GLOBAL ? global : IS_STATIC ? global[name] || (global[name] = {}) : (global[name] || {})[PROTOTYPE];
            var exports3 = IS_GLOBAL ? core : core[name] || (core[name] = {});
            var expProto = exports3[PROTOTYPE] || (exports3[PROTOTYPE] = {});
            var key, own, out, exp;
            if (IS_GLOBAL) source = name;
            for (key in source) {
              own = !IS_FORCED && target && target[key] !== void 0;
              out = (own ? target : source)[key];
              exp = IS_BIND && own ? ctx(out, global) : IS_PROTO && typeof out == "function" ? ctx(Function.call, out) : out;
              if (target) redefine(target, key, out, type & $export.U);
              if (exports3[key] != out) hide(exports3, key, exp);
              if (IS_PROTO && expProto[key] != out) expProto[key] = out;
            }
          };
          global.core = core;
          $export.F = 1;
          $export.G = 2;
          $export.S = 4;
          $export.P = 8;
          $export.B = 16;
          $export.W = 32;
          $export.U = 64;
          $export.R = 128;
          module2.exports = $export;
        }
      ),
      /***/
      "5dbc": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var isObject = __webpack_require__("d3f4");
          var setPrototypeOf = __webpack_require__("8b97").set;
          module2.exports = function(that, target, C) {
            var S = target.constructor;
            var P;
            if (S !== C && typeof S == "function" && (P = S.prototype) !== C.prototype && isObject(P) && setPrototypeOf) {
              setPrototypeOf(that, P);
            }
            return that;
          };
        }
      ),
      /***/
      "5eda": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var $export = __webpack_require__("5ca1");
          var core = __webpack_require__("8378");
          var fails = __webpack_require__("79e5");
          module2.exports = function(KEY, exec) {
            var fn = (core.Object || {})[KEY] || Object[KEY];
            var exp = {};
            exp[KEY] = exec(fn);
            $export($export.S + $export.F * fails(function() {
              fn(1);
            }), "Object", exp);
          };
        }
      ),
      /***/
      "5f1b": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var classof = __webpack_require__("23c6");
          var builtinExec = RegExp.prototype.exec;
          module2.exports = function(R, S) {
            var exec = R.exec;
            if (typeof exec === "function") {
              var result = exec.call(R, S);
              if (typeof result !== "object") {
                throw new TypeError("RegExp exec method returned something other than an Object or null");
              }
              return result;
            }
            if (classof(R) !== "RegExp") {
              throw new TypeError("RegExp#exec called on incompatible receiver");
            }
            return builtinExec.call(R, S);
          };
        }
      ),
      /***/
      "613b": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var shared = __webpack_require__("5537")("keys");
          var uid = __webpack_require__("ca5a");
          module2.exports = function(key) {
            return shared[key] || (shared[key] = uid(key));
          };
        }
      ),
      /***/
      "626a": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var cof = __webpack_require__("2d95");
          module2.exports = Object("z").propertyIsEnumerable(0) ? Object : function(it) {
            return cof(it) == "String" ? it.split("") : Object(it);
          };
        }
      ),
      /***/
      "6521": (
        /***/
        function(module2, __webpack_exports__, __webpack_require__) {
          "use strict";
          var _node_modules_vue_style_loader_index_js_ref_6_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_node_modules_vue_loader_v16_dist_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_v16_dist_index_js_ref_0_1_GridLayout_vue_vue_type_style_index_0_id_fc5948f6_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__("92bf");
          var _node_modules_vue_style_loader_index_js_ref_6_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_node_modules_vue_loader_v16_dist_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_v16_dist_index_js_ref_0_1_GridLayout_vue_vue_type_style_index_0_id_fc5948f6_lang_css__WEBPACK_IMPORTED_MODULE_0___default = __webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_6_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_6_oneOf_1_1_node_modules_vue_loader_v16_dist_stylePostLoader_js_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_2_node_modules_postcss_loader_src_index_js_ref_6_oneOf_1_3_node_modules_cache_loader_dist_cjs_js_ref_0_0_node_modules_vue_loader_v16_dist_index_js_ref_0_1_GridLayout_vue_vue_type_style_index_0_id_fc5948f6_lang_css__WEBPACK_IMPORTED_MODULE_0__);
        }
      ),
      /***/
      "6821": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var IObject = __webpack_require__("626a");
          var defined = __webpack_require__("be13");
          module2.exports = function(it) {
            return IObject(defined(it));
          };
        }
      ),
      /***/
      "69a8": (
        /***/
        function(module2, exports2) {
          var hasOwnProperty = {}.hasOwnProperty;
          module2.exports = function(it, key) {
            return hasOwnProperty.call(it, key);
          };
        }
      ),
      /***/
      "6a99": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var isObject = __webpack_require__("d3f4");
          module2.exports = function(it, S) {
            if (!isObject(it)) return it;
            var fn, val;
            if (S && typeof (fn = it.toString) == "function" && !isObject(val = fn.call(it))) return val;
            if (typeof (fn = it.valueOf) == "function" && !isObject(val = fn.call(it))) return val;
            if (!S && typeof (fn = it.toString) == "function" && !isObject(val = fn.call(it))) return val;
            throw TypeError("Can't convert object to primitive value");
          };
        }
      ),
      /***/
      "7333": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var DESCRIPTORS = __webpack_require__("9e1e");
          var getKeys = __webpack_require__("0d58");
          var gOPS = __webpack_require__("2621");
          var pIE = __webpack_require__("52a7");
          var toObject = __webpack_require__("4bf8");
          var IObject = __webpack_require__("626a");
          var $assign = Object.assign;
          module2.exports = !$assign || __webpack_require__("79e5")(function() {
            var A = {};
            var B = {};
            var S = Symbol();
            var K = "abcdefghijklmnopqrst";
            A[S] = 7;
            K.split("").forEach(function(k) {
              B[k] = k;
            });
            return $assign({}, A)[S] != 7 || Object.keys($assign({}, B)).join("") != K;
          }) ? function assign(target, source) {
            var T = toObject(target);
            var aLen = arguments.length;
            var index = 1;
            var getSymbols = gOPS.f;
            var isEnum = pIE.f;
            while (aLen > index) {
              var S = IObject(arguments[index++]);
              var keys = getSymbols ? getKeys(S).concat(getSymbols(S)) : getKeys(S);
              var length = keys.length;
              var j = 0;
              var key;
              while (length > j) {
                key = keys[j++];
                if (!DESCRIPTORS || isEnum.call(S, key)) T[key] = S[key];
              }
            }
            return T;
          } : $assign;
        }
      ),
      /***/
      "7726": (
        /***/
        function(module2, exports2) {
          var global = module2.exports = typeof window != "undefined" && window.Math == Math ? window : typeof self != "undefined" && self.Math == Math ? self : Function("return this")();
          if (typeof __g == "number") __g = global;
        }
      ),
      /***/
      "77f1": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var toInteger = __webpack_require__("4588");
          var max = Math.max;
          var min = Math.min;
          module2.exports = function(index, length) {
            index = toInteger(index);
            return index < 0 ? max(index + length, 0) : min(index, length);
          };
        }
      ),
      /***/
      "79e5": (
        /***/
        function(module2, exports2) {
          module2.exports = function(exec) {
            try {
              return !!exec();
            } catch (e) {
              return true;
            }
          };
        }
      ),
      /***/
      "7f20": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var def = __webpack_require__("86cc").f;
          var has = __webpack_require__("69a8");
          var TAG = __webpack_require__("2b4c")("toStringTag");
          module2.exports = function(it, tag, stat) {
            if (it && !has(it = stat ? it : it.prototype, TAG)) def(it, TAG, { configurable: true, value: tag });
          };
        }
      ),
      /***/
      "8378": (
        /***/
        function(module2, exports2) {
          var core = module2.exports = { version: "2.6.12" };
          if (typeof __e == "number") __e = core;
        }
      ),
      /***/
      "848e": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var ___CSS_LOADER_API_IMPORT___ = __webpack_require__("24fb");
          exports2 = ___CSS_LOADER_API_IMPORT___(false);
          exports2.push([module2.i, ".vue-grid-layout{position:relative;-webkit-transition:height .2s ease;transition:height .2s ease}", ""]);
          module2.exports = exports2;
        }
      ),
      /***/
      "84f2": (
        /***/
        function(module2, exports2) {
          module2.exports = {};
        }
      ),
      /***/
      "86cc": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var anObject = __webpack_require__("cb7c");
          var IE8_DOM_DEFINE = __webpack_require__("c69a");
          var toPrimitive = __webpack_require__("6a99");
          var dP = Object.defineProperty;
          exports2.f = __webpack_require__("9e1e") ? Object.defineProperty : function defineProperty(O, P, Attributes) {
            anObject(O);
            P = toPrimitive(P, true);
            anObject(Attributes);
            if (IE8_DOM_DEFINE) try {
              return dP(O, P, Attributes);
            } catch (e) {
            }
            if ("get" in Attributes || "set" in Attributes) throw TypeError("Accessors not supported!");
            if ("value" in Attributes) O[P] = Attributes.value;
            return O;
          };
        }
      ),
      /***/
      "8875": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;
          (function(root, factory) {
            if (true) {
              !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_FACTORY__ = factory, __WEBPACK_AMD_DEFINE_RESULT__ = typeof __WEBPACK_AMD_DEFINE_FACTORY__ === "function" ? __WEBPACK_AMD_DEFINE_FACTORY__.apply(exports2, __WEBPACK_AMD_DEFINE_ARRAY__) : __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_RESULT__ !== void 0 && (module2.exports = __WEBPACK_AMD_DEFINE_RESULT__));
            } else {
            }
          })(typeof self !== "undefined" ? self : this, function() {
            function getCurrentScript() {
              var descriptor = Object.getOwnPropertyDescriptor(document, "currentScript");
              if (!descriptor && "currentScript" in document && document.currentScript) {
                return document.currentScript;
              }
              if (descriptor && descriptor.get !== getCurrentScript && document.currentScript) {
                return document.currentScript;
              }
              try {
                throw new Error();
              } catch (err) {
                var ieStackRegExp = /.*at [^(]*\((.*):(.+):(.+)\)$/ig, ffStackRegExp = /@([^@]*):(\d+):(\d+)\s*$/ig, stackDetails = ieStackRegExp.exec(err.stack) || ffStackRegExp.exec(err.stack), scriptLocation = stackDetails && stackDetails[1] || false, line = stackDetails && stackDetails[2] || false, currentLocation = document.location.href.replace(document.location.hash, ""), pageSource, inlineScriptSourceRegExp, inlineScriptSource, scripts = document.getElementsByTagName("script");
                if (scriptLocation === currentLocation) {
                  pageSource = document.documentElement.outerHTML;
                  inlineScriptSourceRegExp = new RegExp("(?:[^\\n]+?\\n){0," + (line - 2) + "}[^<]*<script>([\\d\\D]*?)<\\/script>[\\d\\D]*", "i");
                  inlineScriptSource = pageSource.replace(inlineScriptSourceRegExp, "$1").trim();
                }
                for (var i = 0; i < scripts.length; i++) {
                  if (scripts[i].readyState === "interactive") {
                    return scripts[i];
                  }
                  if (scripts[i].src === scriptLocation) {
                    return scripts[i];
                  }
                  if (scriptLocation === currentLocation && scripts[i].innerHTML && scripts[i].innerHTML.trim() === inlineScriptSource) {
                    return scripts[i];
                  }
                }
                return null;
              }
            }
            ;
            return getCurrentScript;
          });
        }
      ),
      /***/
      "8b97": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var isObject = __webpack_require__("d3f4");
          var anObject = __webpack_require__("cb7c");
          var check = function(O, proto) {
            anObject(O);
            if (!isObject(proto) && proto !== null) throw TypeError(proto + ": can't set as prototype!");
          };
          module2.exports = {
            set: Object.setPrototypeOf || ("__proto__" in {} ? (
              // eslint-disable-line
              function(test, buggy, set) {
                try {
                  set = __webpack_require__("9b43")(Function.call, __webpack_require__("11e9").f(Object.prototype, "__proto__").set, 2);
                  set(test, []);
                  buggy = !(test instanceof Array);
                } catch (e) {
                  buggy = true;
                }
                return function setPrototypeOf(O, proto) {
                  check(O, proto);
                  if (buggy) O.__proto__ = proto;
                  else set(O, proto);
                  return O;
                };
              }({}, false)
            ) : void 0),
            check
          };
        }
      ),
      /***/
      "8bbf": (
        /***/
        function(module2, exports2) {
          module2.exports = require_vue();
        }
      ),
      /***/
      "8e6e": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var $export = __webpack_require__("5ca1");
          var ownKeys = __webpack_require__("990b");
          var toIObject = __webpack_require__("6821");
          var gOPD = __webpack_require__("11e9");
          var createProperty = __webpack_require__("f1ae");
          $export($export.S, "Object", {
            getOwnPropertyDescriptors: function getOwnPropertyDescriptors(object) {
              var O = toIObject(object);
              var getDesc = gOPD.f;
              var keys = ownKeys(O);
              var result = {};
              var i = 0;
              var key, desc;
              while (keys.length > i) {
                desc = getDesc(O, key = keys[i++]);
                if (desc !== void 0) createProperty(result, key, desc);
              }
              return result;
            }
          });
        }
      ),
      /***/
      "9093": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var $keys = __webpack_require__("ce10");
          var hiddenKeys = __webpack_require__("e11e").concat("length", "prototype");
          exports2.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {
            return $keys(O, hiddenKeys);
          };
        }
      ),
      /***/
      "92bf": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var content = __webpack_require__("848e");
          if (typeof content === "string") content = [[module2.i, content, ""]];
          if (content.locals) module2.exports = content.locals;
          var add = __webpack_require__("499e").default;
          var update = add("ff1827d0", content, true, { "sourceMap": false, "shadowMode": false });
        }
      ),
      /***/
      "990b": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var gOPN = __webpack_require__("9093");
          var gOPS = __webpack_require__("2621");
          var anObject = __webpack_require__("cb7c");
          var Reflect = __webpack_require__("7726").Reflect;
          module2.exports = Reflect && Reflect.ownKeys || function ownKeys(it) {
            var keys = gOPN.f(anObject(it));
            var getSymbols = gOPS.f;
            return getSymbols ? keys.concat(getSymbols(it)) : keys;
          };
        }
      ),
      /***/
      "9b43": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var aFunction = __webpack_require__("d8e8");
          module2.exports = function(fn, that, length) {
            aFunction(fn);
            if (that === void 0) return fn;
            switch (length) {
              case 1:
                return function(a) {
                  return fn.call(that, a);
                };
              case 2:
                return function(a, b) {
                  return fn.call(that, a, b);
                };
              case 3:
                return function(a, b, c) {
                  return fn.call(that, a, b, c);
                };
            }
            return function() {
              return fn.apply(that, arguments);
            };
          };
        }
      ),
      /***/
      "9c6c": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var UNSCOPABLES = __webpack_require__("2b4c")("unscopables");
          var ArrayProto = Array.prototype;
          if (ArrayProto[UNSCOPABLES] == void 0) __webpack_require__("32e9")(ArrayProto, UNSCOPABLES, {});
          module2.exports = function(key) {
            ArrayProto[UNSCOPABLES][key] = true;
          };
        }
      ),
      /***/
      "9def": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var toInteger = __webpack_require__("4588");
          var min = Math.min;
          module2.exports = function(it) {
            return it > 0 ? min(toInteger(it), 9007199254740991) : 0;
          };
        }
      ),
      /***/
      "9e1e": (
        /***/
        function(module2, exports2, __webpack_require__) {
          module2.exports = !__webpack_require__("79e5")(function() {
            return Object.defineProperty({}, "a", { get: function() {
              return 7;
            } }).a != 7;
          });
        }
      ),
      /***/
      "a481": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var anObject = __webpack_require__("cb7c");
          var toObject = __webpack_require__("4bf8");
          var toLength = __webpack_require__("9def");
          var toInteger = __webpack_require__("4588");
          var advanceStringIndex = __webpack_require__("0390");
          var regExpExec = __webpack_require__("5f1b");
          var max = Math.max;
          var min = Math.min;
          var floor = Math.floor;
          var SUBSTITUTION_SYMBOLS = /\$([$&`']|\d\d?|<[^>]*>)/g;
          var SUBSTITUTION_SYMBOLS_NO_NAMED = /\$([$&`']|\d\d?)/g;
          var maybeToString = function(it) {
            return it === void 0 ? it : String(it);
          };
          __webpack_require__("214f")("replace", 2, function(defined, REPLACE, $replace, maybeCallNative) {
            return [
              // `String.prototype.replace` method
              // https://tc39.github.io/ecma262/#sec-string.prototype.replace
              function replace(searchValue, replaceValue) {
                var O = defined(this);
                var fn = searchValue == void 0 ? void 0 : searchValue[REPLACE];
                return fn !== void 0 ? fn.call(searchValue, O, replaceValue) : $replace.call(String(O), searchValue, replaceValue);
              },
              // `RegExp.prototype[@@replace]` method
              // https://tc39.github.io/ecma262/#sec-regexp.prototype-@@replace
              function(regexp, replaceValue) {
                var res = maybeCallNative($replace, regexp, this, replaceValue);
                if (res.done) return res.value;
                var rx = anObject(regexp);
                var S = String(this);
                var functionalReplace = typeof replaceValue === "function";
                if (!functionalReplace) replaceValue = String(replaceValue);
                var global = rx.global;
                if (global) {
                  var fullUnicode = rx.unicode;
                  rx.lastIndex = 0;
                }
                var results = [];
                while (true) {
                  var result = regExpExec(rx, S);
                  if (result === null) break;
                  results.push(result);
                  if (!global) break;
                  var matchStr = String(result[0]);
                  if (matchStr === "") rx.lastIndex = advanceStringIndex(S, toLength(rx.lastIndex), fullUnicode);
                }
                var accumulatedResult = "";
                var nextSourcePosition = 0;
                for (var i = 0; i < results.length; i++) {
                  result = results[i];
                  var matched = String(result[0]);
                  var position = max(min(toInteger(result.index), S.length), 0);
                  var captures = [];
                  for (var j = 1; j < result.length; j++) captures.push(maybeToString(result[j]));
                  var namedCaptures = result.groups;
                  if (functionalReplace) {
                    var replacerArgs = [matched].concat(captures, position, S);
                    if (namedCaptures !== void 0) replacerArgs.push(namedCaptures);
                    var replacement = String(replaceValue.apply(void 0, replacerArgs));
                  } else {
                    replacement = getSubstitution(matched, S, position, captures, namedCaptures, replaceValue);
                  }
                  if (position >= nextSourcePosition) {
                    accumulatedResult += S.slice(nextSourcePosition, position) + replacement;
                    nextSourcePosition = position + matched.length;
                  }
                }
                return accumulatedResult + S.slice(nextSourcePosition);
              }
            ];
            function getSubstitution(matched, str, position, captures, namedCaptures, replacement) {
              var tailPos = position + matched.length;
              var m = captures.length;
              var symbols = SUBSTITUTION_SYMBOLS_NO_NAMED;
              if (namedCaptures !== void 0) {
                namedCaptures = toObject(namedCaptures);
                symbols = SUBSTITUTION_SYMBOLS;
              }
              return $replace.call(replacement, symbols, function(match, ch) {
                var capture;
                switch (ch.charAt(0)) {
                  case "$":
                    return "$";
                  case "&":
                    return matched;
                  case "`":
                    return str.slice(0, position);
                  case "'":
                    return str.slice(tailPos);
                  case "<":
                    capture = namedCaptures[ch.slice(1, -1)];
                    break;
                  default:
                    var n = +ch;
                    if (n === 0) return match;
                    if (n > m) {
                      var f = floor(n / 10);
                      if (f === 0) return match;
                      if (f <= m) return captures[f - 1] === void 0 ? ch.charAt(1) : captures[f - 1] + ch.charAt(1);
                      return match;
                    }
                    capture = captures[n - 1];
                }
                return capture === void 0 ? "" : capture;
              });
            }
          });
        }
      ),
      /***/
      "aa77": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var $export = __webpack_require__("5ca1");
          var defined = __webpack_require__("be13");
          var fails = __webpack_require__("79e5");
          var spaces = __webpack_require__("fdef");
          var space = "[" + spaces + "]";
          var non = "​";
          var ltrim = RegExp("^" + space + space + "*");
          var rtrim = RegExp(space + space + "*$");
          var exporter = function(KEY, exec, ALIAS) {
            var exp = {};
            var FORCE = fails(function() {
              return !!spaces[KEY]() || non[KEY]() != non;
            });
            var fn = exp[KEY] = FORCE ? exec(trim) : spaces[KEY];
            if (ALIAS) exp[ALIAS] = fn;
            $export($export.P + $export.F * FORCE, "String", exp);
          };
          var trim = exporter.trim = function(string, TYPE) {
            string = String(defined(string));
            if (TYPE & 1) string = string.replace(ltrim, "");
            if (TYPE & 2) string = string.replace(rtrim, "");
            return string;
          };
          module2.exports = exporter;
        }
      ),
      /***/
      "abb4": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          module2.exports = function(quiet) {
            function noop() {
            }
            var reporter = {
              log: noop,
              warn: noop,
              error: noop
            };
            if (!quiet && window.console) {
              var attachFunction = function(reporter2, name) {
                reporter2[name] = function reporterProxy() {
                  var f = console[name];
                  if (f.apply) {
                    f.apply(console, arguments);
                  } else {
                    for (var i = 0; i < arguments.length; i++) {
                      f(arguments[i]);
                    }
                  }
                };
              };
              attachFunction(reporter, "log");
              attachFunction(reporter, "warn");
              attachFunction(reporter, "error");
            }
            return reporter;
          };
        }
      ),
      /***/
      "ac6a": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var $iterators = __webpack_require__("cadf");
          var getKeys = __webpack_require__("0d58");
          var redefine = __webpack_require__("2aba");
          var global = __webpack_require__("7726");
          var hide = __webpack_require__("32e9");
          var Iterators = __webpack_require__("84f2");
          var wks = __webpack_require__("2b4c");
          var ITERATOR = wks("iterator");
          var TO_STRING_TAG = wks("toStringTag");
          var ArrayValues = Iterators.Array;
          var DOMIterables = {
            CSSRuleList: true,
            // TODO: Not spec compliant, should be false.
            CSSStyleDeclaration: false,
            CSSValueList: false,
            ClientRectList: false,
            DOMRectList: false,
            DOMStringList: false,
            DOMTokenList: true,
            DataTransferItemList: false,
            FileList: false,
            HTMLAllCollection: false,
            HTMLCollection: false,
            HTMLFormElement: false,
            HTMLSelectElement: false,
            MediaList: true,
            // TODO: Not spec compliant, should be false.
            MimeTypeArray: false,
            NamedNodeMap: false,
            NodeList: true,
            PaintRequestList: false,
            Plugin: false,
            PluginArray: false,
            SVGLengthList: false,
            SVGNumberList: false,
            SVGPathSegList: false,
            SVGPointList: false,
            SVGStringList: false,
            SVGTransformList: false,
            SourceBufferList: false,
            StyleSheetList: true,
            // TODO: Not spec compliant, should be false.
            TextTrackCueList: false,
            TextTrackList: false,
            TouchList: false
          };
          for (var collections = getKeys(DOMIterables), i = 0; i < collections.length; i++) {
            var NAME = collections[i];
            var explicit = DOMIterables[NAME];
            var Collection = global[NAME];
            var proto = Collection && Collection.prototype;
            var key;
            if (proto) {
              if (!proto[ITERATOR]) hide(proto, ITERATOR, ArrayValues);
              if (!proto[TO_STRING_TAG]) hide(proto, TO_STRING_TAG, NAME);
              Iterators[NAME] = ArrayValues;
              if (explicit) {
                for (key in $iterators) if (!proto[key]) redefine(proto, key, $iterators[key], true);
              }
            }
          }
        }
      ),
      /***/
      "b0c5": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var regexpExec = __webpack_require__("520a");
          __webpack_require__("5ca1")({
            target: "RegExp",
            proto: true,
            forced: regexpExec !== /./.exec
          }, {
            exec: regexpExec
          });
        }
      ),
      /***/
      "b770": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var utils = module2.exports = {};
          utils.forEach = function(collection, callback) {
            for (var i = 0; i < collection.length; i++) {
              var result = callback(collection[i]);
              if (result) {
                return result;
              }
            }
          };
        }
      ),
      /***/
      "be13": (
        /***/
        function(module2, exports2) {
          module2.exports = function(it) {
            if (it == void 0) throw TypeError("Can't call method on  " + it);
            return it;
          };
        }
      ),
      /***/
      "c274": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var utils = __webpack_require__("50bf");
          module2.exports = function batchProcessorMaker(options) {
            options = options || {};
            var reporter = options.reporter;
            var asyncProcess = utils.getOption(options, "async", true);
            var autoProcess = utils.getOption(options, "auto", true);
            if (autoProcess && !asyncProcess) {
              reporter && reporter.warn("Invalid options combination. auto=true and async=false is invalid. Setting async=true.");
              asyncProcess = true;
            }
            var batch = Batch();
            var asyncFrameHandler;
            var isProcessing = false;
            function addFunction(level, fn) {
              if (!isProcessing && autoProcess && asyncProcess && batch.size() === 0) {
                processBatchAsync();
              }
              batch.add(level, fn);
            }
            function processBatch() {
              isProcessing = true;
              while (batch.size()) {
                var processingBatch = batch;
                batch = Batch();
                processingBatch.process();
              }
              isProcessing = false;
            }
            function forceProcessBatch(localAsyncProcess) {
              if (isProcessing) {
                return;
              }
              if (localAsyncProcess === void 0) {
                localAsyncProcess = asyncProcess;
              }
              if (asyncFrameHandler) {
                cancelFrame(asyncFrameHandler);
                asyncFrameHandler = null;
              }
              if (localAsyncProcess) {
                processBatchAsync();
              } else {
                processBatch();
              }
            }
            function processBatchAsync() {
              asyncFrameHandler = requestFrame(processBatch);
            }
            function clearBatch() {
              batch = {};
              batchSize = 0;
              topLevel = 0;
              bottomLevel = 0;
            }
            function cancelFrame(listener) {
              var cancel = clearTimeout;
              return cancel(listener);
            }
            function requestFrame(callback) {
              var raf = function(fn) {
                return setTimeout(fn, 0);
              };
              return raf(callback);
            }
            return {
              add: addFunction,
              force: forceProcessBatch
            };
          };
          function Batch() {
            var batch = {};
            var size = 0;
            var topLevel2 = 0;
            var bottomLevel2 = 0;
            function add(level, fn) {
              if (!fn) {
                fn = level;
                level = 0;
              }
              if (level > topLevel2) {
                topLevel2 = level;
              } else if (level < bottomLevel2) {
                bottomLevel2 = level;
              }
              if (!batch[level]) {
                batch[level] = [];
              }
              batch[level].push(fn);
              size++;
            }
            function process() {
              for (var level = bottomLevel2; level <= topLevel2; level++) {
                var fns = batch[level];
                for (var i = 0; i < fns.length; i++) {
                  var fn = fns[i];
                  fn();
                }
              }
            }
            function getSize() {
              return size;
            }
            return {
              add,
              process,
              size: getSize
            };
          }
        }
      ),
      /***/
      "c366": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var toIObject = __webpack_require__("6821");
          var toLength = __webpack_require__("9def");
          var toAbsoluteIndex = __webpack_require__("77f1");
          module2.exports = function(IS_INCLUDES) {
            return function($this, el, fromIndex) {
              var O = toIObject($this);
              var length = toLength(O.length);
              var index = toAbsoluteIndex(fromIndex, length);
              var value;
              if (IS_INCLUDES && el != el) while (length > index) {
                value = O[index++];
                if (value != value) return true;
              }
              else for (; length > index; index++) if (IS_INCLUDES || index in O) {
                if (O[index] === el) return IS_INCLUDES || index || 0;
              }
              return !IS_INCLUDES && -1;
            };
          };
        }
      ),
      /***/
      "c541": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var content = __webpack_require__("493e");
          if (typeof content === "string") content = [[module2.i, content, ""]];
          if (content.locals) module2.exports = content.locals;
          var add = __webpack_require__("499e").default;
          var update = add("40158674", content, true, { "sourceMap": false, "shadowMode": false });
        }
      ),
      /***/
      "c5f6": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var global = __webpack_require__("7726");
          var has = __webpack_require__("69a8");
          var cof = __webpack_require__("2d95");
          var inheritIfRequired = __webpack_require__("5dbc");
          var toPrimitive = __webpack_require__("6a99");
          var fails = __webpack_require__("79e5");
          var gOPN = __webpack_require__("9093").f;
          var gOPD = __webpack_require__("11e9").f;
          var dP = __webpack_require__("86cc").f;
          var $trim = __webpack_require__("aa77").trim;
          var NUMBER = "Number";
          var $Number = global[NUMBER];
          var Base = $Number;
          var proto = $Number.prototype;
          var BROKEN_COF = cof(__webpack_require__("2aeb")(proto)) == NUMBER;
          var TRIM = "trim" in String.prototype;
          var toNumber = function(argument) {
            var it = toPrimitive(argument, false);
            if (typeof it == "string" && it.length > 2) {
              it = TRIM ? it.trim() : $trim(it, 3);
              var first = it.charCodeAt(0);
              var third, radix, maxCode;
              if (first === 43 || first === 45) {
                third = it.charCodeAt(2);
                if (third === 88 || third === 120) return NaN;
              } else if (first === 48) {
                switch (it.charCodeAt(1)) {
                  case 66:
                  case 98:
                    radix = 2;
                    maxCode = 49;
                    break;
                  // fast equal /^0b[01]+$/i
                  case 79:
                  case 111:
                    radix = 8;
                    maxCode = 55;
                    break;
                  // fast equal /^0o[0-7]+$/i
                  default:
                    return +it;
                }
                for (var digits = it.slice(2), i = 0, l = digits.length, code; i < l; i++) {
                  code = digits.charCodeAt(i);
                  if (code < 48 || code > maxCode) return NaN;
                }
                return parseInt(digits, radix);
              }
            }
            return +it;
          };
          if (!$Number(" 0o1") || !$Number("0b1") || $Number("+0x1")) {
            $Number = function Number2(value) {
              var it = arguments.length < 1 ? 0 : value;
              var that = this;
              return that instanceof $Number && (BROKEN_COF ? fails(function() {
                proto.valueOf.call(that);
              }) : cof(that) != NUMBER) ? inheritIfRequired(new Base(toNumber(it)), that, $Number) : toNumber(it);
            };
            for (var keys = __webpack_require__("9e1e") ? gOPN(Base) : (
              // ES3:
              "MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(",")
            ), j = 0, key; keys.length > j; j++) {
              if (has(Base, key = keys[j]) && !has($Number, key)) {
                dP($Number, key, gOPD(Base, key));
              }
            }
            $Number.prototype = proto;
            proto.constructor = $Number;
            __webpack_require__("2aba")(global, NUMBER, $Number);
          }
        }
      ),
      /***/
      "c69a": (
        /***/
        function(module2, exports2, __webpack_require__) {
          module2.exports = !__webpack_require__("9e1e") && !__webpack_require__("79e5")(function() {
            return Object.defineProperty(__webpack_require__("230e")("div"), "a", { get: function() {
              return 7;
            } }).a != 7;
          });
        }
      ),
      /***/
      "c946": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var forEach = __webpack_require__("b770").forEach;
          module2.exports = function(options) {
            options = options || {};
            var reporter = options.reporter;
            var batchProcessor = options.batchProcessor;
            var getState = options.stateHandler.getState;
            var hasState = options.stateHandler.hasState;
            var idHandler = options.idHandler;
            if (!batchProcessor) {
              throw new Error("Missing required dependency: batchProcessor");
            }
            if (!reporter) {
              throw new Error("Missing required dependency: reporter.");
            }
            var scrollbarSizes = getScrollbarSizes();
            var styleId = "erd_scroll_detection_scrollbar_style";
            var detectionContainerClass = "erd_scroll_detection_container";
            function initDocument(targetDocument) {
              injectScrollStyle(targetDocument, styleId, detectionContainerClass);
            }
            initDocument(window.document);
            function buildCssTextString(rules) {
              var seperator = options.important ? " !important; " : "; ";
              return (rules.join(seperator) + seperator).trim();
            }
            function getScrollbarSizes() {
              var width = 500;
              var height = 500;
              var child = document.createElement("div");
              child.style.cssText = buildCssTextString(["position: absolute", "width: " + width * 2 + "px", "height: " + height * 2 + "px", "visibility: hidden", "margin: 0", "padding: 0"]);
              var container = document.createElement("div");
              container.style.cssText = buildCssTextString(["position: absolute", "width: " + width + "px", "height: " + height + "px", "overflow: scroll", "visibility: none", "top: " + -width * 3 + "px", "left: " + -height * 3 + "px", "visibility: hidden", "margin: 0", "padding: 0"]);
              container.appendChild(child);
              document.body.insertBefore(container, document.body.firstChild);
              var widthSize = width - container.clientWidth;
              var heightSize = height - container.clientHeight;
              document.body.removeChild(container);
              return {
                width: widthSize,
                height: heightSize
              };
            }
            function injectScrollStyle(targetDocument, styleId2, containerClass) {
              function injectStyle(style2, method) {
                method = method || function(element) {
                  targetDocument.head.appendChild(element);
                };
                var styleElement = targetDocument.createElement("style");
                styleElement.innerHTML = style2;
                styleElement.id = styleId2;
                method(styleElement);
                return styleElement;
              }
              if (!targetDocument.getElementById(styleId2)) {
                var containerAnimationClass = containerClass + "_animation";
                var containerAnimationActiveClass = containerClass + "_animation_active";
                var style = "/* Created by the element-resize-detector library. */\n";
                style += "." + containerClass + " > div::-webkit-scrollbar { " + buildCssTextString(["display: none"]) + " }\n\n";
                style += "." + containerAnimationActiveClass + " { " + buildCssTextString(["-webkit-animation-duration: 0.1s", "animation-duration: 0.1s", "-webkit-animation-name: " + containerAnimationClass, "animation-name: " + containerAnimationClass]) + " }\n";
                style += "@-webkit-keyframes " + containerAnimationClass + " { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }\n";
                style += "@keyframes " + containerAnimationClass + " { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }";
                injectStyle(style);
              }
            }
            function addAnimationClass(element) {
              element.className += " " + detectionContainerClass + "_animation_active";
            }
            function addEvent(el, name, cb) {
              if (el.addEventListener) {
                el.addEventListener(name, cb);
              } else if (el.attachEvent) {
                el.attachEvent("on" + name, cb);
              } else {
                return reporter.error("[scroll] Don't know how to add event listeners.");
              }
            }
            function removeEvent(el, name, cb) {
              if (el.removeEventListener) {
                el.removeEventListener(name, cb);
              } else if (el.detachEvent) {
                el.detachEvent("on" + name, cb);
              } else {
                return reporter.error("[scroll] Don't know how to remove event listeners.");
              }
            }
            function getExpandElement(element) {
              return getState(element).container.childNodes[0].childNodes[0].childNodes[0];
            }
            function getShrinkElement(element) {
              return getState(element).container.childNodes[0].childNodes[0].childNodes[1];
            }
            function addListener(element, listener) {
              var listeners = getState(element).listeners;
              if (!listeners.push) {
                throw new Error("Cannot add listener to an element that is not detectable.");
              }
              getState(element).listeners.push(listener);
            }
            function makeDetectable(options2, element, callback) {
              if (!callback) {
                callback = element;
                element = options2;
                options2 = null;
              }
              options2 = options2 || {};
              function debug() {
                if (options2.debug) {
                  var args = Array.prototype.slice.call(arguments);
                  args.unshift(idHandler.get(element), "Scroll: ");
                  if (reporter.log.apply) {
                    reporter.log.apply(null, args);
                  } else {
                    for (var i = 0; i < args.length; i++) {
                      reporter.log(args[i]);
                    }
                  }
                }
              }
              function isDetached(element2) {
                function isInDocument(element3) {
                  return element3 === element3.ownerDocument.body || element3.ownerDocument.body.contains(element3);
                }
                if (!isInDocument(element2)) {
                  return true;
                }
                if (window.getComputedStyle(element2) === null) {
                  return true;
                }
                return false;
              }
              function isUnrendered(element2) {
                var container = getState(element2).container.childNodes[0];
                var style = window.getComputedStyle(container);
                return !style.width || style.width.indexOf("px") === -1;
              }
              function getStyle() {
                var elementStyle = window.getComputedStyle(element);
                var style = {};
                style.position = elementStyle.position;
                style.width = element.offsetWidth;
                style.height = element.offsetHeight;
                style.top = elementStyle.top;
                style.right = elementStyle.right;
                style.bottom = elementStyle.bottom;
                style.left = elementStyle.left;
                style.widthCSS = elementStyle.width;
                style.heightCSS = elementStyle.height;
                return style;
              }
              function storeStartSize() {
                var style = getStyle();
                getState(element).startSize = {
                  width: style.width,
                  height: style.height
                };
                debug("Element start size", getState(element).startSize);
              }
              function initListeners() {
                getState(element).listeners = [];
              }
              function storeStyle() {
                debug("storeStyle invoked.");
                if (!getState(element)) {
                  debug("Aborting because element has been uninstalled");
                  return;
                }
                var style = getStyle();
                getState(element).style = style;
              }
              function storeCurrentSize(element2, width, height) {
                getState(element2).lastWidth = width;
                getState(element2).lastHeight = height;
              }
              function getExpandChildElement(element2) {
                return getExpandElement(element2).childNodes[0];
              }
              function getWidthOffset() {
                return 2 * scrollbarSizes.width + 1;
              }
              function getHeightOffset() {
                return 2 * scrollbarSizes.height + 1;
              }
              function getExpandWidth(width) {
                return width + 10 + getWidthOffset();
              }
              function getExpandHeight(height) {
                return height + 10 + getHeightOffset();
              }
              function getShrinkWidth(width) {
                return width * 2 + getWidthOffset();
              }
              function getShrinkHeight(height) {
                return height * 2 + getHeightOffset();
              }
              function positionScrollbars(element2, width, height) {
                var expand = getExpandElement(element2);
                var shrink = getShrinkElement(element2);
                var expandWidth = getExpandWidth(width);
                var expandHeight = getExpandHeight(height);
                var shrinkWidth = getShrinkWidth(width);
                var shrinkHeight = getShrinkHeight(height);
                expand.scrollLeft = expandWidth;
                expand.scrollTop = expandHeight;
                shrink.scrollLeft = shrinkWidth;
                shrink.scrollTop = shrinkHeight;
              }
              function injectContainerElement() {
                var container = getState(element).container;
                if (!container) {
                  container = document.createElement("div");
                  container.className = detectionContainerClass;
                  container.style.cssText = buildCssTextString(["visibility: hidden", "display: inline", "width: 0px", "height: 0px", "z-index: -1", "overflow: hidden", "margin: 0", "padding: 0"]);
                  getState(element).container = container;
                  addAnimationClass(container);
                  element.appendChild(container);
                  var onAnimationStart = function() {
                    getState(element).onRendered && getState(element).onRendered();
                  };
                  addEvent(container, "animationstart", onAnimationStart);
                  getState(element).onAnimationStart = onAnimationStart;
                }
                return container;
              }
              function injectScrollElements() {
                function alterPositionStyles() {
                  var style = getState(element).style;
                  if (style.position === "static") {
                    element.style.setProperty("position", "relative", options2.important ? "important" : "");
                    var removeRelativeStyles = function(reporter2, element2, style2, property) {
                      function getNumericalValue(value2) {
                        return value2.replace(/[^-\d\.]/g, "");
                      }
                      var value = style2[property];
                      if (value !== "auto" && getNumericalValue(value) !== "0") {
                        reporter2.warn("An element that is positioned static has style." + property + "=" + value + " which is ignored due to the static positioning. The element will need to be positioned relative, so the style." + property + " will be set to 0. Element: ", element2);
                        element2.style[property] = 0;
                      }
                    };
                    removeRelativeStyles(reporter, element, style, "top");
                    removeRelativeStyles(reporter, element, style, "right");
                    removeRelativeStyles(reporter, element, style, "bottom");
                    removeRelativeStyles(reporter, element, style, "left");
                  }
                }
                function getLeftTopBottomRightCssText(left, top, bottom, right) {
                  left = !left ? "0" : left + "px";
                  top = !top ? "0" : top + "px";
                  bottom = !bottom ? "0" : bottom + "px";
                  right = !right ? "0" : right + "px";
                  return ["left: " + left, "top: " + top, "right: " + right, "bottom: " + bottom];
                }
                debug("Injecting elements");
                if (!getState(element)) {
                  debug("Aborting because element has been uninstalled");
                  return;
                }
                alterPositionStyles();
                var rootContainer = getState(element).container;
                if (!rootContainer) {
                  rootContainer = injectContainerElement();
                }
                var scrollbarWidth = scrollbarSizes.width;
                var scrollbarHeight = scrollbarSizes.height;
                var containerContainerStyle = buildCssTextString(["position: absolute", "flex: none", "overflow: hidden", "z-index: -1", "visibility: hidden", "width: 100%", "height: 100%", "left: 0px", "top: 0px"]);
                var containerStyle = buildCssTextString(["position: absolute", "flex: none", "overflow: hidden", "z-index: -1", "visibility: hidden"].concat(getLeftTopBottomRightCssText(-(1 + scrollbarWidth), -(1 + scrollbarHeight), -scrollbarHeight, -scrollbarWidth)));
                var expandStyle = buildCssTextString(["position: absolute", "flex: none", "overflow: scroll", "z-index: -1", "visibility: hidden", "width: 100%", "height: 100%"]);
                var shrinkStyle = buildCssTextString(["position: absolute", "flex: none", "overflow: scroll", "z-index: -1", "visibility: hidden", "width: 100%", "height: 100%"]);
                var expandChildStyle = buildCssTextString(["position: absolute", "left: 0", "top: 0"]);
                var shrinkChildStyle = buildCssTextString(["position: absolute", "width: 200%", "height: 200%"]);
                var containerContainer = document.createElement("div");
                var container = document.createElement("div");
                var expand = document.createElement("div");
                var expandChild = document.createElement("div");
                var shrink = document.createElement("div");
                var shrinkChild = document.createElement("div");
                containerContainer.dir = "ltr";
                containerContainer.style.cssText = containerContainerStyle;
                containerContainer.className = detectionContainerClass;
                container.className = detectionContainerClass;
                container.style.cssText = containerStyle;
                expand.style.cssText = expandStyle;
                expandChild.style.cssText = expandChildStyle;
                shrink.style.cssText = shrinkStyle;
                shrinkChild.style.cssText = shrinkChildStyle;
                expand.appendChild(expandChild);
                shrink.appendChild(shrinkChild);
                container.appendChild(expand);
                container.appendChild(shrink);
                containerContainer.appendChild(container);
                rootContainer.appendChild(containerContainer);
                function onExpandScroll() {
                  getState(element).onExpand && getState(element).onExpand();
                }
                function onShrinkScroll() {
                  getState(element).onShrink && getState(element).onShrink();
                }
                addEvent(expand, "scroll", onExpandScroll);
                addEvent(shrink, "scroll", onShrinkScroll);
                getState(element).onExpandScroll = onExpandScroll;
                getState(element).onShrinkScroll = onShrinkScroll;
              }
              function registerListenersAndPositionElements() {
                function updateChildSizes(element2, width, height) {
                  var expandChild = getExpandChildElement(element2);
                  var expandWidth = getExpandWidth(width);
                  var expandHeight = getExpandHeight(height);
                  expandChild.style.setProperty("width", expandWidth + "px", options2.important ? "important" : "");
                  expandChild.style.setProperty("height", expandHeight + "px", options2.important ? "important" : "");
                }
                function updateDetectorElements(done) {
                  var width = element.offsetWidth;
                  var height = element.offsetHeight;
                  var sizeChanged = width !== getState(element).lastWidth || height !== getState(element).lastHeight;
                  debug("Storing current size", width, height);
                  storeCurrentSize(element, width, height);
                  batchProcessor.add(0, function performUpdateChildSizes() {
                    if (!sizeChanged) {
                      return;
                    }
                    if (!getState(element)) {
                      debug("Aborting because element has been uninstalled");
                      return;
                    }
                    if (!areElementsInjected()) {
                      debug("Aborting because element container has not been initialized");
                      return;
                    }
                    if (options2.debug) {
                      var w = element.offsetWidth;
                      var h = element.offsetHeight;
                      if (w !== width || h !== height) {
                        reporter.warn(idHandler.get(element), "Scroll: Size changed before updating detector elements.");
                      }
                    }
                    updateChildSizes(element, width, height);
                  });
                  batchProcessor.add(1, function updateScrollbars() {
                    if (!getState(element)) {
                      debug("Aborting because element has been uninstalled");
                      return;
                    }
                    if (!areElementsInjected()) {
                      debug("Aborting because element container has not been initialized");
                      return;
                    }
                    positionScrollbars(element, width, height);
                  });
                  if (sizeChanged && done) {
                    batchProcessor.add(2, function() {
                      if (!getState(element)) {
                        debug("Aborting because element has been uninstalled");
                        return;
                      }
                      if (!areElementsInjected()) {
                        debug("Aborting because element container has not been initialized");
                        return;
                      }
                      done();
                    });
                  }
                }
                function areElementsInjected() {
                  return !!getState(element).container;
                }
                function notifyListenersIfNeeded() {
                  function isFirstNotify() {
                    return getState(element).lastNotifiedWidth === void 0;
                  }
                  debug("notifyListenersIfNeeded invoked");
                  var state = getState(element);
                  if (isFirstNotify() && state.lastWidth === state.startSize.width && state.lastHeight === state.startSize.height) {
                    return debug("Not notifying: Size is the same as the start size, and there has been no notification yet.");
                  }
                  if (state.lastWidth === state.lastNotifiedWidth && state.lastHeight === state.lastNotifiedHeight) {
                    return debug("Not notifying: Size already notified");
                  }
                  debug("Current size not notified, notifying...");
                  state.lastNotifiedWidth = state.lastWidth;
                  state.lastNotifiedHeight = state.lastHeight;
                  forEach(getState(element).listeners, function(listener) {
                    listener(element);
                  });
                }
                function handleRender() {
                  debug("startanimation triggered.");
                  if (isUnrendered(element)) {
                    debug("Ignoring since element is still unrendered...");
                    return;
                  }
                  debug("Element rendered.");
                  var expand = getExpandElement(element);
                  var shrink = getShrinkElement(element);
                  if (expand.scrollLeft === 0 || expand.scrollTop === 0 || shrink.scrollLeft === 0 || shrink.scrollTop === 0) {
                    debug("Scrollbars out of sync. Updating detector elements...");
                    updateDetectorElements(notifyListenersIfNeeded);
                  }
                }
                function handleScroll() {
                  debug("Scroll detected.");
                  if (isUnrendered(element)) {
                    debug("Scroll event fired while unrendered. Ignoring...");
                    return;
                  }
                  updateDetectorElements(notifyListenersIfNeeded);
                }
                debug("registerListenersAndPositionElements invoked.");
                if (!getState(element)) {
                  debug("Aborting because element has been uninstalled");
                  return;
                }
                getState(element).onRendered = handleRender;
                getState(element).onExpand = handleScroll;
                getState(element).onShrink = handleScroll;
                var style = getState(element).style;
                updateChildSizes(element, style.width, style.height);
              }
              function finalizeDomMutation() {
                debug("finalizeDomMutation invoked.");
                if (!getState(element)) {
                  debug("Aborting because element has been uninstalled");
                  return;
                }
                var style = getState(element).style;
                storeCurrentSize(element, style.width, style.height);
                positionScrollbars(element, style.width, style.height);
              }
              function ready() {
                callback(element);
              }
              function install() {
                debug("Installing...");
                initListeners();
                storeStartSize();
                batchProcessor.add(0, storeStyle);
                batchProcessor.add(1, injectScrollElements);
                batchProcessor.add(2, registerListenersAndPositionElements);
                batchProcessor.add(3, finalizeDomMutation);
                batchProcessor.add(4, ready);
              }
              debug("Making detectable...");
              if (isDetached(element)) {
                debug("Element is detached");
                injectContainerElement();
                debug("Waiting until element is attached...");
                getState(element).onRendered = function() {
                  debug("Element is now attached");
                  install();
                };
              } else {
                install();
              }
            }
            function uninstall(element) {
              var state = getState(element);
              if (!state) {
                return;
              }
              state.onExpandScroll && removeEvent(getExpandElement(element), "scroll", state.onExpandScroll);
              state.onShrinkScroll && removeEvent(getShrinkElement(element), "scroll", state.onShrinkScroll);
              state.onAnimationStart && removeEvent(state.container, "animationstart", state.onAnimationStart);
              state.container && element.removeChild(state.container);
            }
            return {
              makeDetectable,
              addListener,
              uninstall,
              initDocument
            };
          };
        }
      ),
      /***/
      "ca5a": (
        /***/
        function(module2, exports2) {
          var id = 0;
          var px = Math.random();
          module2.exports = function(key) {
            return "Symbol(".concat(key === void 0 ? "" : key, ")_", (++id + px).toString(36));
          };
        }
      ),
      /***/
      "cadf": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var addToUnscopables = __webpack_require__("9c6c");
          var step = __webpack_require__("d53b");
          var Iterators = __webpack_require__("84f2");
          var toIObject = __webpack_require__("6821");
          module2.exports = __webpack_require__("01f9")(Array, "Array", function(iterated, kind) {
            this._t = toIObject(iterated);
            this._i = 0;
            this._k = kind;
          }, function() {
            var O = this._t;
            var kind = this._k;
            var index = this._i++;
            if (!O || index >= O.length) {
              this._t = void 0;
              return step(1);
            }
            if (kind == "keys") return step(0, index);
            if (kind == "values") return step(0, O[index]);
            return step(0, [index, O[index]]);
          }, "values");
          Iterators.Arguments = Iterators.Array;
          addToUnscopables("keys");
          addToUnscopables("values");
          addToUnscopables("entries");
        }
      ),
      /***/
      "cb7c": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var isObject = __webpack_require__("d3f4");
          module2.exports = function(it) {
            if (!isObject(it)) throw TypeError(it + " is not an object!");
            return it;
          };
        }
      ),
      /***/
      "ce10": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var has = __webpack_require__("69a8");
          var toIObject = __webpack_require__("6821");
          var arrayIndexOf = __webpack_require__("c366")(false);
          var IE_PROTO = __webpack_require__("613b")("IE_PROTO");
          module2.exports = function(object, names) {
            var O = toIObject(object);
            var i = 0;
            var result = [];
            var key;
            for (key in O) if (key != IE_PROTO) has(O, key) && result.push(key);
            while (names.length > i) if (has(O, key = names[i++])) {
              ~arrayIndexOf(result, key) || result.push(key);
            }
            return result;
          };
        }
      ),
      /***/
      "d3f4": (
        /***/
        function(module2, exports2) {
          module2.exports = function(it) {
            return typeof it === "object" ? it !== null : typeof it === "function";
          };
        }
      ),
      /***/
      "d53b": (
        /***/
        function(module2, exports2) {
          module2.exports = function(done, value) {
            return { value, done: !!done };
          };
        }
      ),
      /***/
      "d6eb": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var prop = "_erd";
          function initState(element) {
            element[prop] = {};
            return getState(element);
          }
          function getState(element) {
            return element[prop];
          }
          function cleanState(element) {
            delete element[prop];
          }
          module2.exports = {
            initState,
            getState,
            cleanState
          };
        }
      ),
      /***/
      "d8e8": (
        /***/
        function(module2, exports2) {
          module2.exports = function(it) {
            if (typeof it != "function") throw TypeError(it + " is not a function!");
            return it;
          };
        }
      ),
      /***/
      "e11e": (
        /***/
        function(module2, exports2) {
          module2.exports = "constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",");
        }
      ),
      /***/
      "eec4": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var forEach = __webpack_require__("b770").forEach;
          var elementUtilsMaker = __webpack_require__("5be5");
          var listenerHandlerMaker = __webpack_require__("49ad");
          var idGeneratorMaker = __webpack_require__("2cef");
          var idHandlerMaker = __webpack_require__("5058");
          var reporterMaker = __webpack_require__("abb4");
          var browserDetector = __webpack_require__("18e9");
          var batchProcessorMaker = __webpack_require__("c274");
          var stateHandler = __webpack_require__("d6eb");
          var objectStrategyMaker = __webpack_require__("18d2");
          var scrollStrategyMaker = __webpack_require__("c946");
          function isCollection(obj) {
            return Array.isArray(obj) || obj.length !== void 0;
          }
          function toArray(collection) {
            if (!Array.isArray(collection)) {
              var array = [];
              forEach(collection, function(obj) {
                array.push(obj);
              });
              return array;
            } else {
              return collection;
            }
          }
          function isElement(obj) {
            return obj && obj.nodeType === 1;
          }
          module2.exports = function(options) {
            options = options || {};
            var idHandler;
            if (options.idHandler) {
              idHandler = {
                get: function(element) {
                  return options.idHandler.get(element, true);
                },
                set: options.idHandler.set
              };
            } else {
              var idGenerator = idGeneratorMaker();
              var defaultIdHandler = idHandlerMaker({
                idGenerator,
                stateHandler
              });
              idHandler = defaultIdHandler;
            }
            var reporter = options.reporter;
            if (!reporter) {
              var quiet = reporter === false;
              reporter = reporterMaker(quiet);
            }
            var batchProcessor = getOption(options, "batchProcessor", batchProcessorMaker({ reporter }));
            var globalOptions = {};
            globalOptions.callOnAdd = !!getOption(options, "callOnAdd", true);
            globalOptions.debug = !!getOption(options, "debug", false);
            var eventListenerHandler = listenerHandlerMaker(idHandler);
            var elementUtils = elementUtilsMaker({
              stateHandler
            });
            var detectionStrategy;
            var desiredStrategy = getOption(options, "strategy", "object");
            var importantCssRules = getOption(options, "important", false);
            var strategyOptions = {
              reporter,
              batchProcessor,
              stateHandler,
              idHandler,
              important: importantCssRules
            };
            if (desiredStrategy === "scroll") {
              if (browserDetector.isLegacyOpera()) {
                reporter.warn("Scroll strategy is not supported on legacy Opera. Changing to object strategy.");
                desiredStrategy = "object";
              } else if (browserDetector.isIE(9)) {
                reporter.warn("Scroll strategy is not supported on IE9. Changing to object strategy.");
                desiredStrategy = "object";
              }
            }
            if (desiredStrategy === "scroll") {
              detectionStrategy = scrollStrategyMaker(strategyOptions);
            } else if (desiredStrategy === "object") {
              detectionStrategy = objectStrategyMaker(strategyOptions);
            } else {
              throw new Error("Invalid strategy name: " + desiredStrategy);
            }
            var onReadyCallbacks = {};
            function listenTo(options2, elements, listener) {
              function onResizeCallback(element) {
                var listeners = eventListenerHandler.get(element);
                forEach(listeners, function callListenerProxy(listener2) {
                  listener2(element);
                });
              }
              function addListener(callOnAdd2, element, listener2) {
                eventListenerHandler.add(element, listener2);
                if (callOnAdd2) {
                  listener2(element);
                }
              }
              if (!listener) {
                listener = elements;
                elements = options2;
                options2 = {};
              }
              if (!elements) {
                throw new Error("At least one element required.");
              }
              if (!listener) {
                throw new Error("Listener required.");
              }
              if (isElement(elements)) {
                elements = [elements];
              } else if (isCollection(elements)) {
                elements = toArray(elements);
              } else {
                return reporter.error("Invalid arguments. Must be a DOM element or a collection of DOM elements.");
              }
              var elementsReady = 0;
              var callOnAdd = getOption(options2, "callOnAdd", globalOptions.callOnAdd);
              var onReadyCallback = getOption(options2, "onReady", function noop() {
              });
              var debug = getOption(options2, "debug", globalOptions.debug);
              forEach(elements, function attachListenerToElement(element) {
                if (!stateHandler.getState(element)) {
                  stateHandler.initState(element);
                  idHandler.set(element);
                }
                var id = idHandler.get(element);
                debug && reporter.log("Attaching listener to element", id, element);
                if (!elementUtils.isDetectable(element)) {
                  debug && reporter.log(id, "Not detectable.");
                  if (elementUtils.isBusy(element)) {
                    debug && reporter.log(id, "System busy making it detectable");
                    addListener(callOnAdd, element, listener);
                    onReadyCallbacks[id] = onReadyCallbacks[id] || [];
                    onReadyCallbacks[id].push(function onReady() {
                      elementsReady++;
                      if (elementsReady === elements.length) {
                        onReadyCallback();
                      }
                    });
                    return;
                  }
                  debug && reporter.log(id, "Making detectable...");
                  elementUtils.markBusy(element, true);
                  return detectionStrategy.makeDetectable({ debug, important: importantCssRules }, element, function onElementDetectable(element2) {
                    debug && reporter.log(id, "onElementDetectable");
                    if (stateHandler.getState(element2)) {
                      elementUtils.markAsDetectable(element2);
                      elementUtils.markBusy(element2, false);
                      detectionStrategy.addListener(element2, onResizeCallback);
                      addListener(callOnAdd, element2, listener);
                      var state = stateHandler.getState(element2);
                      if (state && state.startSize) {
                        var width = element2.offsetWidth;
                        var height = element2.offsetHeight;
                        if (state.startSize.width !== width || state.startSize.height !== height) {
                          onResizeCallback(element2);
                        }
                      }
                      if (onReadyCallbacks[id]) {
                        forEach(onReadyCallbacks[id], function(callback) {
                          callback();
                        });
                      }
                    } else {
                      debug && reporter.log(id, "Element uninstalled before being detectable.");
                    }
                    delete onReadyCallbacks[id];
                    elementsReady++;
                    if (elementsReady === elements.length) {
                      onReadyCallback();
                    }
                  });
                }
                debug && reporter.log(id, "Already detecable, adding listener.");
                addListener(callOnAdd, element, listener);
                elementsReady++;
              });
              if (elementsReady === elements.length) {
                onReadyCallback();
              }
            }
            function uninstall(elements) {
              if (!elements) {
                return reporter.error("At least one element is required.");
              }
              if (isElement(elements)) {
                elements = [elements];
              } else if (isCollection(elements)) {
                elements = toArray(elements);
              } else {
                return reporter.error("Invalid arguments. Must be a DOM element or a collection of DOM elements.");
              }
              forEach(elements, function(element) {
                eventListenerHandler.removeAllListeners(element);
                detectionStrategy.uninstall(element);
                stateHandler.cleanState(element);
              });
            }
            function initDocument(targetDocument) {
              detectionStrategy.initDocument && detectionStrategy.initDocument(targetDocument);
            }
            return {
              listenTo,
              removeListener: eventListenerHandler.removeListener,
              removeAllListeners: eventListenerHandler.removeAllListeners,
              uninstall,
              initDocument
            };
          };
          function getOption(options, name, defaultValue) {
            var value = options[name];
            if ((value === void 0 || value === null) && defaultValue !== void 0) {
              return defaultValue;
            }
            return value;
          }
        }
      ),
      /***/
      "f1ae": (
        /***/
        function(module2, exports2, __webpack_require__) {
          "use strict";
          var $defineProperty = __webpack_require__("86cc");
          var createDesc = __webpack_require__("4630");
          module2.exports = function(object, index, value) {
            if (index in object) $defineProperty.f(object, index, createDesc(0, value));
            else object[index] = value;
          };
        }
      ),
      /***/
      "f751": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var $export = __webpack_require__("5ca1");
          $export($export.S + $export.F, "Object", { assign: __webpack_require__("7333") });
        }
      ),
      /***/
      "fa5b": (
        /***/
        function(module2, exports2, __webpack_require__) {
          module2.exports = __webpack_require__("5537")("native-function-to-string", Function.toString);
        }
      ),
      /***/
      "fab2": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var document2 = __webpack_require__("7726").document;
          module2.exports = document2 && document2.documentElement;
        }
      ),
      /***/
      "fb15": (
        /***/
        function(module2, __webpack_exports__, __webpack_require__) {
          "use strict";
          __webpack_require__.r(__webpack_exports__);
          __webpack_require__.d(__webpack_exports__, "GridLayout", function() {
            return (
              /* reexport */
              GridLayout
            );
          });
          __webpack_require__.d(__webpack_exports__, "GridItem", function() {
            return (
              /* reexport */
              GridItem
            );
          });
          var all_namespaceObject = {};
          __webpack_require__.r(all_namespaceObject);
          __webpack_require__.d(all_namespaceObject, "edgeTarget", function() {
            return edgeTarget;
          });
          __webpack_require__.d(all_namespaceObject, "elements", function() {
            return snappers_elements;
          });
          __webpack_require__.d(all_namespaceObject, "grid", function() {
            return grid;
          });
          if (typeof window !== "undefined") {
            var currentScript = window.document.currentScript;
            if (true) {
              var getCurrentScript = __webpack_require__("8875");
              currentScript = getCurrentScript();
              if (!("currentScript" in document)) {
                Object.defineProperty(document, "currentScript", { get: getCurrentScript });
              }
            }
            var src = currentScript && currentScript.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);
            if (src) {
              __webpack_require__.p = src[1];
            }
          }
          var setPublicPath = null;
          var external_commonjs_vue_commonjs2_vue_root_Vue_ = __webpack_require__("8bbf");
          function render(_ctx, _cache, $props, $setup, $data, $options) {
            return Object(external_commonjs_vue_commonjs2_vue_root_Vue_["openBlock"])(), Object(external_commonjs_vue_commonjs2_vue_root_Vue_["createBlock"])("div", {
              ref: "item",
              class: ["vue-grid-item", $options.classObj],
              style: _ctx.style
            }, [Object(external_commonjs_vue_commonjs2_vue_root_Vue_["renderSlot"])(_ctx.$slots, "default"), $options.resizableAndNotStatic ? (Object(external_commonjs_vue_commonjs2_vue_root_Vue_["openBlock"])(), Object(external_commonjs_vue_commonjs2_vue_root_Vue_["createBlock"])("span", {
              key: 0,
              ref: "handle",
              class: $options.resizableHandleClass
            }, null, 2)) : Object(external_commonjs_vue_commonjs2_vue_root_Vue_["createCommentVNode"])("", true)], 6);
          }
          var es6_regexp_replace = __webpack_require__("a481");
          var es6_regexp_match = __webpack_require__("4917");
          var es6_number_constructor = __webpack_require__("c5f6");
          var es6_array_iterator = __webpack_require__("cadf");
          var es6_object_keys = __webpack_require__("456d");
          var web_dom_iterable = __webpack_require__("ac6a");
          var es6_array_sort = __webpack_require__("55dd");
          function bottom(layout) {
            var max = 0, bottomY;
            for (var i = 0, len = layout.length; i < len; i++) {
              bottomY = layout[i].y + layout[i].h;
              if (bottomY > max) max = bottomY;
            }
            return max;
          }
          function cloneLayout(layout) {
            var newLayout = Array(layout.length);
            for (var i = 0, len = layout.length; i < len; i++) {
              newLayout[i] = cloneLayoutItem(layout[i]);
            }
            return newLayout;
          }
          function cloneLayoutItem(layoutItem) {
            return JSON.parse(JSON.stringify(layoutItem));
          }
          function collides(l1, l2) {
            if (l1 === l2) return false;
            if (l1.x + l1.w <= l2.x) return false;
            if (l1.x >= l2.x + l2.w) return false;
            if (l1.y + l1.h <= l2.y) return false;
            if (l1.y >= l2.y + l2.h) return false;
            return true;
          }
          function compact(layout, verticalCompact) {
            var compareWith = getStatics(layout);
            var sorted = sortLayoutItemsByRowCol(layout);
            var out = Array(layout.length);
            for (var i = 0, len = sorted.length; i < len; i++) {
              var l = sorted[i];
              if (!l.static) {
                l = compactItem(compareWith, l, verticalCompact);
                compareWith.push(l);
              }
              out[layout.indexOf(l)] = l;
              l.moved = false;
            }
            return out;
          }
          function compactItem(compareWith, l, verticalCompact) {
            if (verticalCompact) {
              while (l.y > 0 && !getFirstCollision(compareWith, l)) {
                l.y--;
              }
            }
            var collides2;
            while (collides2 = getFirstCollision(compareWith, l)) {
              l.y = collides2.y + collides2.h;
            }
            return l;
          }
          function correctBounds(layout, bounds) {
            var collidesWith = getStatics(layout);
            for (var i = 0, len = layout.length; i < len; i++) {
              var l = layout[i];
              if (l.x + l.w > bounds.cols) l.x = bounds.cols - l.w;
              if (l.x < 0) {
                l.x = 0;
                l.w = bounds.cols;
              }
              if (!l.static) collidesWith.push(l);
              else {
                while (getFirstCollision(collidesWith, l)) {
                  l.y++;
                }
              }
            }
            return layout;
          }
          function getLayoutItem(layout, id) {
            for (var i = 0, len = layout.length; i < len; i++) {
              if (layout[i].i === id) return layout[i];
            }
          }
          function getFirstCollision(layout, layoutItem) {
            for (var i = 0, len = layout.length; i < len; i++) {
              if (collides(layout[i], layoutItem)) return layout[i];
            }
          }
          function getAllCollisions(layout, layoutItem) {
            return layout.filter(function(l) {
              return collides(l, layoutItem);
            });
          }
          function getStatics(layout) {
            return layout.filter(function(l) {
              return l.static;
            });
          }
          function moveElement(layout, l, x, y, isUserAction, preventCollision) {
            if (l.static) return layout;
            var oldX = l.x;
            var oldY = l.y;
            var movingUp = y && l.y > y;
            if (typeof x === "number") l.x = x;
            if (typeof y === "number") l.y = y;
            l.moved = true;
            var sorted = sortLayoutItemsByRowCol(layout);
            if (movingUp) sorted = sorted.reverse();
            var collisions = getAllCollisions(sorted, l);
            if (preventCollision && collisions.length) {
              l.x = oldX;
              l.y = oldY;
              l.moved = false;
              return layout;
            }
            for (var i = 0, len = collisions.length; i < len; i++) {
              var collision = collisions[i];
              if (collision.moved) continue;
              if (l.y > collision.y && l.y - collision.y > collision.h / 4) continue;
              if (collision.static) {
                layout = moveElementAwayFromCollision(layout, collision, l, isUserAction);
              } else {
                layout = moveElementAwayFromCollision(layout, l, collision, isUserAction);
              }
            }
            return layout;
          }
          function moveElementAwayFromCollision(layout, collidesWith, itemToMove, isUserAction) {
            var preventCollision = false;
            if (isUserAction) {
              var fakeItem = {
                x: itemToMove.x,
                y: itemToMove.y,
                w: itemToMove.w,
                h: itemToMove.h,
                i: "-1"
              };
              fakeItem.y = Math.max(collidesWith.y - itemToMove.h, 0);
              if (!getFirstCollision(layout, fakeItem)) {
                return moveElement(layout, itemToMove, void 0, fakeItem.y, preventCollision);
              }
            }
            return moveElement(layout, itemToMove, void 0, itemToMove.y + 1, preventCollision);
          }
          function perc(num) {
            return num * 100 + "%";
          }
          function setTransform(top, left, width, height) {
            var translate = "translate3d(" + left + "px," + top + "px, 0)";
            return {
              transform: translate,
              WebkitTransform: translate,
              MozTransform: translate,
              msTransform: translate,
              OTransform: translate,
              width: width + "px",
              height: height + "px",
              position: "absolute"
            };
          }
          function setTransformRtl(top, right, width, height) {
            var translate = "translate3d(" + right * -1 + "px," + top + "px, 0)";
            return {
              transform: translate,
              WebkitTransform: translate,
              MozTransform: translate,
              msTransform: translate,
              OTransform: translate,
              width: width + "px",
              height: height + "px",
              position: "absolute"
            };
          }
          function setTopLeft(top, left, width, height) {
            return {
              top: top + "px",
              left: left + "px",
              width: width + "px",
              height: height + "px",
              position: "absolute"
            };
          }
          function setTopRight(top, right, width, height) {
            return {
              top: top + "px",
              right: right + "px",
              width: width + "px",
              height: height + "px",
              position: "absolute"
            };
          }
          function sortLayoutItemsByRowCol(layout) {
            return [].concat(layout).sort(function(a, b) {
              if (a.y === b.y && a.x === b.x) {
                return 0;
              }
              if (a.y > b.y || a.y === b.y && a.x > b.x) {
                return 1;
              }
              return -1;
            });
          }
          function validateLayout(layout, contextName) {
            contextName = contextName || "Layout";
            var subProps = ["x", "y", "w", "h"];
            var keyArr = [];
            if (!Array.isArray(layout)) throw new Error(contextName + " must be an array!");
            for (var i = 0, len = layout.length; i < len; i++) {
              var item = layout[i];
              for (var j = 0; j < subProps.length; j++) {
                if (typeof item[subProps[j]] !== "number") {
                  throw new Error("VueGridLayout: " + contextName + "[" + i + "]." + subProps[j] + " must be a number!");
                }
              }
              if (item.i === void 0 || item.i === null) {
                throw new Error("VueGridLayout: " + contextName + "[" + i + "].i cannot be null!");
              }
              if (typeof item.i !== "number" && typeof item.i !== "string") {
                throw new Error("VueGridLayout: " + contextName + "[" + i + "].i must be a string or number!");
              }
              if (keyArr.indexOf(item.i) >= 0) {
                throw new Error("VueGridLayout: " + contextName + "[" + i + "].i must be unique!");
              }
              keyArr.push(item.i);
              if (item.static !== void 0 && typeof item.static !== "boolean") {
                throw new Error("VueGridLayout: " + contextName + "[" + i + "].static must be a boolean!");
              }
            }
          }
          function autoBindHandlers(el, fns) {
            fns.forEach(function(key) {
              return el[key] = el[key].bind(el);
            });
          }
          function createMarkup(obj) {
            var keys = Object.keys(obj);
            if (!keys.length) return "";
            var i, len = keys.length;
            var result = "";
            for (i = 0; i < len; i++) {
              var key = keys[i];
              var val = obj[key];
              result += hyphenate(key) + ":" + addPx(key, val) + ";";
            }
            return result;
          }
          var IS_UNITLESS = {
            animationIterationCount: true,
            boxFlex: true,
            boxFlexGroup: true,
            boxOrdinalGroup: true,
            columnCount: true,
            flex: true,
            flexGrow: true,
            flexPositive: true,
            flexShrink: true,
            flexNegative: true,
            flexOrder: true,
            gridRow: true,
            gridColumn: true,
            fontWeight: true,
            lineClamp: true,
            lineHeight: true,
            opacity: true,
            order: true,
            orphans: true,
            tabSize: true,
            widows: true,
            zIndex: true,
            zoom: true,
            // SVG-related properties
            fillOpacity: true,
            stopOpacity: true,
            strokeDashoffset: true,
            strokeOpacity: true,
            strokeWidth: true
          };
          function addPx(name, value) {
            if (typeof value === "number" && !IS_UNITLESS[name]) {
              return value + "px";
            } else {
              return value;
            }
          }
          var hyphenateRE = /([a-z\d])([A-Z])/g;
          function hyphenate(str) {
            return str.replace(hyphenateRE, "$1-$2").toLowerCase();
          }
          function findItemInArray(array2, property, value) {
            for (var i = 0; i < array2.length; i++) {
              if (array2[i][property] == value) return true;
            }
            return false;
          }
          function findAndRemove(array2, property, value) {
            array2.forEach(function(result, index) {
              if (result[property] === value) {
                array2.splice(index, 1);
              }
            });
          }
          function getControlPosition(e) {
            return offsetXYFromParentOf(e);
          }
          function offsetXYFromParentOf(evt) {
            var offsetParent = evt.target.offsetParent || document.body;
            var offsetParentRect = evt.offsetParent === document.body ? {
              left: 0,
              top: 0
            } : offsetParent.getBoundingClientRect();
            var x = evt.clientX + offsetParent.scrollLeft - offsetParentRect.left;
            var y = evt.clientY + offsetParent.scrollTop - offsetParentRect.top;
            return {
              x,
              y
            };
          }
          function createCoreData(lastX, lastY, x, y) {
            var isStart = !isNum(lastX);
            if (isStart) {
              return {
                deltaX: 0,
                deltaY: 0,
                lastX: x,
                lastY: y,
                x,
                y
              };
            } else {
              return {
                deltaX: x - lastX,
                deltaY: y - lastY,
                lastX,
                lastY,
                x,
                y
              };
            }
          }
          function isNum(num) {
            return typeof num === "number" && !isNaN(num);
          }
          function getBreakpointFromWidth(breakpoints, width) {
            var sorted = sortBreakpoints(breakpoints);
            var matching = sorted[0];
            for (var i = 1, len = sorted.length; i < len; i++) {
              var breakpointName = sorted[i];
              if (width > breakpoints[breakpointName]) matching = breakpointName;
            }
            return matching;
          }
          function getColsFromBreakpoint(breakpoint, cols) {
            if (!cols[breakpoint]) {
              throw new Error("ResponsiveGridLayout: `cols` entry for breakpoint " + breakpoint + " is missing!");
            }
            return cols[breakpoint];
          }
          function findOrGenerateResponsiveLayout(orgLayout, layouts, breakpoints, breakpoint, lastBreakpoint, cols, verticalCompact) {
            if (layouts[breakpoint]) return cloneLayout(layouts[breakpoint]);
            var layout = orgLayout;
            var breakpointsSorted = sortBreakpoints(breakpoints);
            var breakpointsAbove = breakpointsSorted.slice(breakpointsSorted.indexOf(breakpoint));
            for (var i = 0, len = breakpointsAbove.length; i < len; i++) {
              var b = breakpointsAbove[i];
              if (layouts[b]) {
                layout = layouts[b];
                break;
              }
            }
            layout = cloneLayout(layout || []);
            return compact(correctBounds(layout, {
              cols
            }), verticalCompact);
          }
          function generateResponsiveLayout(layout, breakpoints, breakpoint, lastBreakpoint, cols, verticalCompact) {
            layout = cloneLayout(layout || []);
            return compact(correctBounds(layout, {
              cols
            }), verticalCompact);
          }
          function sortBreakpoints(breakpoints) {
            var keys = Object.keys(breakpoints);
            return keys.sort(function(a, b) {
              return breakpoints[a] - breakpoints[b];
            });
          }
          var currentDir = "auto";
          function hasDocument() {
            return typeof document !== "undefined";
          }
          function hasWindow() {
            return typeof window !== "undefined";
          }
          function getDocumentDir() {
            if (!hasDocument()) {
              return currentDir;
            }
            var direction = typeof document.dir !== "undefined" ? document.dir : document.getElementsByTagName("html")[0].getAttribute("dir");
            return direction;
          }
          function setDocumentDir(dir) {
            if (!hasDocument) {
              currentDir = dir;
              return;
            }
            var html = document.getElementsByTagName("html")[0];
            html.setAttribute("dir", dir);
          }
          function addWindowEventListener(event, callback) {
            if (!hasWindow) {
              callback();
              return;
            }
            window.addEventListener(event, callback);
          }
          function removeWindowEventListener(event, callback) {
            if (!hasWindow) {
              return;
            }
            window.removeEventListener(event, callback);
          }
          const domObjects = {
            init,
            document: null,
            DocumentFragment: null,
            SVGElement: null,
            SVGSVGElement: null,
            SVGElementInstance: null,
            Element: null,
            HTMLElement: null,
            Event: null,
            Touch: null,
            PointerEvent: null
          };
          function blank() {
          }
          var utils_domObjects = domObjects;
          function init(window2) {
            const win2 = window2;
            domObjects.document = win2.document;
            domObjects.DocumentFragment = win2.DocumentFragment || blank;
            domObjects.SVGElement = win2.SVGElement || blank;
            domObjects.SVGSVGElement = win2.SVGSVGElement || blank;
            domObjects.SVGElementInstance = win2.SVGElementInstance || blank;
            domObjects.Element = win2.Element || blank;
            domObjects.HTMLElement = win2.HTMLElement || domObjects.Element;
            domObjects.Event = win2.Event;
            domObjects.Touch = win2.Touch || blank;
            domObjects.PointerEvent = win2.PointerEvent || win2.MSPointerEvent;
          }
          var isWindow = (thing) => !!(thing && thing.Window) && thing instanceof thing.Window;
          let realWindow = void 0;
          let win = void 0;
          function window_init(window2) {
            realWindow = window2;
            const el = window2.document.createTextNode("");
            if (el.ownerDocument !== window2.document && typeof window2.wrap === "function" && window2.wrap(el) === el) {
              window2 = window2.wrap(window2);
            }
            win = window2;
          }
          if (typeof window !== "undefined" && !!window) {
            window_init(window);
          }
          function getWindow(node) {
            if (isWindow(node)) {
              return node;
            }
            const rootNode = node.ownerDocument || node;
            return rootNode.defaultView || win.window;
          }
          const is_window = (thing) => thing === win || isWindow(thing);
          const docFrag = (thing) => object(thing) && thing.nodeType === 11;
          const object = (thing) => !!thing && typeof thing === "object";
          const func = (thing) => typeof thing === "function";
          const number = (thing) => typeof thing === "number";
          const bool = (thing) => typeof thing === "boolean";
          const string = (thing) => typeof thing === "string";
          const is_element = (thing) => {
            if (!thing || typeof thing !== "object") {
              return false;
            }
            const _window = getWindow(thing) || win;
            return /object|function/.test(typeof _window.Element) ? thing instanceof _window.Element : thing.nodeType === 1 && typeof thing.nodeName === "string";
          };
          const plainObject = (thing) => object(thing) && !!thing.constructor && /function Object\b/.test(thing.constructor.toString());
          const array = (thing) => object(thing) && typeof thing.length !== "undefined" && func(thing.splice);
          var is = {
            window: is_window,
            docFrag,
            object,
            func,
            number,
            bool,
            string,
            element: is_element,
            plainObject,
            array
          };
          const browser = {
            init: browser_init,
            supportsTouch: null,
            supportsPointerEvent: null,
            isIOS7: null,
            isIOS: null,
            isIe9: null,
            isOperaMobile: null,
            prefixedMatchesSelector: null,
            pEventTypes: null,
            wheelEvent: null
          };
          function browser_init(window2) {
            const Element = utils_domObjects.Element;
            const navigator2 = win.navigator;
            browser.supportsTouch = "ontouchstart" in window2 || is.func(window2.DocumentTouch) && utils_domObjects.document instanceof window2.DocumentTouch;
            browser.supportsPointerEvent = navigator2.pointerEnabled !== false && !!utils_domObjects.PointerEvent;
            browser.isIOS = /iP(hone|od|ad)/.test(navigator2.platform);
            browser.isIOS7 = /iP(hone|od|ad)/.test(navigator2.platform) && /OS 7[^\d]/.test(navigator2.appVersion);
            browser.isIe9 = /MSIE 9/.test(navigator2.userAgent);
            browser.isOperaMobile = navigator2.appName === "Opera" && browser.supportsTouch && /Presto/.test(navigator2.userAgent);
            browser.prefixedMatchesSelector = "matches" in Element.prototype ? "matches" : "webkitMatchesSelector" in Element.prototype ? "webkitMatchesSelector" : "mozMatchesSelector" in Element.prototype ? "mozMatchesSelector" : "oMatchesSelector" in Element.prototype ? "oMatchesSelector" : "msMatchesSelector";
            browser.pEventTypes = browser.supportsPointerEvent ? utils_domObjects.PointerEvent === window2.MSPointerEvent ? {
              up: "MSPointerUp",
              down: "MSPointerDown",
              over: "mouseover",
              out: "mouseout",
              move: "MSPointerMove",
              cancel: "MSPointerCancel"
            } : {
              up: "pointerup",
              down: "pointerdown",
              over: "pointerover",
              out: "pointerout",
              move: "pointermove",
              cancel: "pointercancel"
            } : null;
            browser.wheelEvent = "onmousewheel" in utils_domObjects.document ? "mousewheel" : "wheel";
          }
          var utils_browser = browser;
          const contains = (array2, target) => array2.indexOf(target) !== -1;
          const arr_remove = (array2, target) => array2.splice(array2.indexOf(target), 1);
          const merge = (target, source) => {
            for (const item of source) {
              target.push(item);
            }
            return target;
          };
          const from = (source) => merge([], source);
          const findIndex = (array2, func2) => {
            for (let i = 0; i < array2.length; i++) {
              if (func2(array2[i], i, array2)) {
                return i;
              }
            }
            return -1;
          };
          const find = (array2, func2) => array2[findIndex(array2, func2)];
          function clone(source) {
            const dest = {};
            for (const prop in source) {
              const value = source[prop];
              if (is.plainObject(value)) {
                dest[prop] = clone(value);
              } else if (is.array(value)) {
                dest[prop] = from(value);
              } else {
                dest[prop] = value;
              }
            }
            return dest;
          }
          function extend(dest, source) {
            for (const prop in source) {
              dest[prop] = source[prop];
            }
            const ret = dest;
            return ret;
          }
          let lastTime = 0;
          let request;
          let cancel;
          function raf_init(window2) {
            request = window2.requestAnimationFrame;
            cancel = window2.cancelAnimationFrame;
            if (!request) {
              const vendors = ["ms", "moz", "webkit", "o"];
              for (const vendor of vendors) {
                request = window2[`${vendor}RequestAnimationFrame`];
                cancel = window2[`${vendor}CancelAnimationFrame`] || window2[`${vendor}CancelRequestAnimationFrame`];
              }
            }
            request = request && request.bind(window2);
            cancel = cancel && cancel.bind(window2);
            if (!request) {
              request = (callback) => {
                const currTime = Date.now();
                const timeToCall = Math.max(0, 16 - (currTime - lastTime));
                const token = window2.setTimeout(() => {
                  callback(currTime + timeToCall);
                }, timeToCall);
                lastTime = currTime + timeToCall;
                return token;
              };
              cancel = (token) => clearTimeout(token);
            }
          }
          var raf = {
            request: (callback) => request(callback),
            cancel: (token) => cancel(token),
            init: raf_init
          };
          function normalize(type, listeners, result) {
            result = result || {};
            if (is.string(type) && type.search(" ") !== -1) {
              type = split(type);
            }
            if (is.array(type)) {
              return type.reduce((acc, t) => extend(acc, normalize(t, listeners, result)), result);
            }
            if (is.object(type)) {
              listeners = type;
              type = "";
            }
            if (is.func(listeners)) {
              result[type] = result[type] || [];
              result[type].push(listeners);
            } else if (is.array(listeners)) {
              for (const l of listeners) {
                normalize(type, l, result);
              }
            } else if (is.object(listeners)) {
              for (const prefix2 in listeners) {
                const combinedTypes = split(prefix2).map((p) => `${type}${p}`);
                normalize(combinedTypes, listeners[prefix2], result);
              }
            }
            return result;
          }
          function split(type) {
            return type.trim().split(/ +/);
          }
          function fireUntilImmediateStopped(event, listeners) {
            for (const listener of listeners) {
              if (event.immediatePropagationStopped) {
                break;
              }
              listener(event);
            }
          }
          class Eventable_Eventable {
            constructor(options) {
              this.options = void 0;
              this.types = {};
              this.propagationStopped = false;
              this.immediatePropagationStopped = false;
              this.global = void 0;
              this.options = extend({}, options || {});
            }
            fire(event) {
              let listeners;
              const global = this.global;
              if (listeners = this.types[event.type]) {
                fireUntilImmediateStopped(event, listeners);
              }
              if (!event.propagationStopped && global && (listeners = global[event.type])) {
                fireUntilImmediateStopped(event, listeners);
              }
            }
            on(type, listener) {
              const listeners = normalize(type, listener);
              for (type in listeners) {
                this.types[type] = merge(this.types[type] || [], listeners[type]);
              }
            }
            off(type, listener) {
              const listeners = normalize(type, listener);
              for (type in listeners) {
                const eventList = this.types[type];
                if (!eventList || !eventList.length) {
                  continue;
                }
                for (const subListener of listeners[type]) {
                  const index = eventList.indexOf(subListener);
                  if (index !== -1) {
                    eventList.splice(index, 1);
                  }
                }
              }
            }
            getRect(_element) {
              return null;
            }
          }
          function nodeContains(parent, child) {
            if (parent.contains) {
              return parent.contains(child);
            }
            while (child) {
              if (child === parent) {
                return true;
              }
              child = child.parentNode;
            }
            return false;
          }
          function domUtils_closest(element, selector) {
            while (is.element(element)) {
              if (matchesSelector(element, selector)) {
                return element;
              }
              element = parentNode(element);
            }
            return null;
          }
          function parentNode(node) {
            let parent = node.parentNode;
            if (is.docFrag(parent)) {
              while ((parent = parent.host) && is.docFrag(parent)) {
                continue;
              }
              return parent;
            }
            return parent;
          }
          function matchesSelector(element, selector) {
            if (win !== realWindow) {
              selector = selector.replace(/\/deep\//g, " ");
            }
            return element[utils_browser.prefixedMatchesSelector](selector);
          }
          const getParent = (el) => el.parentNode || el.host;
          function indexOfDeepestElement(elements) {
            let deepestNodeParents = [];
            let deepestNodeIndex;
            for (let i = 0; i < elements.length; i++) {
              const currentNode = elements[i];
              const deepestNode = elements[deepestNodeIndex];
              if (!currentNode || i === deepestNodeIndex) {
                continue;
              }
              if (!deepestNode) {
                deepestNodeIndex = i;
                continue;
              }
              const currentNodeParent = getParent(currentNode);
              const deepestNodeParent = getParent(deepestNode);
              if (currentNodeParent === currentNode.ownerDocument) {
                continue;
              } else if (deepestNodeParent === currentNode.ownerDocument) {
                deepestNodeIndex = i;
                continue;
              }
              if (currentNodeParent === deepestNodeParent) {
                if (zIndexIsHigherThan(currentNode, deepestNode)) {
                  deepestNodeIndex = i;
                }
                continue;
              }
              deepestNodeParents = deepestNodeParents.length ? deepestNodeParents : getNodeParents(deepestNode);
              let ancestryStart;
              if (deepestNode instanceof utils_domObjects.HTMLElement && currentNode instanceof utils_domObjects.SVGElement && !(currentNode instanceof utils_domObjects.SVGSVGElement)) {
                if (currentNode === deepestNodeParent) {
                  continue;
                }
                ancestryStart = currentNode.ownerSVGElement;
              } else {
                ancestryStart = currentNode;
              }
              const currentNodeParents = getNodeParents(ancestryStart, deepestNode.ownerDocument);
              let commonIndex = 0;
              while (currentNodeParents[commonIndex] && currentNodeParents[commonIndex] === deepestNodeParents[commonIndex]) {
                commonIndex++;
              }
              const parents = [currentNodeParents[commonIndex - 1], currentNodeParents[commonIndex], deepestNodeParents[commonIndex]];
              let child = parents[0].lastChild;
              while (child) {
                if (child === parents[1]) {
                  deepestNodeIndex = i;
                  deepestNodeParents = currentNodeParents;
                  break;
                } else if (child === parents[2]) {
                  break;
                }
                child = child.previousSibling;
              }
            }
            return deepestNodeIndex;
          }
          function getNodeParents(node, limit) {
            const parents = [];
            let parent = node;
            let parentParent;
            while ((parentParent = getParent(parent)) && parent !== limit && parentParent !== parent.ownerDocument) {
              parents.unshift(parent);
              parent = parentParent;
            }
            return parents;
          }
          function zIndexIsHigherThan(higherNode, lowerNode) {
            const higherIndex = parseInt(getWindow(higherNode).getComputedStyle(higherNode).zIndex, 10) || 0;
            const lowerIndex = parseInt(getWindow(lowerNode).getComputedStyle(lowerNode).zIndex, 10) || 0;
            return higherIndex >= lowerIndex;
          }
          function matchesUpTo(element, selector, limit) {
            while (is.element(element)) {
              if (matchesSelector(element, selector)) {
                return true;
              }
              element = parentNode(element);
              if (element === limit) {
                return matchesSelector(element, selector);
              }
            }
            return false;
          }
          function getActualElement(element) {
            return element.correspondingUseElement || element;
          }
          function getScrollXY(relevantWindow) {
            relevantWindow = relevantWindow || win;
            return {
              x: relevantWindow.scrollX || relevantWindow.document.documentElement.scrollLeft,
              y: relevantWindow.scrollY || relevantWindow.document.documentElement.scrollTop
            };
          }
          function getElementClientRect(element) {
            const clientRect = element instanceof utils_domObjects.SVGElement ? element.getBoundingClientRect() : element.getClientRects()[0];
            return clientRect && {
              left: clientRect.left,
              right: clientRect.right,
              top: clientRect.top,
              bottom: clientRect.bottom,
              width: clientRect.width || clientRect.right - clientRect.left,
              height: clientRect.height || clientRect.bottom - clientRect.top
            };
          }
          function getElementRect(element) {
            const clientRect = getElementClientRect(element);
            if (!utils_browser.isIOS7 && clientRect) {
              const scroll = getScrollXY(getWindow(element));
              clientRect.left += scroll.x;
              clientRect.right += scroll.x;
              clientRect.top += scroll.y;
              clientRect.bottom += scroll.y;
            }
            return clientRect;
          }
          function getPath(node) {
            const path = [];
            while (node) {
              path.push(node);
              node = parentNode(node);
            }
            return path;
          }
          function trySelector(value) {
            if (!is.string(value)) {
              return false;
            }
            utils_domObjects.document.querySelector(value);
            return true;
          }
          function getStringOptionResult(value, target, element) {
            if (value === "parent") {
              return parentNode(element);
            }
            if (value === "self") {
              return target.getRect(element);
            }
            return domUtils_closest(element, value);
          }
          function resolveRectLike(value, target, element, functionArgs) {
            let returnValue = value;
            if (is.string(returnValue)) {
              returnValue = getStringOptionResult(returnValue, target, element);
            } else if (is.func(returnValue)) {
              returnValue = returnValue(...functionArgs);
            }
            if (is.element(returnValue)) {
              returnValue = getElementRect(returnValue);
            }
            return returnValue;
          }
          function rectToXY(rect) {
            return rect && {
              x: "x" in rect ? rect.x : rect.left,
              y: "y" in rect ? rect.y : rect.top
            };
          }
          function xywhToTlbr(rect) {
            if (rect && !("left" in rect && "top" in rect)) {
              rect = extend({}, rect);
              rect.left = rect.x || 0;
              rect.top = rect.y || 0;
              rect.right = rect.right || rect.left + rect.width;
              rect.bottom = rect.bottom || rect.top + rect.height;
            }
            return rect;
          }
          function tlbrToXywh(rect) {
            if (rect && !("x" in rect && "y" in rect)) {
              rect = extend({}, rect);
              rect.x = rect.left || 0;
              rect.y = rect.top || 0;
              rect.width = rect.width || (rect.right || 0) - rect.x;
              rect.height = rect.height || (rect.bottom || 0) - rect.y;
            }
            return rect;
          }
          function addEdges(edges, rect, delta) {
            if (edges.left) {
              rect.left += delta.x;
            }
            if (edges.right) {
              rect.right += delta.x;
            }
            if (edges.top) {
              rect.top += delta.y;
            }
            if (edges.bottom) {
              rect.bottom += delta.y;
            }
            rect.width = rect.right - rect.left;
            rect.height = rect.bottom - rect.top;
          }
          var getOriginXY = function(target, element, actionName) {
            const actionOptions = target.options[actionName];
            const actionOrigin = actionOptions && actionOptions.origin;
            const origin = actionOrigin || target.options.origin;
            const originRect = resolveRectLike(origin, target, element, [target && element]);
            return rectToXY(originRect) || {
              x: 0,
              y: 0
            };
          };
          var hypot = (x, y) => Math.sqrt(x * x + y * y);
          class BaseEvent {
            constructor(interaction) {
              this.type = void 0;
              this.target = void 0;
              this.currentTarget = void 0;
              this.interactable = void 0;
              this._interaction = void 0;
              this.timeStamp = void 0;
              this.immediatePropagationStopped = false;
              this.propagationStopped = false;
              this._interaction = interaction;
            }
            preventDefault() {
            }
            /**
             * Don't call any other listeners (even on the current target)
             */
            stopPropagation() {
              this.propagationStopped = true;
            }
            /**
             * Don't call listeners on the remaining targets
             */
            stopImmediatePropagation() {
              this.immediatePropagationStopped = this.propagationStopped = true;
            }
          }
          Object.defineProperty(BaseEvent.prototype, "interaction", {
            get() {
              return this._interaction._proxy;
            },
            set() {
            }
          });
          const defaultOptions_defaults = {
            base: {
              preventDefault: "auto",
              deltaSource: "page"
            },
            perAction: {
              enabled: false,
              origin: {
                x: 0,
                y: 0
              }
            },
            actions: {}
          };
          class InteractEvent_InteractEvent extends BaseEvent {
            // resize
            /** */
            constructor(interaction, event, actionName, phase, element, preEnd, type) {
              super(interaction);
              this.target = void 0;
              this.currentTarget = void 0;
              this.relatedTarget = null;
              this.screenX = void 0;
              this.screenY = void 0;
              this.button = void 0;
              this.buttons = void 0;
              this.ctrlKey = void 0;
              this.shiftKey = void 0;
              this.altKey = void 0;
              this.metaKey = void 0;
              this.page = void 0;
              this.client = void 0;
              this.delta = void 0;
              this.rect = void 0;
              this.x0 = void 0;
              this.y0 = void 0;
              this.t0 = void 0;
              this.dt = void 0;
              this.duration = void 0;
              this.clientX0 = void 0;
              this.clientY0 = void 0;
              this.velocity = void 0;
              this.speed = void 0;
              this.swipe = void 0;
              this.timeStamp = void 0;
              this.axes = void 0;
              this.preEnd = void 0;
              element = element || interaction.element;
              const target = interaction.interactable;
              const deltaSource = (target && target.options || defaultOptions_defaults).deltaSource;
              const origin = getOriginXY(target, element, actionName);
              const starting = phase === "start";
              const ending = phase === "end";
              const prevEvent = starting ? this : interaction.prevEvent;
              const coords = starting ? interaction.coords.start : ending ? {
                page: prevEvent.page,
                client: prevEvent.client,
                timeStamp: interaction.coords.cur.timeStamp
              } : interaction.coords.cur;
              this.page = extend({}, coords.page);
              this.client = extend({}, coords.client);
              this.rect = extend({}, interaction.rect);
              this.timeStamp = coords.timeStamp;
              if (!ending) {
                this.page.x -= origin.x;
                this.page.y -= origin.y;
                this.client.x -= origin.x;
                this.client.y -= origin.y;
              }
              this.ctrlKey = event.ctrlKey;
              this.altKey = event.altKey;
              this.shiftKey = event.shiftKey;
              this.metaKey = event.metaKey;
              this.button = event.button;
              this.buttons = event.buttons;
              this.target = element;
              this.currentTarget = element;
              this.preEnd = preEnd;
              this.type = type || actionName + (phase || "");
              this.interactable = target;
              this.t0 = starting ? interaction.pointers[interaction.pointers.length - 1].downTime : prevEvent.t0;
              this.x0 = interaction.coords.start.page.x - origin.x;
              this.y0 = interaction.coords.start.page.y - origin.y;
              this.clientX0 = interaction.coords.start.client.x - origin.x;
              this.clientY0 = interaction.coords.start.client.y - origin.y;
              if (starting || ending) {
                this.delta = {
                  x: 0,
                  y: 0
                };
              } else {
                this.delta = {
                  x: this[deltaSource].x - prevEvent[deltaSource].x,
                  y: this[deltaSource].y - prevEvent[deltaSource].y
                };
              }
              this.dt = interaction.coords.delta.timeStamp;
              this.duration = this.timeStamp - this.t0;
              this.velocity = extend({}, interaction.coords.velocity[deltaSource]);
              this.speed = hypot(this.velocity.x, this.velocity.y);
              this.swipe = ending || phase === "inertiastart" ? this.getSwipe() : null;
            }
            getSwipe() {
              const interaction = this._interaction;
              if (interaction.prevEvent.speed < 600 || this.timeStamp - interaction.prevEvent.timeStamp > 150) {
                return null;
              }
              let angle = 180 * Math.atan2(interaction.prevEvent.velocityY, interaction.prevEvent.velocityX) / Math.PI;
              const overlap = 22.5;
              if (angle < 0) {
                angle += 360;
              }
              const left = 135 - overlap <= angle && angle < 225 + overlap;
              const up = 225 - overlap <= angle && angle < 315 + overlap;
              const right = !left && (315 - overlap <= angle || angle < 45 + overlap);
              const down = !up && 45 - overlap <= angle && angle < 135 + overlap;
              return {
                up,
                down,
                left,
                right,
                angle,
                speed: interaction.prevEvent.speed,
                velocity: {
                  x: interaction.prevEvent.velocityX,
                  y: interaction.prevEvent.velocityY
                }
              };
            }
            preventDefault() {
            }
            /**
             * Don't call listeners on the remaining targets
             */
            stopImmediatePropagation() {
              this.immediatePropagationStopped = this.propagationStopped = true;
            }
            /**
             * Don't call any other listeners (even on the current target)
             */
            stopPropagation() {
              this.propagationStopped = true;
            }
          }
          Object.defineProperties(InteractEvent_InteractEvent.prototype, {
            pageX: {
              get() {
                return this.page.x;
              },
              set(value) {
                this.page.x = value;
              }
            },
            pageY: {
              get() {
                return this.page.y;
              },
              set(value) {
                this.page.y = value;
              }
            },
            clientX: {
              get() {
                return this.client.x;
              },
              set(value) {
                this.client.x = value;
              }
            },
            clientY: {
              get() {
                return this.client.y;
              },
              set(value) {
                this.client.y = value;
              }
            },
            dx: {
              get() {
                return this.delta.x;
              },
              set(value) {
                this.delta.x = value;
              }
            },
            dy: {
              get() {
                return this.delta.y;
              },
              set(value) {
                this.delta.y = value;
              }
            },
            velocityX: {
              get() {
                return this.velocity.x;
              },
              set(value) {
                this.velocity.x = value;
              }
            },
            velocityY: {
              get() {
                return this.velocity.y;
              },
              set(value) {
                this.velocity.y = value;
              }
            }
          });
          function isNonNativeEvent(type, actions) {
            if (actions.phaselessTypes[type]) {
              return true;
            }
            for (const name in actions.map) {
              if (type.indexOf(name) === 0 && type.substr(name.length) in actions.phases) {
                return true;
              }
            }
            return false;
          }
          class Interactable_Interactable {
            /** @internal */
            get _defaults() {
              return {
                base: {},
                perAction: {},
                actions: {}
              };
            }
            /** */
            constructor(target, options, defaultContext, scopeEvents) {
              this.options = void 0;
              this._actions = void 0;
              this.target = void 0;
              this.events = new Eventable_Eventable();
              this._context = void 0;
              this._win = void 0;
              this._doc = void 0;
              this._scopeEvents = void 0;
              this._rectChecker = void 0;
              this._actions = options.actions;
              this.target = target;
              this._context = options.context || defaultContext;
              this._win = getWindow(trySelector(target) ? this._context : target);
              this._doc = this._win.document;
              this._scopeEvents = scopeEvents;
              this.set(options);
            }
            setOnEvents(actionName, phases) {
              if (is.func(phases.onstart)) {
                this.on(`${actionName}start`, phases.onstart);
              }
              if (is.func(phases.onmove)) {
                this.on(`${actionName}move`, phases.onmove);
              }
              if (is.func(phases.onend)) {
                this.on(`${actionName}end`, phases.onend);
              }
              if (is.func(phases.oninertiastart)) {
                this.on(`${actionName}inertiastart`, phases.oninertiastart);
              }
              return this;
            }
            updatePerActionListeners(actionName, prev, cur) {
              if (is.array(prev) || is.object(prev)) {
                this.off(actionName, prev);
              }
              if (is.array(cur) || is.object(cur)) {
                this.on(actionName, cur);
              }
            }
            setPerAction(actionName, options) {
              const defaults = this._defaults;
              for (const optionName_ in options) {
                const optionName = optionName_;
                const actionOptions = this.options[actionName];
                const optionValue = options[optionName];
                if (optionName === "listeners") {
                  this.updatePerActionListeners(actionName, actionOptions.listeners, optionValue);
                }
                if (is.array(optionValue)) {
                  actionOptions[optionName] = from(optionValue);
                } else if (is.plainObject(optionValue)) {
                  actionOptions[optionName] = extend(actionOptions[optionName] || {}, clone(optionValue));
                  if (is.object(defaults.perAction[optionName]) && "enabled" in defaults.perAction[optionName]) {
                    actionOptions[optionName].enabled = optionValue.enabled !== false;
                  }
                } else if (is.bool(optionValue) && is.object(defaults.perAction[optionName])) {
                  actionOptions[optionName].enabled = optionValue;
                } else {
                  actionOptions[optionName] = optionValue;
                }
              }
            }
            /**
             * The default function to get an Interactables bounding rect. Can be
             * overridden using {@link Interactable.rectChecker}.
             *
             * @param {Element} [element] The element to measure.
             * @return {Rect} The object's bounding rectangle.
             */
            getRect(element) {
              element = element || (is.element(this.target) ? this.target : null);
              if (is.string(this.target)) {
                element = element || this._context.querySelector(this.target);
              }
              return getElementRect(element);
            }
            /**
             * Returns or sets the function used to calculate the interactable's
             * element's rectangle
             *
             * @param {function} [checker] A function which returns this Interactable's
             * bounding rectangle. See {@link Interactable.getRect}
             * @return {function | object} The checker function or this Interactable
             */
            rectChecker(checker) {
              if (is.func(checker)) {
                this._rectChecker = checker;
                this.getRect = (element) => {
                  const rect = extend({}, this._rectChecker(element));
                  if (!("width" in rect)) {
                    rect.width = rect.right - rect.left;
                    rect.height = rect.bottom - rect.top;
                  }
                  return rect;
                };
                return this;
              }
              if (checker === null) {
                delete this.getRect;
                delete this._rectChecker;
                return this;
              }
              return this.getRect;
            }
            _backCompatOption(optionName, newValue) {
              if (trySelector(newValue) || is.object(newValue)) {
                this.options[optionName] = newValue;
                for (const action in this._actions.map) {
                  this.options[action][optionName] = newValue;
                }
                return this;
              }
              return this.options[optionName];
            }
            /**
             * Gets or sets the origin of the Interactable's element.  The x and y
             * of the origin will be subtracted from action event coordinates.
             *
             * @param {Element | object | string} [origin] An HTML or SVG Element whose
             * rect will be used, an object eg. { x: 0, y: 0 } or string 'parent', 'self'
             * or any CSS selector
             *
             * @return {object} The current origin or this Interactable
             */
            origin(newValue) {
              return this._backCompatOption("origin", newValue);
            }
            /**
             * Returns or sets the mouse coordinate types used to calculate the
             * movement of the pointer.
             *
             * @param {string} [newValue] Use 'client' if you will be scrolling while
             * interacting; Use 'page' if you want autoScroll to work
             * @return {string | object} The current deltaSource or this Interactable
             */
            deltaSource(newValue) {
              if (newValue === "page" || newValue === "client") {
                this.options.deltaSource = newValue;
                return this;
              }
              return this.options.deltaSource;
            }
            /**
             * Gets the selector context Node of the Interactable. The default is
             * `window.document`.
             *
             * @return {Node} The context Node of this Interactable
             */
            context() {
              return this._context;
            }
            inContext(element) {
              return this._context === element.ownerDocument || nodeContains(this._context, element);
            }
            testIgnoreAllow(options, targetNode, eventTarget) {
              return !this.testIgnore(options.ignoreFrom, targetNode, eventTarget) && this.testAllow(options.allowFrom, targetNode, eventTarget);
            }
            testAllow(allowFrom, targetNode, element) {
              if (!allowFrom) {
                return true;
              }
              if (!is.element(element)) {
                return false;
              }
              if (is.string(allowFrom)) {
                return matchesUpTo(element, allowFrom, targetNode);
              } else if (is.element(allowFrom)) {
                return nodeContains(allowFrom, element);
              }
              return false;
            }
            testIgnore(ignoreFrom, targetNode, element) {
              if (!ignoreFrom || !is.element(element)) {
                return false;
              }
              if (is.string(ignoreFrom)) {
                return matchesUpTo(element, ignoreFrom, targetNode);
              } else if (is.element(ignoreFrom)) {
                return nodeContains(ignoreFrom, element);
              }
              return false;
            }
            /**
             * Calls listeners for the given InteractEvent type bound globally
             * and directly to this Interactable
             *
             * @param {InteractEvent} iEvent The InteractEvent object to be fired on this
             * Interactable
             * @return {Interactable} this Interactable
             */
            fire(iEvent) {
              this.events.fire(iEvent);
              return this;
            }
            _onOff(method, typeArg, listenerArg, options) {
              if (is.object(typeArg) && !is.array(typeArg)) {
                options = listenerArg;
                listenerArg = null;
              }
              const addRemove = method === "on" ? "add" : "remove";
              const listeners = normalize(typeArg, listenerArg);
              for (let type in listeners) {
                if (type === "wheel") {
                  type = utils_browser.wheelEvent;
                }
                for (const listener of listeners[type]) {
                  if (isNonNativeEvent(type, this._actions)) {
                    this.events[method](type, listener);
                  } else if (is.string(this.target)) {
                    this._scopeEvents[`${addRemove}Delegate`](this.target, this._context, type, listener, options);
                  } else {
                    this._scopeEvents[addRemove](this.target, type, listener, options);
                  }
                }
              }
              return this;
            }
            /**
             * Binds a listener for an InteractEvent, pointerEvent or DOM event.
             *
             * @param {string | array | object} types The types of events to listen
             * for
             * @param {function | array | object} [listener] The event listener function(s)
             * @param {object | boolean} [options] options object or useCapture flag for
             * addEventListener
             * @return {Interactable} This Interactable
             */
            on(types, listener, options) {
              return this._onOff("on", types, listener, options);
            }
            /**
             * Removes an InteractEvent, pointerEvent or DOM event listener.
             *
             * @param {string | array | object} types The types of events that were
             * listened for
             * @param {function | array | object} [listener] The event listener function(s)
             * @param {object | boolean} [options] options object or useCapture flag for
             * removeEventListener
             * @return {Interactable} This Interactable
             */
            off(types, listener, options) {
              return this._onOff("off", types, listener, options);
            }
            /**
             * Reset the options of this Interactable
             *
             * @param {object} options The new settings to apply
             * @return {object} This Interactable
             */
            set(options) {
              const defaults = this._defaults;
              if (!is.object(options)) {
                options = {};
              }
              this.options = clone(defaults.base);
              for (const actionName_ in this._actions.methodDict) {
                const actionName = actionName_;
                const methodName = this._actions.methodDict[actionName];
                this.options[actionName] = {};
                this.setPerAction(actionName, extend(extend({}, defaults.perAction), defaults.actions[actionName]));
                this[methodName](options[actionName]);
              }
              for (const setting in options) {
                if (is.func(this[setting])) {
                  this[setting](options[setting]);
                }
              }
              return this;
            }
            /**
             * Remove this interactable from the list of interactables and remove it's
             * action capabilities and event listeners
             */
            unset() {
              if (is.string(this.target)) {
                for (const type in this._scopeEvents.delegatedEvents) {
                  const delegated = this._scopeEvents.delegatedEvents[type];
                  for (let i = delegated.length - 1; i >= 0; i--) {
                    const {
                      selector,
                      context,
                      listeners
                    } = delegated[i];
                    if (selector === this.target && context === this._context) {
                      delegated.splice(i, 1);
                    }
                    for (let l = listeners.length - 1; l >= 0; l--) {
                      this._scopeEvents.removeDelegate(this.target, this._context, type, listeners[l][0], listeners[l][1]);
                    }
                  }
                }
              } else {
                this._scopeEvents.remove(this.target, "all");
              }
            }
          }
          class InteractableSet_InteractableSet {
            // all set interactables
            constructor(scope) {
              this.list = [];
              this.selectorMap = {};
              this.scope = void 0;
              this.scope = scope;
              scope.addListeners({
                "interactable:unset": ({
                  interactable
                }) => {
                  const {
                    target,
                    _context: context
                  } = interactable;
                  const targetMappings = is.string(target) ? this.selectorMap[target] : target[this.scope.id];
                  const targetIndex = findIndex(targetMappings, (m) => m.context === context);
                  if (targetMappings[targetIndex]) {
                    targetMappings[targetIndex].context = null;
                    targetMappings[targetIndex].interactable = null;
                  }
                  targetMappings.splice(targetIndex, 1);
                }
              });
            }
            new(target, options) {
              options = extend(options || {}, {
                actions: this.scope.actions
              });
              const interactable = new this.scope.Interactable(target, options, this.scope.document, this.scope.events);
              const mappingInfo = {
                context: interactable._context,
                interactable
              };
              this.scope.addDocument(interactable._doc);
              this.list.push(interactable);
              if (is.string(target)) {
                if (!this.selectorMap[target]) {
                  this.selectorMap[target] = [];
                }
                this.selectorMap[target].push(mappingInfo);
              } else {
                if (!interactable.target[this.scope.id]) {
                  Object.defineProperty(target, this.scope.id, {
                    value: [],
                    configurable: true
                  });
                }
                target[this.scope.id].push(mappingInfo);
              }
              this.scope.fire("interactable:new", {
                target,
                options,
                interactable,
                win: this.scope._win
              });
              return interactable;
            }
            get(target, options) {
              const context = options && options.context || this.scope.document;
              const isSelector = is.string(target);
              const targetMappings = isSelector ? this.selectorMap[target] : target[this.scope.id];
              if (!targetMappings) {
                return null;
              }
              const found = find(targetMappings, (m) => m.context === context && (isSelector || m.interactable.inContext(target)));
              return found && found.interactable;
            }
            forEachMatch(node, callback) {
              for (const interactable of this.list) {
                let ret;
                if ((is.string(interactable.target) ? is.element(node) && matchesSelector(node, interactable.target) : (
                  // target is the element
                  node === interactable.target
                )) && // the element is in context
                interactable.inContext(node)) {
                  ret = callback(interactable);
                }
                if (ret !== void 0) {
                  return ret;
                }
              }
            }
          }
          function pointerExtend(dest, source) {
            for (const prop in source) {
              const prefixedPropREs = pointerExtend.prefixedPropREs;
              let deprecated = false;
              for (const vendor in prefixedPropREs) {
                if (prop.indexOf(vendor) === 0 && prefixedPropREs[vendor].test(prop)) {
                  deprecated = true;
                  break;
                }
              }
              if (!deprecated && typeof source[prop] !== "function") {
                dest[prop] = source[prop];
              }
            }
            return dest;
          }
          pointerExtend.prefixedPropREs = {
            webkit: /(Movement[XY]|Radius[XY]|RotationAngle|Force)$/,
            moz: /(Pressure)$/
          };
          var utils_pointerExtend = pointerExtend;
          function copyCoords(dest, src2) {
            dest.page = dest.page || {};
            dest.page.x = src2.page.x;
            dest.page.y = src2.page.y;
            dest.client = dest.client || {};
            dest.client.x = src2.client.x;
            dest.client.y = src2.client.y;
            dest.timeStamp = src2.timeStamp;
          }
          function setCoordDeltas(targetObj, prev, cur) {
            targetObj.page.x = cur.page.x - prev.page.x;
            targetObj.page.y = cur.page.y - prev.page.y;
            targetObj.client.x = cur.client.x - prev.client.x;
            targetObj.client.y = cur.client.y - prev.client.y;
            targetObj.timeStamp = cur.timeStamp - prev.timeStamp;
          }
          function setCoordVelocity(targetObj, delta) {
            const dt = Math.max(delta.timeStamp / 1e3, 1e-3);
            targetObj.page.x = delta.page.x / dt;
            targetObj.page.y = delta.page.y / dt;
            targetObj.client.x = delta.client.x / dt;
            targetObj.client.y = delta.client.y / dt;
            targetObj.timeStamp = dt;
          }
          function setZeroCoords(targetObj) {
            targetObj.page.x = 0;
            targetObj.page.y = 0;
            targetObj.client.x = 0;
            targetObj.client.y = 0;
          }
          function isNativePointer(pointer) {
            return pointer instanceof utils_domObjects.Event || pointer instanceof utils_domObjects.Touch;
          }
          function getXY(type, pointer, xy) {
            xy = xy || {};
            type = type || "page";
            xy.x = pointer[type + "X"];
            xy.y = pointer[type + "Y"];
            return xy;
          }
          function getPageXY(pointer, page) {
            page = page || {
              x: 0,
              y: 0
            };
            if (utils_browser.isOperaMobile && isNativePointer(pointer)) {
              getXY("screen", pointer, page);
              page.x += window.scrollX;
              page.y += window.scrollY;
            } else {
              getXY("page", pointer, page);
            }
            return page;
          }
          function getClientXY(pointer, client) {
            client = client || {};
            if (utils_browser.isOperaMobile && isNativePointer(pointer)) {
              getXY("screen", pointer, client);
            } else {
              getXY("client", pointer, client);
            }
            return client;
          }
          function getPointerId(pointer) {
            return is.number(pointer.pointerId) ? pointer.pointerId : pointer.identifier;
          }
          function setCoords(dest, pointers, timeStamp) {
            const pointer = pointers.length > 1 ? pointerAverage(pointers) : pointers[0];
            getPageXY(pointer, dest.page);
            getClientXY(pointer, dest.client);
            dest.timeStamp = timeStamp;
          }
          function getTouchPair(event) {
            const touches = [];
            if (is.array(event)) {
              touches[0] = event[0];
              touches[1] = event[1];
            } else {
              if (event.type === "touchend") {
                if (event.touches.length === 1) {
                  touches[0] = event.touches[0];
                  touches[1] = event.changedTouches[0];
                } else if (event.touches.length === 0) {
                  touches[0] = event.changedTouches[0];
                  touches[1] = event.changedTouches[1];
                }
              } else {
                touches[0] = event.touches[0];
                touches[1] = event.touches[1];
              }
            }
            return touches;
          }
          function pointerAverage(pointers) {
            const average = {
              pageX: 0,
              pageY: 0,
              clientX: 0,
              clientY: 0,
              screenX: 0,
              screenY: 0
            };
            for (const pointer of pointers) {
              for (const prop in average) {
                average[prop] += pointer[prop];
              }
            }
            for (const prop in average) {
              average[prop] /= pointers.length;
            }
            return average;
          }
          function touchBBox(event) {
            if (!event.length) {
              return null;
            }
            const touches = getTouchPair(event);
            const minX = Math.min(touches[0].pageX, touches[1].pageX);
            const minY = Math.min(touches[0].pageY, touches[1].pageY);
            const maxX = Math.max(touches[0].pageX, touches[1].pageX);
            const maxY = Math.max(touches[0].pageY, touches[1].pageY);
            return {
              x: minX,
              y: minY,
              left: minX,
              top: minY,
              right: maxX,
              bottom: maxY,
              width: maxX - minX,
              height: maxY - minY
            };
          }
          function touchDistance(event, deltaSource) {
            const sourceX = deltaSource + "X";
            const sourceY = deltaSource + "Y";
            const touches = getTouchPair(event);
            const dx = touches[0][sourceX] - touches[1][sourceX];
            const dy = touches[0][sourceY] - touches[1][sourceY];
            return hypot(dx, dy);
          }
          function touchAngle(event, deltaSource) {
            const sourceX = deltaSource + "X";
            const sourceY = deltaSource + "Y";
            const touches = getTouchPair(event);
            const dx = touches[1][sourceX] - touches[0][sourceX];
            const dy = touches[1][sourceY] - touches[0][sourceY];
            const angle = 180 * Math.atan2(dy, dx) / Math.PI;
            return angle;
          }
          function getPointerType(pointer) {
            return is.string(pointer.pointerType) ? pointer.pointerType : is.number(pointer.pointerType) ? [void 0, void 0, "touch", "pen", "mouse"][pointer.pointerType] : /touch/.test(pointer.type) || pointer instanceof utils_domObjects.Touch ? "touch" : "mouse";
          }
          function getEventTargets(event) {
            const path = is.func(event.composedPath) ? event.composedPath() : event.path;
            return [getActualElement(path ? path[0] : event.target), getActualElement(event.currentTarget)];
          }
          function newCoords() {
            return {
              page: {
                x: 0,
                y: 0
              },
              client: {
                x: 0,
                y: 0
              },
              timeStamp: 0
            };
          }
          function coordsToEvent(coords) {
            const event = {
              coords,
              get page() {
                return this.coords.page;
              },
              get client() {
                return this.coords.client;
              },
              get timeStamp() {
                return this.coords.timeStamp;
              },
              get pageX() {
                return this.coords.page.x;
              },
              get pageY() {
                return this.coords.page.y;
              },
              get clientX() {
                return this.coords.client.x;
              },
              get clientY() {
                return this.coords.client.y;
              },
              get pointerId() {
                return this.coords.pointerId;
              },
              get target() {
                return this.coords.target;
              },
              get type() {
                return this.coords.type;
              },
              get pointerType() {
                return this.coords.pointerType;
              },
              get buttons() {
                return this.coords.buttons;
              },
              preventDefault() {
              }
            };
            return event;
          }
          function events_install(scope) {
            const targets = [];
            const delegatedEvents = {};
            const documents = [];
            const eventsMethods = {
              add,
              remove,
              addDelegate,
              removeDelegate,
              delegateListener,
              delegateUseCapture,
              delegatedEvents,
              documents,
              targets,
              supportsOptions: false,
              supportsPassive: false
            };
            scope.document.createElement("div").addEventListener("test", null, {
              get capture() {
                return eventsMethods.supportsOptions = true;
              },
              get passive() {
                return eventsMethods.supportsPassive = true;
              }
            });
            scope.events = eventsMethods;
            function add(eventTarget, type, listener, optionalArg) {
              const options = getOptions(optionalArg);
              let target = find(targets, (t) => t.eventTarget === eventTarget);
              if (!target) {
                target = {
                  eventTarget,
                  events: {}
                };
                targets.push(target);
              }
              if (!target.events[type]) {
                target.events[type] = [];
              }
              if (eventTarget.addEventListener && !contains(target.events[type], listener)) {
                eventTarget.addEventListener(type, listener, eventsMethods.supportsOptions ? options : options.capture);
                target.events[type].push(listener);
              }
            }
            function remove(eventTarget, type, listener, optionalArg) {
              const options = getOptions(optionalArg);
              const targetIndex = findIndex(targets, (t) => t.eventTarget === eventTarget);
              const target = targets[targetIndex];
              if (!target || !target.events) {
                return;
              }
              if (type === "all") {
                for (type in target.events) {
                  if (target.events.hasOwnProperty(type)) {
                    remove(eventTarget, type, "all");
                  }
                }
                return;
              }
              let typeIsEmpty = false;
              const typeListeners = target.events[type];
              if (typeListeners) {
                if (listener === "all") {
                  for (let i = typeListeners.length - 1; i >= 0; i--) {
                    remove(eventTarget, type, typeListeners[i], options);
                  }
                  return;
                } else {
                  for (let i = 0; i < typeListeners.length; i++) {
                    if (typeListeners[i] === listener) {
                      eventTarget.removeEventListener(type, listener, eventsMethods.supportsOptions ? options : options.capture);
                      typeListeners.splice(i, 1);
                      if (typeListeners.length === 0) {
                        delete target.events[type];
                        typeIsEmpty = true;
                      }
                      break;
                    }
                  }
                }
              }
              if (typeIsEmpty && !Object.keys(target.events).length) {
                targets.splice(targetIndex, 1);
              }
            }
            function addDelegate(selector, context, type, listener, optionalArg) {
              const options = getOptions(optionalArg);
              if (!delegatedEvents[type]) {
                delegatedEvents[type] = [];
                for (const doc of documents) {
                  add(doc, type, delegateListener);
                  add(doc, type, delegateUseCapture, true);
                }
              }
              const delegates = delegatedEvents[type];
              let delegate = find(delegates, (d) => d.selector === selector && d.context === context);
              if (!delegate) {
                delegate = {
                  selector,
                  context,
                  listeners: []
                };
                delegates.push(delegate);
              }
              delegate.listeners.push([listener, options]);
            }
            function removeDelegate(selector, context, type, listener, optionalArg) {
              const options = getOptions(optionalArg);
              const delegates = delegatedEvents[type];
              let matchFound = false;
              let index;
              if (!delegates) {
                return;
              }
              for (index = delegates.length - 1; index >= 0; index--) {
                const cur = delegates[index];
                if (cur.selector === selector && cur.context === context) {
                  const {
                    listeners
                  } = cur;
                  for (let i = listeners.length - 1; i >= 0; i--) {
                    const [fn, {
                      capture,
                      passive
                    }] = listeners[i];
                    if (fn === listener && capture === options.capture && passive === options.passive) {
                      listeners.splice(i, 1);
                      if (!listeners.length) {
                        delegates.splice(index, 1);
                        remove(context, type, delegateListener);
                        remove(context, type, delegateUseCapture, true);
                      }
                      matchFound = true;
                      break;
                    }
                  }
                  if (matchFound) {
                    break;
                  }
                }
              }
            }
            function delegateListener(event, optionalArg) {
              const options = getOptions(optionalArg);
              const fakeEvent = new events_FakeEvent(event);
              const delegates = delegatedEvents[event.type];
              const [eventTarget] = getEventTargets(event);
              let element = eventTarget;
              while (is.element(element)) {
                for (let i = 0; i < delegates.length; i++) {
                  const cur = delegates[i];
                  const {
                    selector,
                    context
                  } = cur;
                  if (matchesSelector(element, selector) && nodeContains(context, eventTarget) && nodeContains(context, element)) {
                    const {
                      listeners
                    } = cur;
                    fakeEvent.currentTarget = element;
                    for (const [fn, {
                      capture,
                      passive
                    }] of listeners) {
                      if (capture === options.capture && passive === options.passive) {
                        fn(fakeEvent);
                      }
                    }
                  }
                }
                element = parentNode(element);
              }
            }
            function delegateUseCapture(event) {
              return delegateListener.call(this, event, true);
            }
            return eventsMethods;
          }
          class events_FakeEvent {
            constructor(originalEvent) {
              this.currentTarget = void 0;
              this.originalEvent = void 0;
              this.type = void 0;
              this.originalEvent = originalEvent;
              utils_pointerExtend(this, originalEvent);
            }
            preventOriginalDefault() {
              this.originalEvent.preventDefault();
            }
            stopPropagation() {
              this.originalEvent.stopPropagation();
            }
            stopImmediatePropagation() {
              this.originalEvent.stopImmediatePropagation();
            }
          }
          function getOptions(param) {
            if (!is.object(param)) {
              return {
                capture: !!param,
                passive: false
              };
            }
            const options = extend({}, param);
            options.capture = !!param.capture;
            options.passive = !!param.passive;
            return options;
          }
          var events = {
            id: "events",
            install: events_install
          };
          function warnOnce(method, message) {
            let warned = false;
            return function() {
              if (!warned) {
                win.console.warn(message);
                warned = true;
              }
              return method.apply(this, arguments);
            };
          }
          function copyAction(dest, src2) {
            dest.name = src2.name;
            dest.axis = src2.axis;
            dest.edges = src2.edges;
            return dest;
          }
          function createInteractStatic(scope) {
            const interact = (target, options) => {
              let interactable = scope.interactables.get(target, options);
              if (!interactable) {
                interactable = scope.interactables.new(target, options);
                interactable.events.global = interact.globalEvents;
              }
              return interactable;
            };
            interact.getPointerAverage = pointerAverage;
            interact.getTouchBBox = touchBBox;
            interact.getTouchDistance = touchDistance;
            interact.getTouchAngle = touchAngle;
            interact.getElementRect = getElementRect;
            interact.getElementClientRect = getElementClientRect;
            interact.matchesSelector = matchesSelector;
            interact.closest = domUtils_closest;
            interact.globalEvents = {};
            interact.version = "1.10.2";
            interact.scope = scope;
            interact.use = function(plugin, options) {
              this.scope.usePlugin(plugin, options);
              return this;
            };
            interact.isSet = function(target, options) {
              return !!this.scope.interactables.get(target, options && options.context);
            };
            interact.on = warnOnce(function on(type, listener, options) {
              if (is.string(type) && type.search(" ") !== -1) {
                type = type.trim().split(/ +/);
              }
              if (is.array(type)) {
                for (const eventType of type) {
                  this.on(eventType, listener, options);
                }
                return this;
              }
              if (is.object(type)) {
                for (const prop in type) {
                  this.on(prop, type[prop], listener);
                }
                return this;
              }
              if (isNonNativeEvent(type, this.scope.actions)) {
                if (!this.globalEvents[type]) {
                  this.globalEvents[type] = [listener];
                } else {
                  this.globalEvents[type].push(listener);
                }
              } else {
                this.scope.events.add(this.scope.document, type, listener, {
                  options
                });
              }
              return this;
            }, "The interact.on() method is being deprecated");
            interact.off = warnOnce(function off(type, listener, options) {
              if (is.string(type) && type.search(" ") !== -1) {
                type = type.trim().split(/ +/);
              }
              if (is.array(type)) {
                for (const eventType of type) {
                  this.off(eventType, listener, options);
                }
                return this;
              }
              if (is.object(type)) {
                for (const prop in type) {
                  this.off(prop, type[prop], listener);
                }
                return this;
              }
              if (isNonNativeEvent(type, this.scope.actions)) {
                let index;
                if (type in this.globalEvents && (index = this.globalEvents[type].indexOf(listener)) !== -1) {
                  this.globalEvents[type].splice(index, 1);
                }
              } else {
                this.scope.events.remove(this.scope.document, type, listener, options);
              }
              return this;
            }, "The interact.off() method is being deprecated");
            interact.debug = function() {
              return this.scope;
            };
            interact.supportsTouch = function() {
              return utils_browser.supportsTouch;
            };
            interact.supportsPointerEvent = function() {
              return utils_browser.supportsPointerEvent;
            };
            interact.stop = function() {
              for (const interaction of this.scope.interactions.list) {
                interaction.stop();
              }
              return this;
            };
            interact.pointerMoveTolerance = function(newValue) {
              if (is.number(newValue)) {
                this.scope.interactions.pointerMoveTolerance = newValue;
                return this;
              }
              return this.scope.interactions.pointerMoveTolerance;
            };
            interact.addDocument = function(doc, options) {
              this.scope.addDocument(doc, options);
            };
            interact.removeDocument = function(doc) {
              this.scope.removeDocument(doc);
            };
            return interact;
          }
          class PointerInfo {
            constructor(id, pointer, event, downTime, downTarget) {
              this.id = void 0;
              this.pointer = void 0;
              this.event = void 0;
              this.downTime = void 0;
              this.downTarget = void 0;
              this.id = id;
              this.pointer = pointer;
              this.event = event;
              this.downTime = downTime;
              this.downTarget = downTarget;
            }
          }
          let _ProxyValues;
          (function(_ProxyValues2) {
            _ProxyValues2["interactable"] = "";
            _ProxyValues2["element"] = "";
            _ProxyValues2["prepared"] = "";
            _ProxyValues2["pointerIsDown"] = "";
            _ProxyValues2["pointerWasMoved"] = "";
            _ProxyValues2["_proxy"] = "";
          })(_ProxyValues || (_ProxyValues = {}));
          let _ProxyMethods;
          (function(_ProxyMethods2) {
            _ProxyMethods2["start"] = "";
            _ProxyMethods2["move"] = "";
            _ProxyMethods2["end"] = "";
            _ProxyMethods2["stop"] = "";
            _ProxyMethods2["interacting"] = "";
          })(_ProxyMethods || (_ProxyMethods = {}));
          let idCounter = 0;
          class Interaction_Interaction {
            // current interactable being interacted with
            // the target element of the interactable
            // action that's ready to be fired on next move event
            // keep track of added pointers
            // pointerdown/mousedown/touchstart event
            // previous action event
            /** @internal */
            get pointerMoveTolerance() {
              return 1;
            }
            /**
             * @alias Interaction.prototype.move
             */
            /** */
            constructor({
              pointerType,
              scopeFire
            }) {
              this.interactable = null;
              this.element = null;
              this.rect = void 0;
              this._rects = void 0;
              this.edges = void 0;
              this._scopeFire = void 0;
              this.prepared = {
                name: null,
                axis: null,
                edges: null
              };
              this.pointerType = void 0;
              this.pointers = [];
              this.downEvent = null;
              this.downPointer = {};
              this._latestPointer = {
                pointer: null,
                event: null,
                eventTarget: null
              };
              this.prevEvent = null;
              this.pointerIsDown = false;
              this.pointerWasMoved = false;
              this._interacting = false;
              this._ending = false;
              this._stopped = true;
              this._proxy = null;
              this.simulation = null;
              this.doMove = warnOnce(function(signalArg) {
                this.move(signalArg);
              }, "The interaction.doMove() method has been renamed to interaction.move()");
              this.coords = {
                // Starting InteractEvent pointer coordinates
                start: newCoords(),
                // Previous native pointer move event coordinates
                prev: newCoords(),
                // current native pointer move event coordinates
                cur: newCoords(),
                // Change in coordinates and time of the pointer
                delta: newCoords(),
                // pointer velocity
                velocity: newCoords()
              };
              this._id = idCounter++;
              this._scopeFire = scopeFire;
              this.pointerType = pointerType;
              const that = this;
              this._proxy = {};
              for (const key in _ProxyValues) {
                Object.defineProperty(this._proxy, key, {
                  get() {
                    return that[key];
                  }
                });
              }
              for (const key in _ProxyMethods) {
                Object.defineProperty(this._proxy, key, {
                  value: (...args) => that[key](...args)
                });
              }
              this._scopeFire("interactions:new", {
                interaction: this
              });
            }
            pointerDown(pointer, event, eventTarget) {
              const pointerIndex = this.updatePointer(pointer, event, eventTarget, true);
              const pointerInfo = this.pointers[pointerIndex];
              this._scopeFire("interactions:down", {
                pointer,
                event,
                eventTarget,
                pointerIndex,
                pointerInfo,
                type: "down",
                interaction: this
              });
            }
            /**
             * ```js
             * interact(target)
             *   .draggable({
             *     // disable the default drag start by down->move
             *     manualStart: true
             *   })
             *   // start dragging after the user holds the pointer down
             *   .on('hold', function (event) {
             *     var interaction = event.interaction
             *
             *     if (!interaction.interacting()) {
             *       interaction.start({ name: 'drag' },
             *                         event.interactable,
             *                         event.currentTarget)
             *     }
             * })
             * ```
             *
             * Start an action with the given Interactable and Element as tartgets. The
             * action must be enabled for the target Interactable and an appropriate
             * number of pointers must be held down - 1 for drag/resize, 2 for gesture.
             *
             * Use it with `interactable.<action>able({ manualStart: false })` to always
             * [start actions manually](https://github.com/taye/interact.js/issues/114)
             *
             * @param {object} action   The action to be performed - drag, resize, etc.
             * @param {Interactable} target  The Interactable to target
             * @param {Element} element The DOM Element to target
             * @return {Boolean} Whether the interaction was successfully started
             */
            start(action, interactable, element) {
              if (this.interacting() || !this.pointerIsDown || this.pointers.length < (action.name === "gesture" ? 2 : 1) || !interactable.options[action.name].enabled) {
                return false;
              }
              copyAction(this.prepared, action);
              this.interactable = interactable;
              this.element = element;
              this.rect = interactable.getRect(element);
              this.edges = this.prepared.edges ? extend({}, this.prepared.edges) : {
                left: true,
                right: true,
                top: true,
                bottom: true
              };
              this._stopped = false;
              this._interacting = this._doPhase({
                interaction: this,
                event: this.downEvent,
                phase: "start"
              }) && !this._stopped;
              return this._interacting;
            }
            pointerMove(pointer, event, eventTarget) {
              if (!this.simulation && !(this.modification && this.modification.endResult)) {
                this.updatePointer(pointer, event, eventTarget, false);
              }
              const duplicateMove = this.coords.cur.page.x === this.coords.prev.page.x && this.coords.cur.page.y === this.coords.prev.page.y && this.coords.cur.client.x === this.coords.prev.client.x && this.coords.cur.client.y === this.coords.prev.client.y;
              let dx;
              let dy;
              if (this.pointerIsDown && !this.pointerWasMoved) {
                dx = this.coords.cur.client.x - this.coords.start.client.x;
                dy = this.coords.cur.client.y - this.coords.start.client.y;
                this.pointerWasMoved = hypot(dx, dy) > this.pointerMoveTolerance;
              }
              const pointerIndex = this.getPointerIndex(pointer);
              const signalArg = {
                pointer,
                pointerIndex,
                pointerInfo: this.pointers[pointerIndex],
                event,
                type: "move",
                eventTarget,
                dx,
                dy,
                duplicate: duplicateMove,
                interaction: this
              };
              if (!duplicateMove) {
                setCoordVelocity(this.coords.velocity, this.coords.delta);
              }
              this._scopeFire("interactions:move", signalArg);
              if (!duplicateMove && !this.simulation) {
                if (this.interacting()) {
                  signalArg.type = null;
                  this.move(signalArg);
                }
                if (this.pointerWasMoved) {
                  copyCoords(this.coords.prev, this.coords.cur);
                }
              }
            }
            /**
             * ```js
             * interact(target)
             *   .draggable(true)
             *   .on('dragmove', function (event) {
             *     if (someCondition) {
             *       // change the snap settings
             *       event.interactable.draggable({ snap: { targets: [] }})
             *       // fire another move event with re-calculated snap
             *       event.interaction.move()
             *     }
             *   })
             * ```
             *
             * Force a move of the current action at the same coordinates. Useful if
             * snap/restrict has been changed and you want a movement with the new
             * settings.
             */
            move(signalArg) {
              if (!signalArg || !signalArg.event) {
                setZeroCoords(this.coords.delta);
              }
              signalArg = extend({
                pointer: this._latestPointer.pointer,
                event: this._latestPointer.event,
                eventTarget: this._latestPointer.eventTarget,
                interaction: this
              }, signalArg || {});
              signalArg.phase = "move";
              this._doPhase(signalArg);
            }
            // End interact move events and stop auto-scroll unless simulation is running
            pointerUp(pointer, event, eventTarget, curEventTarget) {
              let pointerIndex = this.getPointerIndex(pointer);
              if (pointerIndex === -1) {
                pointerIndex = this.updatePointer(pointer, event, eventTarget, false);
              }
              const type = /cancel$/i.test(event.type) ? "cancel" : "up";
              this._scopeFire(`interactions:${type}`, {
                pointer,
                pointerIndex,
                pointerInfo: this.pointers[pointerIndex],
                event,
                eventTarget,
                type,
                curEventTarget,
                interaction: this
              });
              if (!this.simulation) {
                this.end(event);
              }
              this.removePointer(pointer, event);
            }
            documentBlur(event) {
              this.end(event);
              this._scopeFire("interactions:blur", {
                event,
                type: "blur",
                interaction: this
              });
            }
            /**
             * ```js
             * interact(target)
             *   .draggable(true)
             *   .on('move', function (event) {
             *     if (event.pageX > 1000) {
             *       // end the current action
             *       event.interaction.end()
             *       // stop all further listeners from being called
             *       event.stopImmediatePropagation()
             *     }
             *   })
             * ```
             *
             * @param {PointerEvent} [event]
             */
            end(event) {
              this._ending = true;
              event = event || this._latestPointer.event;
              let endPhaseResult;
              if (this.interacting()) {
                endPhaseResult = this._doPhase({
                  event,
                  interaction: this,
                  phase: "end"
                });
              }
              this._ending = false;
              if (endPhaseResult === true) {
                this.stop();
              }
            }
            currentAction() {
              return this._interacting ? this.prepared.name : null;
            }
            interacting() {
              return this._interacting;
            }
            /** */
            stop() {
              this._scopeFire("interactions:stop", {
                interaction: this
              });
              this.interactable = this.element = null;
              this._interacting = false;
              this._stopped = true;
              this.prepared.name = this.prevEvent = null;
            }
            getPointerIndex(pointer) {
              const pointerId = getPointerId(pointer);
              return this.pointerType === "mouse" || this.pointerType === "pen" ? this.pointers.length - 1 : findIndex(this.pointers, (curPointer) => curPointer.id === pointerId);
            }
            getPointerInfo(pointer) {
              return this.pointers[this.getPointerIndex(pointer)];
            }
            updatePointer(pointer, event, eventTarget, down) {
              const id = getPointerId(pointer);
              let pointerIndex = this.getPointerIndex(pointer);
              let pointerInfo = this.pointers[pointerIndex];
              down = down === false ? false : down || /(down|start)$/i.test(event.type);
              if (!pointerInfo) {
                pointerInfo = new PointerInfo(id, pointer, event, null, null);
                pointerIndex = this.pointers.length;
                this.pointers.push(pointerInfo);
              } else {
                pointerInfo.pointer = pointer;
              }
              setCoords(this.coords.cur, this.pointers.map((p) => p.pointer), this._now());
              setCoordDeltas(this.coords.delta, this.coords.prev, this.coords.cur);
              if (down) {
                this.pointerIsDown = true;
                pointerInfo.downTime = this.coords.cur.timeStamp;
                pointerInfo.downTarget = eventTarget;
                utils_pointerExtend(this.downPointer, pointer);
                if (!this.interacting()) {
                  copyCoords(this.coords.start, this.coords.cur);
                  copyCoords(this.coords.prev, this.coords.cur);
                  this.downEvent = event;
                  this.pointerWasMoved = false;
                }
              }
              this._updateLatestPointer(pointer, event, eventTarget);
              this._scopeFire("interactions:update-pointer", {
                pointer,
                event,
                eventTarget,
                down,
                pointerInfo,
                pointerIndex,
                interaction: this
              });
              return pointerIndex;
            }
            removePointer(pointer, event) {
              const pointerIndex = this.getPointerIndex(pointer);
              if (pointerIndex === -1) {
                return;
              }
              const pointerInfo = this.pointers[pointerIndex];
              this._scopeFire("interactions:remove-pointer", {
                pointer,
                event,
                eventTarget: null,
                pointerIndex,
                pointerInfo,
                interaction: this
              });
              this.pointers.splice(pointerIndex, 1);
              this.pointerIsDown = false;
            }
            _updateLatestPointer(pointer, event, eventTarget) {
              this._latestPointer.pointer = pointer;
              this._latestPointer.event = event;
              this._latestPointer.eventTarget = eventTarget;
            }
            destroy() {
              this._latestPointer.pointer = null;
              this._latestPointer.event = null;
              this._latestPointer.eventTarget = null;
            }
            _createPreparedEvent(event, phase, preEnd, type) {
              return new InteractEvent_InteractEvent(this, event, this.prepared.name, phase, this.element, preEnd, type);
            }
            _fireEvent(iEvent) {
              this.interactable.fire(iEvent);
              if (!this.prevEvent || iEvent.timeStamp >= this.prevEvent.timeStamp) {
                this.prevEvent = iEvent;
              }
            }
            _doPhase(signalArg) {
              const {
                event,
                phase,
                preEnd,
                type
              } = signalArg;
              const {
                rect
              } = this;
              if (rect && phase === "move") {
                addEdges(this.edges, rect, this.coords.delta[this.interactable.options.deltaSource]);
                rect.width = rect.right - rect.left;
                rect.height = rect.bottom - rect.top;
              }
              const beforeResult = this._scopeFire(`interactions:before-action-${phase}`, signalArg);
              if (beforeResult === false) {
                return false;
              }
              const iEvent = signalArg.iEvent = this._createPreparedEvent(event, phase, preEnd, type);
              this._scopeFire(`interactions:action-${phase}`, signalArg);
              if (phase === "start") {
                this.prevEvent = iEvent;
              }
              this._fireEvent(iEvent);
              this._scopeFire(`interactions:after-action-${phase}`, signalArg);
              return true;
            }
            _now() {
              return Date.now();
            }
          }
          var core_Interaction = Interaction_Interaction;
          function preventDefault(newValue) {
            if (/^(always|never|auto)$/.test(newValue)) {
              this.options.preventDefault = newValue;
              return this;
            }
            if (is.bool(newValue)) {
              this.options.preventDefault = newValue ? "always" : "never";
              return this;
            }
            return this.options.preventDefault;
          }
          function checkAndPreventDefault(interactable, scope, event) {
            const setting = interactable.options.preventDefault;
            if (setting === "never") {
              return;
            }
            if (setting === "always") {
              event.preventDefault();
              return;
            }
            if (scope.events.supportsPassive && /^touch(start|move)$/.test(event.type)) {
              const doc = getWindow(event.target).document;
              const docOptions = scope.getDocOptions(doc);
              if (!(docOptions && docOptions.events) || docOptions.events.passive !== false) {
                return;
              }
            }
            if (/^(mouse|pointer|touch)*(down|start)/i.test(event.type)) {
              return;
            }
            if (is.element(event.target) && matchesSelector(event.target, "input,select,textarea,[contenteditable=true],[contenteditable=true] *")) {
              return;
            }
            event.preventDefault();
          }
          function onInteractionEvent({
            interaction,
            event
          }) {
            if (interaction.interactable) {
              interaction.interactable.checkAndPreventDefault(event);
            }
          }
          function interactablePreventDefault_install(scope) {
            const {
              Interactable
            } = scope;
            Interactable.prototype.preventDefault = preventDefault;
            Interactable.prototype.checkAndPreventDefault = function(event) {
              return checkAndPreventDefault(this, scope, event);
            };
            scope.interactions.docEvents.push({
              type: "dragstart",
              listener(event) {
                for (const interaction of scope.interactions.list) {
                  if (interaction.element && (interaction.element === event.target || nodeContains(interaction.element, event.target))) {
                    interaction.interactable.checkAndPreventDefault(event);
                    return;
                  }
                }
              }
            });
          }
          var interactablePreventDefault = {
            id: "core/interactablePreventDefault",
            install: interactablePreventDefault_install,
            listeners: ["down", "move", "up", "cancel"].reduce((acc, eventType) => {
              acc[`interactions:${eventType}`] = onInteractionEvent;
              return acc;
            }, {})
          };
          const finder = {
            methodOrder: ["simulationResume", "mouseOrPen", "hasPointer", "idle"],
            search(details) {
              for (const method of finder.methodOrder) {
                const interaction = finder[method](details);
                if (interaction) {
                  return interaction;
                }
              }
              return null;
            },
            // try to resume simulation with a new pointer
            simulationResume({
              pointerType,
              eventType,
              eventTarget,
              scope
            }) {
              if (!/down|start/i.test(eventType)) {
                return null;
              }
              for (const interaction of scope.interactions.list) {
                let element = eventTarget;
                if (interaction.simulation && interaction.simulation.allowResume && interaction.pointerType === pointerType) {
                  while (element) {
                    if (element === interaction.element) {
                      return interaction;
                    }
                    element = parentNode(element);
                  }
                }
              }
              return null;
            },
            // if it's a mouse or pen interaction
            mouseOrPen({
              pointerId,
              pointerType,
              eventType,
              scope
            }) {
              if (pointerType !== "mouse" && pointerType !== "pen") {
                return null;
              }
              let firstNonActive;
              for (const interaction of scope.interactions.list) {
                if (interaction.pointerType === pointerType) {
                  if (interaction.simulation && !hasPointerId(interaction, pointerId)) {
                    continue;
                  }
                  if (interaction.interacting()) {
                    return interaction;
                  } else if (!firstNonActive) {
                    firstNonActive = interaction;
                  }
                }
              }
              if (firstNonActive) {
                return firstNonActive;
              }
              for (const interaction of scope.interactions.list) {
                if (interaction.pointerType === pointerType && !(/down/i.test(eventType) && interaction.simulation)) {
                  return interaction;
                }
              }
              return null;
            },
            // get interaction that has this pointer
            hasPointer({
              pointerId,
              scope
            }) {
              for (const interaction of scope.interactions.list) {
                if (hasPointerId(interaction, pointerId)) {
                  return interaction;
                }
              }
              return null;
            },
            // get first idle interaction with a matching pointerType
            idle({
              pointerType,
              scope
            }) {
              for (const interaction of scope.interactions.list) {
                if (interaction.pointers.length === 1) {
                  const target = interaction.interactable;
                  if (target && !(target.options.gesture && target.options.gesture.enabled)) {
                    continue;
                  }
                } else if (interaction.pointers.length >= 2) {
                  continue;
                }
                if (!interaction.interacting() && pointerType === interaction.pointerType) {
                  return interaction;
                }
              }
              return null;
            }
          };
          function hasPointerId(interaction, pointerId) {
            return interaction.pointers.some(({
              id
            }) => id === pointerId);
          }
          var interactionFinder = finder;
          const methodNames = ["pointerDown", "pointerMove", "pointerUp", "updatePointer", "removePointer", "windowBlur"];
          function interactions_install(scope) {
            const listeners = {};
            for (const method of methodNames) {
              listeners[method] = doOnInteractions(method, scope);
            }
            const pEventTypes = utils_browser.pEventTypes;
            let docEvents;
            if (utils_domObjects.PointerEvent) {
              docEvents = [{
                type: pEventTypes.down,
                listener: releasePointersOnRemovedEls
              }, {
                type: pEventTypes.down,
                listener: listeners.pointerDown
              }, {
                type: pEventTypes.move,
                listener: listeners.pointerMove
              }, {
                type: pEventTypes.up,
                listener: listeners.pointerUp
              }, {
                type: pEventTypes.cancel,
                listener: listeners.pointerUp
              }];
            } else {
              docEvents = [{
                type: "mousedown",
                listener: listeners.pointerDown
              }, {
                type: "mousemove",
                listener: listeners.pointerMove
              }, {
                type: "mouseup",
                listener: listeners.pointerUp
              }, {
                type: "touchstart",
                listener: releasePointersOnRemovedEls
              }, {
                type: "touchstart",
                listener: listeners.pointerDown
              }, {
                type: "touchmove",
                listener: listeners.pointerMove
              }, {
                type: "touchend",
                listener: listeners.pointerUp
              }, {
                type: "touchcancel",
                listener: listeners.pointerUp
              }];
            }
            docEvents.push({
              type: "blur",
              listener(event) {
                for (const interaction of scope.interactions.list) {
                  interaction.documentBlur(event);
                }
              }
            });
            scope.prevTouchTime = 0;
            scope.Interaction = class extends core_Interaction {
              get pointerMoveTolerance() {
                return scope.interactions.pointerMoveTolerance;
              }
              set pointerMoveTolerance(value) {
                scope.interactions.pointerMoveTolerance = value;
              }
              _now() {
                return scope.now();
              }
            };
            scope.interactions = {
              // all active and idle interactions
              list: [],
              new(options) {
                options.scopeFire = (name, arg) => scope.fire(name, arg);
                const interaction = new scope.Interaction(options);
                scope.interactions.list.push(interaction);
                return interaction;
              },
              listeners,
              docEvents,
              pointerMoveTolerance: 1
            };
            function releasePointersOnRemovedEls() {
              for (const interaction of scope.interactions.list) {
                if (!interaction.pointerIsDown || interaction.pointerType !== "touch" || interaction._interacting) {
                  continue;
                }
                for (const pointer of interaction.pointers) {
                  if (!scope.documents.some(({
                    doc
                  }) => nodeContains(doc, pointer.downTarget))) {
                    interaction.removePointer(pointer.pointer, pointer.event);
                  }
                }
              }
            }
            scope.usePlugin(interactablePreventDefault);
          }
          function doOnInteractions(method, scope) {
            return function(event) {
              const interactions = scope.interactions.list;
              const pointerType = getPointerType(event);
              const [eventTarget, curEventTarget] = getEventTargets(event);
              const matches = [];
              if (/^touch/.test(event.type)) {
                scope.prevTouchTime = scope.now();
                for (const changedTouch of event.changedTouches) {
                  const pointer = changedTouch;
                  const pointerId = getPointerId(pointer);
                  const searchDetails = {
                    pointer,
                    pointerId,
                    pointerType,
                    eventType: event.type,
                    eventTarget,
                    curEventTarget,
                    scope
                  };
                  const interaction = getInteraction(searchDetails);
                  matches.push([searchDetails.pointer, searchDetails.eventTarget, searchDetails.curEventTarget, interaction]);
                }
              } else {
                let invalidPointer = false;
                if (!utils_browser.supportsPointerEvent && /mouse/.test(event.type)) {
                  for (let i = 0; i < interactions.length && !invalidPointer; i++) {
                    invalidPointer = interactions[i].pointerType !== "mouse" && interactions[i].pointerIsDown;
                  }
                  invalidPointer = invalidPointer || scope.now() - scope.prevTouchTime < 500 || // on iOS and Firefox Mobile, MouseEvent.timeStamp is zero if simulated
                  event.timeStamp === 0;
                }
                if (!invalidPointer) {
                  const searchDetails = {
                    pointer: event,
                    pointerId: getPointerId(event),
                    pointerType,
                    eventType: event.type,
                    curEventTarget,
                    eventTarget,
                    scope
                  };
                  const interaction = getInteraction(searchDetails);
                  matches.push([searchDetails.pointer, searchDetails.eventTarget, searchDetails.curEventTarget, interaction]);
                }
              }
              for (const [pointer, eventTarget2, curEventTarget2, interaction] of matches) {
                interaction[method](pointer, event, eventTarget2, curEventTarget2);
              }
            };
          }
          function getInteraction(searchDetails) {
            const {
              pointerType,
              scope
            } = searchDetails;
            const foundInteraction = interactionFinder.search(searchDetails);
            const signalArg = {
              interaction: foundInteraction,
              searchDetails
            };
            scope.fire("interactions:find", signalArg);
            return signalArg.interaction || scope.interactions.new({
              pointerType
            });
          }
          function onDocSignal({
            doc,
            scope,
            options
          }, eventMethodName) {
            const {
              interactions: {
                docEvents
              },
              events: events2
            } = scope;
            const eventMethod = events2[eventMethodName];
            if (scope.browser.isIOS && !options.events) {
              options.events = {
                passive: false
              };
            }
            for (const eventType in events2.delegatedEvents) {
              eventMethod(doc, eventType, events2.delegateListener);
              eventMethod(doc, eventType, events2.delegateUseCapture, true);
            }
            const eventOptions = options && options.events;
            for (const {
              type,
              listener
            } of docEvents) {
              eventMethod(doc, type, listener, eventOptions);
            }
          }
          const interactions_interactions = {
            id: "core/interactions",
            install: interactions_install,
            listeners: {
              "scope:add-document": (arg) => onDocSignal(arg, "add"),
              "scope:remove-document": (arg) => onDocSignal(arg, "remove"),
              "interactable:unset": ({
                interactable
              }, scope) => {
                for (let i = scope.interactions.list.length - 1; i >= 0; i--) {
                  const interaction = scope.interactions.list[i];
                  if (interaction.interactable !== interactable) {
                    continue;
                  }
                  interaction.stop();
                  scope.fire("interactions:destroy", {
                    interaction
                  });
                  interaction.destroy();
                  if (scope.interactions.list.length > 2) {
                    scope.interactions.list.splice(i, 1);
                  }
                }
              }
            },
            onDocSignal,
            doOnInteractions,
            methodNames
          };
          var core_interactions = interactions_interactions;
          class scope_Scope {
            // main window
            // main document
            // main window
            // all documents being listened to
            constructor() {
              this.id = `__interact_scope_${Math.floor(Math.random() * 100)}`;
              this.isInitialized = false;
              this.listenerMaps = [];
              this.browser = utils_browser;
              this.defaults = clone(defaultOptions_defaults);
              this.Eventable = Eventable_Eventable;
              this.actions = {
                map: {},
                phases: {
                  start: true,
                  move: true,
                  end: true
                },
                methodDict: {},
                phaselessTypes: {}
              };
              this.interactStatic = createInteractStatic(this);
              this.InteractEvent = InteractEvent_InteractEvent;
              this.Interactable = void 0;
              this.interactables = new InteractableSet_InteractableSet(this);
              this._win = void 0;
              this.document = void 0;
              this.window = void 0;
              this.documents = [];
              this._plugins = {
                list: [],
                map: {}
              };
              this.onWindowUnload = (event) => this.removeDocument(event.target);
              const scope = this;
              this.Interactable = class extends Interactable_Interactable {
                get _defaults() {
                  return scope.defaults;
                }
                set(options) {
                  super.set(options);
                  scope.fire("interactable:set", {
                    options,
                    interactable: this
                  });
                  return this;
                }
                unset() {
                  super.unset();
                  scope.interactables.list.splice(scope.interactables.list.indexOf(this), 1);
                  scope.fire("interactable:unset", {
                    interactable: this
                  });
                }
              };
            }
            addListeners(map, id) {
              this.listenerMaps.push({
                id,
                map
              });
            }
            fire(name, arg) {
              for (const {
                map: {
                  [name]: listener
                }
              } of this.listenerMaps) {
                if (!!listener && listener(arg, this, name) === false) {
                  return false;
                }
              }
            }
            init(window2) {
              return this.isInitialized ? this : initScope(this, window2);
            }
            pluginIsInstalled(plugin) {
              return this._plugins.map[plugin.id] || this._plugins.list.indexOf(plugin) !== -1;
            }
            usePlugin(plugin, options) {
              if (!this.isInitialized) {
                return this;
              }
              if (this.pluginIsInstalled(plugin)) {
                return this;
              }
              if (plugin.id) {
                this._plugins.map[plugin.id] = plugin;
              }
              this._plugins.list.push(plugin);
              if (plugin.install) {
                plugin.install(this, options);
              }
              if (plugin.listeners && plugin.before) {
                let index = 0;
                const len = this.listenerMaps.length;
                const before = plugin.before.reduce((acc, id) => {
                  acc[id] = true;
                  acc[pluginIdRoot(id)] = true;
                  return acc;
                }, {});
                for (; index < len; index++) {
                  const otherId = this.listenerMaps[index].id;
                  if (before[otherId] || before[pluginIdRoot(otherId)]) {
                    break;
                  }
                }
                this.listenerMaps.splice(index, 0, {
                  id: plugin.id,
                  map: plugin.listeners
                });
              } else if (plugin.listeners) {
                this.listenerMaps.push({
                  id: plugin.id,
                  map: plugin.listeners
                });
              }
              return this;
            }
            addDocument(doc, options) {
              if (this.getDocIndex(doc) !== -1) {
                return false;
              }
              const window2 = getWindow(doc);
              options = options ? extend({}, options) : {};
              this.documents.push({
                doc,
                options
              });
              this.events.documents.push(doc);
              if (doc !== this.document) {
                this.events.add(window2, "unload", this.onWindowUnload);
              }
              this.fire("scope:add-document", {
                doc,
                window: window2,
                scope: this,
                options
              });
            }
            removeDocument(doc) {
              const index = this.getDocIndex(doc);
              const window2 = getWindow(doc);
              const options = this.documents[index].options;
              this.events.remove(window2, "unload", this.onWindowUnload);
              this.documents.splice(index, 1);
              this.events.documents.splice(index, 1);
              this.fire("scope:remove-document", {
                doc,
                window: window2,
                scope: this,
                options
              });
            }
            getDocIndex(doc) {
              for (let i = 0; i < this.documents.length; i++) {
                if (this.documents[i].doc === doc) {
                  return i;
                }
              }
              return -1;
            }
            getDocOptions(doc) {
              const docIndex = this.getDocIndex(doc);
              return docIndex === -1 ? null : this.documents[docIndex].options;
            }
            now() {
              return (this.window.Date || Date).now();
            }
          }
          function initScope(scope, window2) {
            scope.isInitialized = true;
            window_init(window2);
            utils_domObjects.init(window2);
            utils_browser.init(window2);
            raf.init(window2);
            scope.window = window2;
            scope.document = window2.document;
            scope.usePlugin(core_interactions);
            scope.usePlugin(events);
            return scope;
          }
          function pluginIdRoot(id) {
            return id && id.replace(/\/.*$/, "");
          }
          const interact_scope = new scope_Scope();
          const interact_interact = interact_scope.interactStatic;
          var _interactjs_interact = interact_interact;
          const interact_init = (win2) => interact_scope.init(win2);
          if (typeof window === "object" && !!window) {
            interact_init(window);
          }
          function InteractableMethods_install(scope) {
            const {
              /** @lends Interactable */
              Interactable
              // tslint:disable-line no-shadowed-variable
            } = scope;
            Interactable.prototype.getAction = function getAction(pointer, event, interaction, element) {
              const action = defaultActionChecker(this, event, interaction, element, scope);
              if (this.options.actionChecker) {
                return this.options.actionChecker(pointer, event, action, this, element, interaction);
              }
              return action;
            };
            Interactable.prototype.ignoreFrom = warnOnce(function(newValue) {
              return this._backCompatOption("ignoreFrom", newValue);
            }, "Interactable.ignoreFrom() has been deprecated. Use Interactble.draggable({ignoreFrom: newValue}).");
            Interactable.prototype.allowFrom = warnOnce(function(newValue) {
              return this._backCompatOption("allowFrom", newValue);
            }, "Interactable.allowFrom() has been deprecated. Use Interactble.draggable({allowFrom: newValue}).");
            Interactable.prototype.actionChecker = actionChecker;
            Interactable.prototype.styleCursor = styleCursor;
          }
          function defaultActionChecker(interactable, event, interaction, element, scope) {
            const rect = interactable.getRect(element);
            const buttons = event.buttons || {
              0: 1,
              1: 4,
              3: 8,
              4: 16
            }[event.button];
            const arg = {
              action: null,
              interactable,
              interaction,
              element,
              rect,
              buttons
            };
            scope.fire("auto-start:check", arg);
            return arg.action;
          }
          function styleCursor(newValue) {
            if (is.bool(newValue)) {
              this.options.styleCursor = newValue;
              return this;
            }
            if (newValue === null) {
              delete this.options.styleCursor;
              return this;
            }
            return this.options.styleCursor;
          }
          function actionChecker(checker) {
            if (is.func(checker)) {
              this.options.actionChecker = checker;
              return this;
            }
            if (checker === null) {
              delete this.options.actionChecker;
              return this;
            }
            return this.options.actionChecker;
          }
          var InteractableMethods = {
            id: "auto-start/interactableMethods",
            install: InteractableMethods_install
          };
          function base_install(scope) {
            const {
              interactStatic: interact,
              defaults
            } = scope;
            scope.usePlugin(InteractableMethods);
            defaults.base.actionChecker = null;
            defaults.base.styleCursor = true;
            extend(defaults.perAction, {
              manualStart: false,
              max: Infinity,
              maxPerElement: 1,
              allowFrom: null,
              ignoreFrom: null,
              // only allow left button by default
              // see https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons#Return_value
              mouseButtons: 1
            });
            interact.maxInteractions = (newValue) => maxInteractions(newValue, scope);
            scope.autoStart = {
              // Allow this many interactions to happen simultaneously
              maxInteractions: Infinity,
              withinInteractionLimit,
              cursorElement: null
            };
          }
          function prepareOnDown({
            interaction,
            pointer,
            event,
            eventTarget
          }, scope) {
            if (interaction.interacting()) {
              return;
            }
            const actionInfo = getActionInfo(interaction, pointer, event, eventTarget, scope);
            prepare(interaction, actionInfo, scope);
          }
          function prepareOnMove({
            interaction,
            pointer,
            event,
            eventTarget
          }, scope) {
            if (interaction.pointerType !== "mouse" || interaction.pointerIsDown || interaction.interacting()) {
              return;
            }
            const actionInfo = getActionInfo(interaction, pointer, event, eventTarget, scope);
            prepare(interaction, actionInfo, scope);
          }
          function startOnMove(arg, scope) {
            const {
              interaction
            } = arg;
            if (!interaction.pointerIsDown || interaction.interacting() || !interaction.pointerWasMoved || !interaction.prepared.name) {
              return;
            }
            scope.fire("autoStart:before-start", arg);
            const {
              interactable
            } = interaction;
            const actionName = interaction.prepared.name;
            if (actionName && interactable) {
              if (interactable.options[actionName].manualStart || !withinInteractionLimit(interactable, interaction.element, interaction.prepared, scope)) {
                interaction.stop();
              } else {
                interaction.start(interaction.prepared, interactable, interaction.element);
                setInteractionCursor(interaction, scope);
              }
            }
          }
          function clearCursorOnStop({
            interaction
          }, scope) {
            const {
              interactable
            } = interaction;
            if (interactable && interactable.options.styleCursor) {
              setCursor(interaction.element, "", scope);
            }
          }
          function validateAction(action, interactable, element, eventTarget, scope) {
            if (interactable.testIgnoreAllow(interactable.options[action.name], element, eventTarget) && interactable.options[action.name].enabled && withinInteractionLimit(interactable, element, action, scope)) {
              return action;
            }
            return null;
          }
          function validateMatches(interaction, pointer, event, matches, matchElements, eventTarget, scope) {
            for (let i = 0, len = matches.length; i < len; i++) {
              const match = matches[i];
              const matchElement = matchElements[i];
              const matchAction = match.getAction(pointer, event, interaction, matchElement);
              if (!matchAction) {
                continue;
              }
              const action = validateAction(matchAction, match, matchElement, eventTarget, scope);
              if (action) {
                return {
                  action,
                  interactable: match,
                  element: matchElement
                };
              }
            }
            return {
              action: null,
              interactable: null,
              element: null
            };
          }
          function getActionInfo(interaction, pointer, event, eventTarget, scope) {
            let matches = [];
            let matchElements = [];
            let element = eventTarget;
            function pushMatches(interactable) {
              matches.push(interactable);
              matchElements.push(element);
            }
            while (is.element(element)) {
              matches = [];
              matchElements = [];
              scope.interactables.forEachMatch(element, pushMatches);
              const actionInfo = validateMatches(interaction, pointer, event, matches, matchElements, eventTarget, scope);
              if (actionInfo.action && !actionInfo.interactable.options[actionInfo.action.name].manualStart) {
                return actionInfo;
              }
              element = parentNode(element);
            }
            return {
              action: null,
              interactable: null,
              element: null
            };
          }
          function prepare(interaction, {
            action,
            interactable,
            element
          }, scope) {
            action = action || {
              name: null
            };
            interaction.interactable = interactable;
            interaction.element = element;
            copyAction(interaction.prepared, action);
            interaction.rect = interactable && action.name ? interactable.getRect(element) : null;
            setInteractionCursor(interaction, scope);
            scope.fire("autoStart:prepared", {
              interaction
            });
          }
          function withinInteractionLimit(interactable, element, action, scope) {
            const options = interactable.options;
            const maxActions = options[action.name].max;
            const maxPerElement = options[action.name].maxPerElement;
            const autoStartMax = scope.autoStart.maxInteractions;
            let activeInteractions = 0;
            let interactableCount = 0;
            let elementCount = 0;
            if (!(maxActions && maxPerElement && autoStartMax)) {
              return false;
            }
            for (const interaction of scope.interactions.list) {
              const otherAction = interaction.prepared.name;
              if (!interaction.interacting()) {
                continue;
              }
              activeInteractions++;
              if (activeInteractions >= autoStartMax) {
                return false;
              }
              if (interaction.interactable !== interactable) {
                continue;
              }
              interactableCount += otherAction === action.name ? 1 : 0;
              if (interactableCount >= maxActions) {
                return false;
              }
              if (interaction.element === element) {
                elementCount++;
                if (otherAction === action.name && elementCount >= maxPerElement) {
                  return false;
                }
              }
            }
            return autoStartMax > 0;
          }
          function maxInteractions(newValue, scope) {
            if (is.number(newValue)) {
              scope.autoStart.maxInteractions = newValue;
              return this;
            }
            return scope.autoStart.maxInteractions;
          }
          function setCursor(element, cursor, scope) {
            const {
              cursorElement: prevCursorElement
            } = scope.autoStart;
            if (prevCursorElement && prevCursorElement !== element) {
              prevCursorElement.style.cursor = "";
            }
            element.ownerDocument.documentElement.style.cursor = cursor;
            element.style.cursor = cursor;
            scope.autoStart.cursorElement = cursor ? element : null;
          }
          function setInteractionCursor(interaction, scope) {
            const {
              interactable,
              element,
              prepared
            } = interaction;
            if (!(interaction.pointerType === "mouse" && interactable && interactable.options.styleCursor)) {
              if (scope.autoStart.cursorElement) {
                setCursor(scope.autoStart.cursorElement, "", scope);
              }
              return;
            }
            let cursor = "";
            if (prepared.name) {
              const cursorChecker = interactable.options[prepared.name].cursorChecker;
              if (is.func(cursorChecker)) {
                cursor = cursorChecker(prepared, interactable, element, interaction._interacting);
              } else {
                cursor = scope.actions.map[prepared.name].getCursor(prepared);
              }
            }
            setCursor(interaction.element, cursor || "", scope);
          }
          const autoStart = {
            id: "auto-start/base",
            before: ["actions"],
            install: base_install,
            listeners: {
              "interactions:down": prepareOnDown,
              "interactions:move": (arg, scope) => {
                prepareOnMove(arg, scope);
                startOnMove(arg, scope);
              },
              "interactions:stop": clearCursorOnStop
            },
            maxInteractions,
            withinInteractionLimit,
            validateAction
          };
          var base = autoStart;
          function beforeStart({
            interaction,
            eventTarget,
            dx,
            dy
          }, scope) {
            if (interaction.prepared.name !== "drag") {
              return;
            }
            const absX = Math.abs(dx);
            const absY = Math.abs(dy);
            const targetOptions = interaction.interactable.options.drag;
            const startAxis = targetOptions.startAxis;
            const currentAxis = absX > absY ? "x" : absX < absY ? "y" : "xy";
            interaction.prepared.axis = targetOptions.lockAxis === "start" ? currentAxis[0] : targetOptions.lockAxis;
            if (currentAxis !== "xy" && startAxis !== "xy" && startAxis !== currentAxis) {
              interaction.prepared.name = null;
              let element = eventTarget;
              const getDraggable = function(interactable) {
                if (interactable === interaction.interactable) {
                  return;
                }
                const options = interaction.interactable.options.drag;
                if (!options.manualStart && interactable.testIgnoreAllow(options, element, eventTarget)) {
                  const action = interactable.getAction(interaction.downPointer, interaction.downEvent, interaction, element);
                  if (action && action.name === "drag" && checkStartAxis(currentAxis, interactable) && base.validateAction(action, interactable, element, eventTarget, scope)) {
                    return interactable;
                  }
                }
              };
              while (is.element(element)) {
                const interactable = scope.interactables.forEachMatch(element, getDraggable);
                if (interactable) {
                  interaction.prepared.name = "drag";
                  interaction.interactable = interactable;
                  interaction.element = element;
                  break;
                }
                element = parentNode(element);
              }
            }
          }
          function checkStartAxis(startAxis, interactable) {
            if (!interactable) {
              return false;
            }
            const thisAxis = interactable.options.drag.startAxis;
            return startAxis === "xy" || thisAxis === "xy" || thisAxis === startAxis;
          }
          var dragAxis = {
            id: "auto-start/dragAxis",
            listeners: {
              "autoStart:before-start": beforeStart
            }
          };
          function hold_install(scope) {
            const {
              defaults
            } = scope;
            scope.usePlugin(base);
            defaults.perAction.hold = 0;
            defaults.perAction.delay = 0;
          }
          function getHoldDuration(interaction) {
            const actionName = interaction.prepared && interaction.prepared.name;
            if (!actionName) {
              return null;
            }
            const options = interaction.interactable.options;
            return options[actionName].hold || options[actionName].delay;
          }
          const hold = {
            id: "auto-start/hold",
            install: hold_install,
            listeners: {
              "interactions:new": ({
                interaction
              }) => {
                interaction.autoStartHoldTimer = null;
              },
              "autoStart:prepared": ({
                interaction
              }) => {
                const hold2 = getHoldDuration(interaction);
                if (hold2 > 0) {
                  interaction.autoStartHoldTimer = setTimeout(() => {
                    interaction.start(interaction.prepared, interaction.interactable, interaction.element);
                  }, hold2);
                }
              },
              "interactions:move": ({
                interaction,
                duplicate
              }) => {
                if (interaction.autoStartHoldTimer && interaction.pointerWasMoved && !duplicate) {
                  clearTimeout(interaction.autoStartHoldTimer);
                  interaction.autoStartHoldTimer = null;
                }
              },
              // prevent regular down->move autoStart
              "autoStart:before-start": ({
                interaction
              }) => {
                const holdDuration = getHoldDuration(interaction);
                if (holdDuration > 0) {
                  interaction.prepared.name = null;
                }
              }
            },
            getHoldDuration
          };
          var auto_start_hold = hold;
          var auto_start_plugin = {
            id: "auto-start",
            install(scope) {
              scope.usePlugin(base);
              scope.usePlugin(auto_start_hold);
              scope.usePlugin(dragAxis);
            }
          };
          if (typeof window === "object" && !!window) {
            interact_init(window);
          }
          _interactjs_interact.use(auto_start_plugin);
          function plugin_install(scope) {
            const {
              actions,
              Interactable,
              defaults
            } = scope;
            Interactable.prototype.draggable = drag.draggable;
            actions.map.drag = drag;
            actions.methodDict.drag = "draggable";
            defaults.actions.drag = drag.defaults;
          }
          function beforeMove({
            interaction
          }) {
            if (interaction.prepared.name !== "drag") {
              return;
            }
            const axis = interaction.prepared.axis;
            if (axis === "x") {
              interaction.coords.cur.page.y = interaction.coords.start.page.y;
              interaction.coords.cur.client.y = interaction.coords.start.client.y;
              interaction.coords.velocity.client.y = 0;
              interaction.coords.velocity.page.y = 0;
            } else if (axis === "y") {
              interaction.coords.cur.page.x = interaction.coords.start.page.x;
              interaction.coords.cur.client.x = interaction.coords.start.client.x;
              interaction.coords.velocity.client.x = 0;
              interaction.coords.velocity.page.x = 0;
            }
          }
          function move({
            iEvent,
            interaction
          }) {
            if (interaction.prepared.name !== "drag") {
              return;
            }
            const axis = interaction.prepared.axis;
            if (axis === "x" || axis === "y") {
              const opposite = axis === "x" ? "y" : "x";
              iEvent.page[opposite] = interaction.coords.start.page[opposite];
              iEvent.client[opposite] = interaction.coords.start.client[opposite];
              iEvent.delta[opposite] = 0;
            }
          }
          const plugin_draggable = function draggable(options) {
            if (is.object(options)) {
              this.options.drag.enabled = options.enabled !== false;
              this.setPerAction("drag", options);
              this.setOnEvents("drag", options);
              if (/^(xy|x|y|start)$/.test(options.lockAxis)) {
                this.options.drag.lockAxis = options.lockAxis;
              }
              if (/^(xy|x|y)$/.test(options.startAxis)) {
                this.options.drag.startAxis = options.startAxis;
              }
              return this;
            }
            if (is.bool(options)) {
              this.options.drag.enabled = options;
              return this;
            }
            return this.options.drag;
          };
          const drag = {
            id: "actions/drag",
            install: plugin_install,
            listeners: {
              "interactions:before-action-move": beforeMove,
              "interactions:action-resume": beforeMove,
              // dragmove
              "interactions:action-move": move,
              "auto-start:check": (arg) => {
                const {
                  interaction,
                  interactable,
                  buttons
                } = arg;
                const dragOptions = interactable.options.drag;
                if (!(dragOptions && dragOptions.enabled) || // check mouseButton setting if the pointer is down
                interaction.pointerIsDown && /mouse|pointer/.test(interaction.pointerType) && (buttons & interactable.options.drag.mouseButtons) === 0) {
                  return void 0;
                }
                arg.action = {
                  name: "drag",
                  axis: dragOptions.lockAxis === "start" ? dragOptions.startAxis : dragOptions.lockAxis
                };
                return false;
              }
            },
            draggable: plugin_draggable,
            beforeMove,
            move,
            defaults: {
              startAxis: "xy",
              lockAxis: "xy"
            },
            getCursor() {
              return "move";
            }
          };
          var drag_plugin = drag;
          if (typeof window === "object" && !!window) {
            interact_init(window);
          }
          _interactjs_interact.use(drag_plugin);
          function resize_plugin_install(scope) {
            const {
              actions,
              browser: browser2,
              /** @lends Interactable */
              Interactable,
              // tslint:disable-line no-shadowed-variable
              defaults
            } = scope;
            resize.cursors = initCursors(browser2);
            resize.defaultMargin = browser2.supportsTouch || browser2.supportsPointerEvent ? 20 : 10;
            Interactable.prototype.resizable = function(options) {
              return resizable(this, options, scope);
            };
            actions.map.resize = resize;
            actions.methodDict.resize = "resizable";
            defaults.actions.resize = resize.defaults;
          }
          function resizeChecker(arg) {
            const {
              interaction,
              interactable,
              element,
              rect,
              buttons
            } = arg;
            if (!rect) {
              return void 0;
            }
            const page = extend({}, interaction.coords.cur.page);
            const resizeOptions = interactable.options.resize;
            if (!(resizeOptions && resizeOptions.enabled) || // check mouseButton setting if the pointer is down
            interaction.pointerIsDown && /mouse|pointer/.test(interaction.pointerType) && (buttons & resizeOptions.mouseButtons) === 0) {
              return void 0;
            }
            if (is.object(resizeOptions.edges)) {
              const resizeEdges = {
                left: false,
                right: false,
                top: false,
                bottom: false
              };
              for (const edge in resizeEdges) {
                resizeEdges[edge] = checkResizeEdge(edge, resizeOptions.edges[edge], page, interaction._latestPointer.eventTarget, element, rect, resizeOptions.margin || resize.defaultMargin);
              }
              resizeEdges.left = resizeEdges.left && !resizeEdges.right;
              resizeEdges.top = resizeEdges.top && !resizeEdges.bottom;
              if (resizeEdges.left || resizeEdges.right || resizeEdges.top || resizeEdges.bottom) {
                arg.action = {
                  name: "resize",
                  edges: resizeEdges
                };
              }
            } else {
              const right = resizeOptions.axis !== "y" && page.x > rect.right - resize.defaultMargin;
              const bottom2 = resizeOptions.axis !== "x" && page.y > rect.bottom - resize.defaultMargin;
              if (right || bottom2) {
                arg.action = {
                  name: "resize",
                  axes: (right ? "x" : "") + (bottom2 ? "y" : "")
                };
              }
            }
            return arg.action ? false : void 0;
          }
          function resizable(interactable, options, scope) {
            if (is.object(options)) {
              interactable.options.resize.enabled = options.enabled !== false;
              interactable.setPerAction("resize", options);
              interactable.setOnEvents("resize", options);
              if (is.string(options.axis) && /^x$|^y$|^xy$/.test(options.axis)) {
                interactable.options.resize.axis = options.axis;
              } else if (options.axis === null) {
                interactable.options.resize.axis = scope.defaults.actions.resize.axis;
              }
              if (is.bool(options.preserveAspectRatio)) {
                interactable.options.resize.preserveAspectRatio = options.preserveAspectRatio;
              } else if (is.bool(options.square)) {
                interactable.options.resize.square = options.square;
              }
              return interactable;
            }
            if (is.bool(options)) {
              interactable.options.resize.enabled = options;
              return interactable;
            }
            return interactable.options.resize;
          }
          function checkResizeEdge(name, value, page, element, interactableElement, rect, margin) {
            if (!value) {
              return false;
            }
            if (value === true) {
              const width = is.number(rect.width) ? rect.width : rect.right - rect.left;
              const height = is.number(rect.height) ? rect.height : rect.bottom - rect.top;
              margin = Math.min(margin, Math.abs((name === "left" || name === "right" ? width : height) / 2));
              if (width < 0) {
                if (name === "left") {
                  name = "right";
                } else if (name === "right") {
                  name = "left";
                }
              }
              if (height < 0) {
                if (name === "top") {
                  name = "bottom";
                } else if (name === "bottom") {
                  name = "top";
                }
              }
              if (name === "left") {
                return page.x < (width >= 0 ? rect.left : rect.right) + margin;
              }
              if (name === "top") {
                return page.y < (height >= 0 ? rect.top : rect.bottom) + margin;
              }
              if (name === "right") {
                return page.x > (width >= 0 ? rect.right : rect.left) - margin;
              }
              if (name === "bottom") {
                return page.y > (height >= 0 ? rect.bottom : rect.top) - margin;
              }
            }
            if (!is.element(element)) {
              return false;
            }
            return is.element(value) ? value === element : matchesUpTo(element, value, interactableElement);
          }
          function initCursors(browser2) {
            return browser2.isIe9 ? {
              x: "e-resize",
              y: "s-resize",
              xy: "se-resize",
              top: "n-resize",
              left: "w-resize",
              bottom: "s-resize",
              right: "e-resize",
              topleft: "se-resize",
              bottomright: "se-resize",
              topright: "ne-resize",
              bottomleft: "ne-resize"
            } : {
              x: "ew-resize",
              y: "ns-resize",
              xy: "nwse-resize",
              top: "ns-resize",
              left: "ew-resize",
              bottom: "ns-resize",
              right: "ew-resize",
              topleft: "nwse-resize",
              bottomright: "nwse-resize",
              topright: "nesw-resize",
              bottomleft: "nesw-resize"
            };
          }
          function start({
            iEvent,
            interaction
          }) {
            if (interaction.prepared.name !== "resize" || !interaction.prepared.edges) {
              return;
            }
            const resizeEvent = iEvent;
            const rect = interaction.rect;
            interaction._rects = {
              start: extend({}, rect),
              corrected: extend({}, rect),
              previous: extend({}, rect),
              delta: {
                left: 0,
                right: 0,
                width: 0,
                top: 0,
                bottom: 0,
                height: 0
              }
            };
            resizeEvent.edges = interaction.prepared.edges;
            resizeEvent.rect = interaction._rects.corrected;
            resizeEvent.deltaRect = interaction._rects.delta;
          }
          function plugin_move({
            iEvent,
            interaction
          }) {
            if (interaction.prepared.name !== "resize" || !interaction.prepared.edges) {
              return;
            }
            const resizeEvent = iEvent;
            const resizeOptions = interaction.interactable.options.resize;
            const invert = resizeOptions.invert;
            const invertible = invert === "reposition" || invert === "negate";
            const current = interaction.rect;
            const {
              start: startRect,
              corrected,
              delta: deltaRect,
              previous
            } = interaction._rects;
            extend(previous, corrected);
            if (invertible) {
              extend(corrected, current);
              if (invert === "reposition") {
                if (corrected.top > corrected.bottom) {
                  const swap = corrected.top;
                  corrected.top = corrected.bottom;
                  corrected.bottom = swap;
                }
                if (corrected.left > corrected.right) {
                  const swap = corrected.left;
                  corrected.left = corrected.right;
                  corrected.right = swap;
                }
              }
            } else {
              corrected.top = Math.min(current.top, startRect.bottom);
              corrected.bottom = Math.max(current.bottom, startRect.top);
              corrected.left = Math.min(current.left, startRect.right);
              corrected.right = Math.max(current.right, startRect.left);
            }
            corrected.width = corrected.right - corrected.left;
            corrected.height = corrected.bottom - corrected.top;
            for (const edge in corrected) {
              deltaRect[edge] = corrected[edge] - previous[edge];
            }
            resizeEvent.edges = interaction.prepared.edges;
            resizeEvent.rect = corrected;
            resizeEvent.deltaRect = deltaRect;
          }
          function end({
            iEvent,
            interaction
          }) {
            if (interaction.prepared.name !== "resize" || !interaction.prepared.edges) {
              return;
            }
            const resizeEvent = iEvent;
            resizeEvent.edges = interaction.prepared.edges;
            resizeEvent.rect = interaction._rects.corrected;
            resizeEvent.deltaRect = interaction._rects.delta;
          }
          function updateEventAxes({
            iEvent,
            interaction
          }) {
            if (interaction.prepared.name !== "resize" || !interaction.resizeAxes) {
              return;
            }
            const options = interaction.interactable.options;
            const resizeEvent = iEvent;
            if (options.resize.square) {
              if (interaction.resizeAxes === "y") {
                resizeEvent.delta.x = resizeEvent.delta.y;
              } else {
                resizeEvent.delta.y = resizeEvent.delta.x;
              }
              resizeEvent.axes = "xy";
            } else {
              resizeEvent.axes = interaction.resizeAxes;
              if (interaction.resizeAxes === "x") {
                resizeEvent.delta.y = 0;
              } else if (interaction.resizeAxes === "y") {
                resizeEvent.delta.x = 0;
              }
            }
          }
          const resize = {
            id: "actions/resize",
            before: ["actions/drag"],
            install: resize_plugin_install,
            listeners: {
              "interactions:new": ({
                interaction
              }) => {
                interaction.resizeAxes = "xy";
              },
              "interactions:action-start": (arg) => {
                start(arg);
                updateEventAxes(arg);
              },
              "interactions:action-move": (arg) => {
                plugin_move(arg);
                updateEventAxes(arg);
              },
              "interactions:action-end": end,
              "auto-start:check": resizeChecker
            },
            defaults: {
              square: false,
              preserveAspectRatio: false,
              axis: "xy",
              // use default margin
              margin: NaN,
              // object with props left, right, top, bottom which are
              // true/false values to resize when the pointer is over that edge,
              // CSS selectors to match the handles for each direction
              // or the Elements for each handle
              edges: null,
              // a value of 'none' will limit the resize rect to a minimum of 0x0
              // 'negate' will alow the rect to have negative width/height
              // 'reposition' will keep the width/height positive by swapping
              // the top and bottom edges and/or swapping the left and right edges
              invert: "none"
            },
            cursors: null,
            getCursor({
              edges,
              axis,
              name
            }) {
              const cursors = resize.cursors;
              let result = null;
              if (axis) {
                result = cursors[name + axis];
              } else if (edges) {
                let cursorKey = "";
                for (const edge of ["top", "bottom", "left", "right"]) {
                  if (edges[edge]) {
                    cursorKey += edge;
                  }
                }
                result = cursors[cursorKey];
              }
              return result;
            },
            defaultMargin: null
          };
          var resize_plugin = resize;
          if (typeof window === "object" && !!window) {
            interact_init(window);
          }
          _interactjs_interact.use(resize_plugin);
          var edgeTarget = () => {
          };
          var snappers_elements = () => {
          };
          var grid = (grid2) => {
            const coordFields = [["x", "y"], ["left", "top"], ["right", "bottom"], ["width", "height"]].filter(([xField, yField]) => xField in grid2 || yField in grid2);
            const gridFunc = (x, y) => {
              const {
                range,
                limits = {
                  left: -Infinity,
                  right: Infinity,
                  top: -Infinity,
                  bottom: Infinity
                },
                offset = {
                  x: 0,
                  y: 0
                }
              } = grid2;
              const result = {
                range,
                grid: grid2,
                x: null,
                y: null
              };
              for (const [xField, yField] of coordFields) {
                const gridx = Math.round((x - offset.x) / grid2[xField]);
                const gridy = Math.round((y - offset.y) / grid2[yField]);
                result[xField] = Math.max(limits.left, Math.min(limits.right, gridx * grid2[xField] + offset.x));
                result[yField] = Math.max(limits.top, Math.min(limits.bottom, gridy * grid2[yField] + offset.y));
              }
              return result;
            };
            gridFunc.grid = grid2;
            gridFunc.coordFields = coordFields;
            return gridFunc;
          };
          const snappersPlugin = {
            id: "snappers",
            install(scope) {
              const {
                interactStatic: interact
              } = scope;
              interact.snappers = extend(interact.snappers || {}, all_namespaceObject);
              interact.createSnapGrid = interact.snappers.grid;
            }
          };
          var snappers_plugin = snappersPlugin;
          class Modification_Modification {
            constructor(interaction) {
              this.states = [];
              this.startOffset = {
                left: 0,
                right: 0,
                top: 0,
                bottom: 0
              };
              this.startDelta = null;
              this.result = null;
              this.endResult = null;
              this.edges = void 0;
              this.interaction = void 0;
              this.interaction = interaction;
              this.result = createResult();
            }
            start({
              phase
            }, pageCoords) {
              const {
                interaction
              } = this;
              const modifierList = getModifierList(interaction);
              this.prepareStates(modifierList);
              this.edges = extend({}, interaction.edges);
              this.startOffset = getRectOffset(interaction.rect, pageCoords);
              this.startDelta = {
                x: 0,
                y: 0
              };
              const arg = {
                phase,
                pageCoords,
                preEnd: false
              };
              this.result = createResult();
              this.startAll(arg);
              const result = this.result = this.setAll(arg);
              return result;
            }
            fillArg(arg) {
              const {
                interaction
              } = this;
              arg.interaction = interaction;
              arg.interactable = interaction.interactable;
              arg.element = interaction.element;
              arg.rect = arg.rect || interaction.rect;
              arg.edges = this.edges;
              arg.startOffset = this.startOffset;
            }
            startAll(arg) {
              this.fillArg(arg);
              for (const state of this.states) {
                if (state.methods.start) {
                  arg.state = state;
                  state.methods.start(arg);
                }
              }
            }
            setAll(arg) {
              this.fillArg(arg);
              const {
                phase,
                preEnd,
                skipModifiers,
                rect: unmodifiedRect
              } = arg;
              arg.coords = extend({}, arg.pageCoords);
              arg.rect = extend({}, unmodifiedRect);
              const states = skipModifiers ? this.states.slice(skipModifiers) : this.states;
              const newResult = createResult(arg.coords, arg.rect);
              for (const state of states) {
                const {
                  options
                } = state;
                const lastModifierCoords = extend({}, arg.coords);
                let returnValue = null;
                if (state.methods.set && this.shouldDo(options, preEnd, phase)) {
                  arg.state = state;
                  returnValue = state.methods.set(arg);
                  addEdges(this.interaction.edges, arg.rect, {
                    x: arg.coords.x - lastModifierCoords.x,
                    y: arg.coords.y - lastModifierCoords.y
                  });
                }
                newResult.eventProps.push(returnValue);
              }
              newResult.delta.x = arg.coords.x - arg.pageCoords.x;
              newResult.delta.y = arg.coords.y - arg.pageCoords.y;
              newResult.rectDelta.left = arg.rect.left - unmodifiedRect.left;
              newResult.rectDelta.right = arg.rect.right - unmodifiedRect.right;
              newResult.rectDelta.top = arg.rect.top - unmodifiedRect.top;
              newResult.rectDelta.bottom = arg.rect.bottom - unmodifiedRect.bottom;
              const prevCoords = this.result.coords;
              const prevRect = this.result.rect;
              if (prevCoords && prevRect) {
                const rectChanged = newResult.rect.left !== prevRect.left || newResult.rect.right !== prevRect.right || newResult.rect.top !== prevRect.top || newResult.rect.bottom !== prevRect.bottom;
                newResult.changed = rectChanged || prevCoords.x !== newResult.coords.x || prevCoords.y !== newResult.coords.y;
              }
              return newResult;
            }
            applyToInteraction(arg) {
              const {
                interaction
              } = this;
              const {
                phase
              } = arg;
              const curCoords = interaction.coords.cur;
              const startCoords = interaction.coords.start;
              const {
                result,
                startDelta
              } = this;
              const curDelta = result.delta;
              if (phase === "start") {
                extend(this.startDelta, result.delta);
              }
              for (const [coordsSet, delta] of [[startCoords, startDelta], [curCoords, curDelta]]) {
                coordsSet.page.x += delta.x;
                coordsSet.page.y += delta.y;
                coordsSet.client.x += delta.x;
                coordsSet.client.y += delta.y;
              }
              const {
                rectDelta
              } = this.result;
              const rect = arg.rect || interaction.rect;
              rect.left += rectDelta.left;
              rect.right += rectDelta.right;
              rect.top += rectDelta.top;
              rect.bottom += rectDelta.bottom;
              rect.width = rect.right - rect.left;
              rect.height = rect.bottom - rect.top;
            }
            setAndApply(arg) {
              const {
                interaction
              } = this;
              const {
                phase,
                preEnd,
                skipModifiers
              } = arg;
              const result = this.setAll({
                preEnd,
                phase,
                pageCoords: arg.modifiedCoords || interaction.coords.cur.page
              });
              this.result = result;
              if (!result.changed && (!skipModifiers || skipModifiers < this.states.length) && interaction.interacting()) {
                return false;
              }
              if (arg.modifiedCoords) {
                const {
                  page
                } = interaction.coords.cur;
                const adjustment = {
                  x: arg.modifiedCoords.x - page.x,
                  y: arg.modifiedCoords.y - page.y
                };
                result.coords.x += adjustment.x;
                result.coords.y += adjustment.y;
                result.delta.x += adjustment.x;
                result.delta.y += adjustment.y;
              }
              this.applyToInteraction(arg);
            }
            beforeEnd(arg) {
              const {
                interaction,
                event
              } = arg;
              const states = this.states;
              if (!states || !states.length) {
                return;
              }
              let doPreend = false;
              for (const state of states) {
                arg.state = state;
                const {
                  options,
                  methods
                } = state;
                const endPosition = methods.beforeEnd && methods.beforeEnd(arg);
                if (endPosition) {
                  this.endResult = endPosition;
                  return false;
                }
                doPreend = doPreend || !doPreend && this.shouldDo(options, true, arg.phase, true);
              }
              if (doPreend) {
                interaction.move({
                  event,
                  preEnd: true
                });
              }
            }
            stop(arg) {
              const {
                interaction
              } = arg;
              if (!this.states || !this.states.length) {
                return;
              }
              const modifierArg = extend({
                states: this.states,
                interactable: interaction.interactable,
                element: interaction.element,
                rect: null
              }, arg);
              this.fillArg(modifierArg);
              for (const state of this.states) {
                modifierArg.state = state;
                if (state.methods.stop) {
                  state.methods.stop(modifierArg);
                }
              }
              this.states = null;
              this.endResult = null;
            }
            prepareStates(modifierList) {
              this.states = [];
              for (let index = 0; index < modifierList.length; index++) {
                const {
                  options,
                  methods,
                  name
                } = modifierList[index];
                this.states.push({
                  options,
                  methods,
                  index,
                  name
                });
              }
              return this.states;
            }
            restoreInteractionCoords({
              interaction: {
                coords,
                rect,
                modification
              }
            }) {
              if (!modification.result) {
                return;
              }
              const {
                startDelta
              } = modification;
              const {
                delta: curDelta,
                rectDelta
              } = modification.result;
              const coordsAndDeltas = [[coords.start, startDelta], [coords.cur, curDelta]];
              for (const [coordsSet, delta] of coordsAndDeltas) {
                coordsSet.page.x -= delta.x;
                coordsSet.page.y -= delta.y;
                coordsSet.client.x -= delta.x;
                coordsSet.client.y -= delta.y;
              }
              rect.left -= rectDelta.left;
              rect.right -= rectDelta.right;
              rect.top -= rectDelta.top;
              rect.bottom -= rectDelta.bottom;
            }
            shouldDo(options, preEnd, phase, requireEndOnly) {
              if (
                // ignore disabled modifiers
                !options || options.enabled === false || // check if we require endOnly option to fire move before end
                requireEndOnly && !options.endOnly || // don't apply endOnly modifiers when not ending
                options.endOnly && !preEnd || // check if modifier should run be applied on start
                phase === "start" && !options.setStart
              ) {
                return false;
              }
              return true;
            }
            copyFrom(other) {
              this.startOffset = other.startOffset;
              this.startDelta = other.startDelta;
              this.edges = other.edges;
              this.states = other.states.map((s) => clone(s));
              this.result = createResult(extend({}, other.result.coords), extend({}, other.result.rect));
            }
            destroy() {
              for (const prop in this) {
                this[prop] = null;
              }
            }
          }
          function createResult(coords, rect) {
            return {
              rect,
              coords,
              delta: {
                x: 0,
                y: 0
              },
              rectDelta: {
                left: 0,
                right: 0,
                top: 0,
                bottom: 0
              },
              eventProps: [],
              changed: true
            };
          }
          function getModifierList(interaction) {
            const actionOptions = interaction.interactable.options[interaction.prepared.name];
            const actionModifiers = actionOptions.modifiers;
            if (actionModifiers && actionModifiers.length) {
              return actionModifiers;
            }
            return ["snap", "snapSize", "snapEdges", "restrict", "restrictEdges", "restrictSize"].map((type) => {
              const options = actionOptions[type];
              return options && options.enabled && {
                options,
                methods: options._methods
              };
            }).filter((m) => !!m);
          }
          function getRectOffset(rect, coords) {
            return rect ? {
              left: coords.x - rect.left,
              top: coords.y - rect.top,
              right: rect.right - coords.x,
              bottom: rect.bottom - coords.y
            } : {
              left: 0,
              top: 0,
              right: 0,
              bottom: 0
            };
          }
          function makeModifier(module3, name) {
            const {
              defaults
            } = module3;
            const methods = {
              start: module3.start,
              set: module3.set,
              beforeEnd: module3.beforeEnd,
              stop: module3.stop
            };
            const modifier = (_options) => {
              const options = _options || {};
              options.enabled = options.enabled !== false;
              for (const prop in defaults) {
                if (!(prop in options)) {
                  options[prop] = defaults[prop];
                }
              }
              const m = {
                options,
                methods,
                name,
                enable: () => {
                  options.enabled = true;
                  return m;
                },
                disable: () => {
                  options.enabled = false;
                  return m;
                }
              };
              return m;
            };
            if (name && typeof name === "string") {
              modifier._defaults = defaults;
              modifier._methods = methods;
            }
            return modifier;
          }
          function addEventModifiers({
            iEvent,
            interaction: {
              modification: {
                result
              }
            }
          }) {
            if (result) {
              iEvent.modifiers = result.eventProps;
            }
          }
          const modifiersBase = {
            id: "modifiers/base",
            before: ["actions"],
            install: (scope) => {
              scope.defaults.perAction.modifiers = [];
            },
            listeners: {
              "interactions:new": ({
                interaction
              }) => {
                interaction.modification = new Modification_Modification(interaction);
              },
              "interactions:before-action-start": (arg) => {
                const {
                  modification
                } = arg.interaction;
                modification.start(arg, arg.interaction.coords.start.page);
                arg.interaction.edges = modification.edges;
                modification.applyToInteraction(arg);
              },
              "interactions:before-action-move": (arg) => arg.interaction.modification.setAndApply(arg),
              "interactions:before-action-end": (arg) => arg.interaction.modification.beforeEnd(arg),
              "interactions:action-start": addEventModifiers,
              "interactions:action-move": addEventModifiers,
              "interactions:action-end": addEventModifiers,
              "interactions:after-action-start": (arg) => arg.interaction.modification.restoreInteractionCoords(arg),
              "interactions:after-action-move": (arg) => arg.interaction.modification.restoreInteractionCoords(arg),
              "interactions:stop": (arg) => arg.interaction.modification.stop(arg)
            }
          };
          var modifiers_base = modifiersBase;
          const aspectRatio = {
            start(arg) {
              const {
                state,
                rect,
                edges: originalEdges,
                pageCoords: coords
              } = arg;
              let {
                ratio
              } = state.options;
              const {
                equalDelta,
                modifiers
              } = state.options;
              if (ratio === "preserve") {
                ratio = rect.width / rect.height;
              }
              state.startCoords = extend({}, coords);
              state.startRect = extend({}, rect);
              state.ratio = ratio;
              state.equalDelta = equalDelta;
              const linkedEdges = state.linkedEdges = {
                top: originalEdges.top || originalEdges.left && !originalEdges.bottom,
                left: originalEdges.left || originalEdges.top && !originalEdges.right,
                bottom: originalEdges.bottom || originalEdges.right && !originalEdges.top,
                right: originalEdges.right || originalEdges.bottom && !originalEdges.left
              };
              state.xIsPrimaryAxis = !!(originalEdges.left || originalEdges.right);
              if (state.equalDelta) {
                state.edgeSign = (linkedEdges.left ? 1 : -1) * (linkedEdges.top ? 1 : -1);
              } else {
                const negativeSecondaryEdge = state.xIsPrimaryAxis ? linkedEdges.top : linkedEdges.left;
                state.edgeSign = negativeSecondaryEdge ? -1 : 1;
              }
              extend(arg.edges, linkedEdges);
              if (!modifiers || !modifiers.length) {
                return;
              }
              const subModification = new Modification_Modification(arg.interaction);
              subModification.copyFrom(arg.interaction.modification);
              subModification.prepareStates(modifiers);
              state.subModification = subModification;
              subModification.startAll({
                ...arg
              });
            },
            set(arg) {
              const {
                state,
                rect,
                coords
              } = arg;
              const initialCoords = extend({}, coords);
              const aspectMethod = state.equalDelta ? setEqualDelta : setRatio;
              aspectMethod(state, state.xIsPrimaryAxis, coords, rect);
              if (!state.subModification) {
                return null;
              }
              const correctedRect = extend({}, rect);
              addEdges(state.linkedEdges, correctedRect, {
                x: coords.x - initialCoords.x,
                y: coords.y - initialCoords.y
              });
              const result = state.subModification.setAll({
                ...arg,
                rect: correctedRect,
                edges: state.linkedEdges,
                pageCoords: coords,
                prevCoords: coords,
                prevRect: correctedRect
              });
              const {
                delta
              } = result;
              if (result.changed) {
                const xIsCriticalAxis = Math.abs(delta.x) > Math.abs(delta.y);
                aspectMethod(state, xIsCriticalAxis, result.coords, result.rect);
                extend(coords, result.coords);
              }
              return result.eventProps;
            },
            defaults: {
              ratio: "preserve",
              equalDelta: false,
              modifiers: [],
              enabled: false
            }
          };
          function setEqualDelta({
            startCoords,
            edgeSign
          }, xIsPrimaryAxis, coords) {
            if (xIsPrimaryAxis) {
              coords.y = startCoords.y + (coords.x - startCoords.x) * edgeSign;
            } else {
              coords.x = startCoords.x + (coords.y - startCoords.y) * edgeSign;
            }
          }
          function setRatio({
            startRect,
            startCoords,
            ratio,
            edgeSign
          }, xIsPrimaryAxis, coords, rect) {
            if (xIsPrimaryAxis) {
              const newHeight = rect.width / ratio;
              coords.y = startCoords.y + (newHeight - startRect.height) * edgeSign;
            } else {
              const newWidth = rect.height * ratio;
              coords.x = startCoords.x + (newWidth - startRect.width) * edgeSign;
            }
          }
          var modifiers_aspectRatio = makeModifier(aspectRatio, "aspectRatio");
          const noop = () => {
          };
          noop._defaults = {};
          var modifiers_noop = noop;
          function pointer_start({
            rect,
            startOffset,
            state,
            interaction,
            pageCoords
          }) {
            const {
              options
            } = state;
            const {
              elementRect
            } = options;
            const offset = extend({
              left: 0,
              top: 0,
              right: 0,
              bottom: 0
            }, options.offset || {});
            if (rect && elementRect) {
              const restriction = getRestrictionRect(options.restriction, interaction, pageCoords);
              if (restriction) {
                const widthDiff = restriction.right - restriction.left - rect.width;
                const heightDiff = restriction.bottom - restriction.top - rect.height;
                if (widthDiff < 0) {
                  offset.left += widthDiff;
                  offset.right += widthDiff;
                }
                if (heightDiff < 0) {
                  offset.top += heightDiff;
                  offset.bottom += heightDiff;
                }
              }
              offset.left += startOffset.left - rect.width * elementRect.left;
              offset.top += startOffset.top - rect.height * elementRect.top;
              offset.right += startOffset.right - rect.width * (1 - elementRect.right);
              offset.bottom += startOffset.bottom - rect.height * (1 - elementRect.bottom);
            }
            state.offset = offset;
          }
          function set({
            coords,
            interaction,
            state
          }) {
            const {
              options,
              offset
            } = state;
            const restriction = getRestrictionRect(options.restriction, interaction, coords);
            if (!restriction) {
              return;
            }
            const rect = xywhToTlbr(restriction);
            coords.x = Math.max(Math.min(rect.right - offset.right, coords.x), rect.left + offset.left);
            coords.y = Math.max(Math.min(rect.bottom - offset.bottom, coords.y), rect.top + offset.top);
          }
          function getRestrictionRect(value, interaction, coords) {
            if (is.func(value)) {
              return resolveRectLike(value, interaction.interactable, interaction.element, [coords.x, coords.y, interaction]);
            } else {
              return resolveRectLike(value, interaction.interactable, interaction.element);
            }
          }
          const pointer_defaults = {
            restriction: null,
            elementRect: null,
            offset: null,
            endOnly: false,
            enabled: false
          };
          const restrict = {
            start: pointer_start,
            set,
            defaults: pointer_defaults
          };
          var restrict_pointer = makeModifier(restrict, "restrict");
          const noInner = {
            top: Infinity,
            left: Infinity,
            bottom: -Infinity,
            right: -Infinity
          };
          const noOuter = {
            top: -Infinity,
            left: -Infinity,
            bottom: Infinity,
            right: Infinity
          };
          function edges_start({
            interaction,
            startOffset,
            state
          }) {
            const {
              options
            } = state;
            let offset;
            if (options) {
              const offsetRect = getRestrictionRect(options.offset, interaction, interaction.coords.start.page);
              offset = rectToXY(offsetRect);
            }
            offset = offset || {
              x: 0,
              y: 0
            };
            state.offset = {
              top: offset.y + startOffset.top,
              left: offset.x + startOffset.left,
              bottom: offset.y - startOffset.bottom,
              right: offset.x - startOffset.right
            };
          }
          function edges_set({
            coords,
            edges,
            interaction,
            state
          }) {
            const {
              offset,
              options
            } = state;
            if (!edges) {
              return;
            }
            const page = extend({}, coords);
            const inner = getRestrictionRect(options.inner, interaction, page) || {};
            const outer = getRestrictionRect(options.outer, interaction, page) || {};
            fixRect(inner, noInner);
            fixRect(outer, noOuter);
            if (edges.top) {
              coords.y = Math.min(Math.max(outer.top + offset.top, page.y), inner.top + offset.top);
            } else if (edges.bottom) {
              coords.y = Math.max(Math.min(outer.bottom + offset.bottom, page.y), inner.bottom + offset.bottom);
            }
            if (edges.left) {
              coords.x = Math.min(Math.max(outer.left + offset.left, page.x), inner.left + offset.left);
            } else if (edges.right) {
              coords.x = Math.max(Math.min(outer.right + offset.right, page.x), inner.right + offset.right);
            }
          }
          function fixRect(rect, defaults) {
            for (const edge of ["top", "left", "bottom", "right"]) {
              if (!(edge in rect)) {
                rect[edge] = defaults[edge];
              }
            }
            return rect;
          }
          const edges_defaults = {
            inner: null,
            outer: null,
            offset: null,
            endOnly: false,
            enabled: false
          };
          const restrictEdges = {
            noInner,
            noOuter,
            start: edges_start,
            set: edges_set,
            defaults: edges_defaults
          };
          var restrict_edges = makeModifier(restrictEdges, "restrictEdges");
          const rect_defaults = extend({
            get elementRect() {
              return {
                top: 0,
                left: 0,
                bottom: 1,
                right: 1
              };
            },
            set elementRect(_) {
            }
          }, restrict.defaults);
          const restrictRect = {
            start: restrict.start,
            set: restrict.set,
            defaults: rect_defaults
          };
          var restrict_rect = makeModifier(restrictRect, "restrictRect");
          const noMin = {
            width: -Infinity,
            height: -Infinity
          };
          const noMax = {
            width: Infinity,
            height: Infinity
          };
          function size_start(arg) {
            return restrictEdges.start(arg);
          }
          function size_set(arg) {
            const {
              interaction,
              state,
              rect,
              edges
            } = arg;
            const {
              options
            } = state;
            if (!edges) {
              return;
            }
            const minSize = tlbrToXywh(getRestrictionRect(options.min, interaction, arg.coords)) || noMin;
            const maxSize = tlbrToXywh(getRestrictionRect(options.max, interaction, arg.coords)) || noMax;
            state.options = {
              endOnly: options.endOnly,
              inner: extend({}, restrictEdges.noInner),
              outer: extend({}, restrictEdges.noOuter)
            };
            if (edges.top) {
              state.options.inner.top = rect.bottom - minSize.height;
              state.options.outer.top = rect.bottom - maxSize.height;
            } else if (edges.bottom) {
              state.options.inner.bottom = rect.top + minSize.height;
              state.options.outer.bottom = rect.top + maxSize.height;
            }
            if (edges.left) {
              state.options.inner.left = rect.right - minSize.width;
              state.options.outer.left = rect.right - maxSize.width;
            } else if (edges.right) {
              state.options.inner.right = rect.left + minSize.width;
              state.options.outer.right = rect.left + maxSize.width;
            }
            restrictEdges.set(arg);
            state.options = options;
          }
          const size_defaults = {
            min: null,
            max: null,
            endOnly: false,
            enabled: false
          };
          const restrictSize = {
            start: size_start,
            set: size_set,
            defaults: size_defaults
          };
          var size = makeModifier(restrictSize, "restrictSize");
          function snap_pointer_start(arg) {
            const {
              interaction,
              interactable,
              element,
              rect,
              state,
              startOffset
            } = arg;
            const {
              options
            } = state;
            const origin = options.offsetWithOrigin ? getOrigin(arg) : {
              x: 0,
              y: 0
            };
            let snapOffset;
            if (options.offset === "startCoords") {
              snapOffset = {
                x: interaction.coords.start.page.x,
                y: interaction.coords.start.page.y
              };
            } else {
              const offsetRect = resolveRectLike(options.offset, interactable, element, [interaction]);
              snapOffset = rectToXY(offsetRect) || {
                x: 0,
                y: 0
              };
              snapOffset.x += origin.x;
              snapOffset.y += origin.y;
            }
            const {
              relativePoints
            } = options;
            state.offsets = rect && relativePoints && relativePoints.length ? relativePoints.map((relativePoint, index) => ({
              index,
              relativePoint,
              x: startOffset.left - rect.width * relativePoint.x + snapOffset.x,
              y: startOffset.top - rect.height * relativePoint.y + snapOffset.y
            })) : [extend({
              index: 0,
              relativePoint: null
            }, snapOffset)];
          }
          function pointer_set(arg) {
            const {
              interaction,
              coords,
              state
            } = arg;
            const {
              options,
              offsets
            } = state;
            const origin = getOriginXY(interaction.interactable, interaction.element, interaction.prepared.name);
            const page = extend({}, coords);
            const targets = [];
            if (!options.offsetWithOrigin) {
              page.x -= origin.x;
              page.y -= origin.y;
            }
            for (const offset of offsets) {
              const relativeX = page.x - offset.x;
              const relativeY = page.y - offset.y;
              for (let index = 0, len = options.targets.length; index < len; index++) {
                const snapTarget = options.targets[index];
                let target;
                if (is.func(snapTarget)) {
                  target = snapTarget(relativeX, relativeY, interaction._proxy, offset, index);
                } else {
                  target = snapTarget;
                }
                if (!target) {
                  continue;
                }
                targets.push({
                  x: (is.number(target.x) ? target.x : relativeX) + offset.x,
                  y: (is.number(target.y) ? target.y : relativeY) + offset.y,
                  range: is.number(target.range) ? target.range : options.range,
                  source: snapTarget,
                  index,
                  offset
                });
              }
            }
            const closest = {
              target: null,
              inRange: false,
              distance: 0,
              range: 0,
              delta: {
                x: 0,
                y: 0
              }
            };
            for (const target of targets) {
              const range = target.range;
              const dx = target.x - page.x;
              const dy = target.y - page.y;
              const distance = hypot(dx, dy);
              let inRange = distance <= range;
              if (range === Infinity && closest.inRange && closest.range !== Infinity) {
                inRange = false;
              }
              if (!closest.target || (inRange ? closest.inRange && range !== Infinity ? distance / range < closest.distance / closest.range : range === Infinity && closest.range !== Infinity || // OR this target is closer that the previous closest
              distance < closest.distance : (
                // The other is not in range and the pointer is closer to this target
                !closest.inRange && distance < closest.distance
              ))) {
                closest.target = target;
                closest.distance = distance;
                closest.range = range;
                closest.inRange = inRange;
                closest.delta.x = dx;
                closest.delta.y = dy;
              }
            }
            if (closest.inRange) {
              coords.x = closest.target.x;
              coords.y = closest.target.y;
            }
            state.closest = closest;
            return closest;
          }
          function getOrigin(arg) {
            const {
              element
            } = arg.interaction;
            const optionsOrigin = rectToXY(resolveRectLike(arg.state.options.origin, null, null, [element]));
            const origin = optionsOrigin || getOriginXY(arg.interactable, element, arg.interaction.prepared.name);
            return origin;
          }
          const snap_pointer_defaults = {
            range: Infinity,
            targets: null,
            offset: null,
            offsetWithOrigin: true,
            origin: null,
            relativePoints: null,
            endOnly: false,
            enabled: false
          };
          const snap = {
            start: snap_pointer_start,
            set: pointer_set,
            defaults: snap_pointer_defaults
          };
          var snap_pointer = makeModifier(snap, "snap");
          function snap_size_start(arg) {
            const {
              state,
              edges
            } = arg;
            const {
              options
            } = state;
            if (!edges) {
              return null;
            }
            arg.state = {
              options: {
                targets: null,
                relativePoints: [{
                  x: edges.left ? 0 : 1,
                  y: edges.top ? 0 : 1
                }],
                offset: options.offset || "self",
                origin: {
                  x: 0,
                  y: 0
                },
                range: options.range
              }
            };
            state.targetFields = state.targetFields || [["width", "height"], ["x", "y"]];
            snap.start(arg);
            state.offsets = arg.state.offsets;
            arg.state = state;
          }
          function snap_size_set(arg) {
            const {
              interaction,
              state,
              coords
            } = arg;
            const {
              options,
              offsets
            } = state;
            const relative = {
              x: coords.x - offsets[0].x,
              y: coords.y - offsets[0].y
            };
            state.options = extend({}, options);
            state.options.targets = [];
            for (const snapTarget of options.targets || []) {
              let target;
              if (is.func(snapTarget)) {
                target = snapTarget(relative.x, relative.y, interaction);
              } else {
                target = snapTarget;
              }
              if (!target) {
                continue;
              }
              for (const [xField, yField] of state.targetFields) {
                if (xField in target || yField in target) {
                  target.x = target[xField];
                  target.y = target[yField];
                  break;
                }
              }
              state.options.targets.push(target);
            }
            const returnValue = snap.set(arg);
            state.options = options;
            return returnValue;
          }
          const snap_size_defaults = {
            range: Infinity,
            targets: null,
            offset: null,
            endOnly: false,
            enabled: false
          };
          const snapSize = {
            start: snap_size_start,
            set: snap_size_set,
            defaults: snap_size_defaults
          };
          var snap_size = makeModifier(snapSize, "snapSize");
          function snap_edges_start(arg) {
            const {
              edges
            } = arg;
            if (!edges) {
              return null;
            }
            arg.state.targetFields = arg.state.targetFields || [[edges.left ? "left" : "right", edges.top ? "top" : "bottom"]];
            return snapSize.start(arg);
          }
          const snapEdges = {
            start: snap_edges_start,
            set: snapSize.set,
            defaults: extend(clone(snapSize.defaults), {
              targets: null,
              range: null,
              offset: {
                x: 0,
                y: 0
              }
            })
          };
          var snap_edges = makeModifier(snapEdges, "snapEdges");
          var modifiers_all = {
            aspectRatio: modifiers_aspectRatio,
            restrictEdges: restrict_edges,
            restrict: restrict_pointer,
            restrictRect: restrict_rect,
            restrictSize: size,
            snapEdges: snap_edges,
            snap: snap_pointer,
            snapSize: snap_size,
            spring: modifiers_noop,
            avoid: modifiers_noop,
            transform: modifiers_noop,
            rubberband: modifiers_noop
          };
          const plugin_modifiers = {
            id: "modifiers",
            install(scope) {
              const {
                interactStatic: interact
              } = scope;
              scope.usePlugin(modifiers_base);
              scope.usePlugin(snappers_plugin);
              interact.modifiers = modifiers_all;
              for (const type in modifiers_all) {
                const {
                  _defaults,
                  _methods
                } = modifiers_all[type];
                _defaults._methods = _methods;
                scope.defaults.perAction[type] = _defaults;
              }
            }
          };
          var modifiers_plugin = plugin_modifiers;
          if (typeof window === "object" && !!window) {
            interact_init(window);
          }
          _interactjs_interact.use(modifiers_plugin);
          var CheckName;
          (function(CheckName2) {
            CheckName2["touchAction"] = "touchAction";
            CheckName2["boxSizing"] = "boxSizing";
            CheckName2["noListeners"] = "noListeners";
          })(CheckName || (CheckName = {}));
          const prefix = "[interact.js] ";
          const links = {
            touchAction: "https://developer.mozilla.org/en-US/docs/Web/CSS/touch-action",
            boxSizing: "https://developer.mozilla.org/en-US/docs/Web/CSS/box-sizing"
          };
          const isProduction = false;
          function dev_tools_plugin_install(scope, {
            logger
          } = {}) {
            const {
              Interactable,
              defaults
            } = scope;
            scope.logger = logger || console;
            defaults.base.devTools = {
              ignore: {}
            };
            Interactable.prototype.devTools = function(options) {
              if (options) {
                extend(this.options.devTools, options);
                return this;
              }
              return this.options.devTools;
            };
          }
          const checks = [{
            name: CheckName.touchAction,
            perform({
              element
            }) {
              return !parentHasStyle(element, "touchAction", /pan-|pinch|none/);
            },
            getInfo({
              element
            }) {
              return [element, links.touchAction];
            },
            text: 'Consider adding CSS "touch-action: none" to this element\n'
          }, {
            name: CheckName.boxSizing,
            perform(interaction) {
              const {
                element
              } = interaction;
              return interaction.prepared.name === "resize" && element instanceof utils_domObjects.HTMLElement && !hasStyle(element, "boxSizing", /border-box/);
            },
            text: 'Consider adding CSS "box-sizing: border-box" to this resizable element',
            getInfo({
              element
            }) {
              return [element, links.boxSizing];
            }
          }, {
            name: CheckName.noListeners,
            perform(interaction) {
              const actionName = interaction.prepared.name;
              const moveListeners = interaction.interactable.events.types[`${actionName}move`] || [];
              return !moveListeners.length;
            },
            getInfo(interaction) {
              return [interaction.prepared.name, interaction.interactable];
            },
            text: "There are no listeners set for this action"
          }];
          function hasStyle(element, prop, styleRe) {
            const value = element.style[prop] || win.getComputedStyle(element)[prop];
            return styleRe.test((value || "").toString());
          }
          function parentHasStyle(element, prop, styleRe) {
            let parent = element;
            while (is.element(parent)) {
              if (hasStyle(parent, prop, styleRe)) {
                return true;
              }
              parent = parentNode(parent);
            }
            return false;
          }
          const plugin_id = "dev-tools";
          const defaultExport = isProduction ? {
            id: plugin_id,
            install: () => {
            }
          } : {
            id: plugin_id,
            install: dev_tools_plugin_install,
            listeners: {
              "interactions:action-start": ({
                interaction
              }, scope) => {
                for (const check of checks) {
                  const options = interaction.interactable && interaction.interactable.options;
                  if (!(options && options.devTools && options.devTools.ignore[check.name]) && check.perform(interaction)) {
                    scope.logger.warn(prefix + check.text, ...check.getInfo(interaction));
                  }
                }
              }
            },
            checks,
            CheckName,
            links,
            prefix
          };
          var dev_tools_plugin = defaultExport;
          if (typeof window === "object" && !!window) {
            interact_init(window);
          }
          _interactjs_interact.use(dev_tools_plugin);
          var GridItemvue_type_script_lang_js = {
            name: "GridItem",
            props: {
              /*cols: {
               type: Number,
               required: true
               },*/
              /*containerWidth: {
               type: Number,
               required: true
                 },
               rowHeight: {
               type: Number,
               required: true
               },
               margin: {
               type: Array,
               required: true
               },
               maxRows: {
               type: Number,
               required: true
               },*/
              isDraggable: {
                type: Boolean,
                required: false,
                default: null
              },
              isResizable: {
                type: Boolean,
                required: false,
                default: null
              },
              /*useCssTransforms: {
               type: Boolean,
               required: true
               },
               */
              static: {
                type: Boolean,
                required: false,
                default: false
              },
              minH: {
                type: Number,
                required: false,
                default: 1
              },
              minW: {
                type: Number,
                required: false,
                default: 1
              },
              maxH: {
                type: Number,
                required: false,
                default: Infinity
              },
              maxW: {
                type: Number,
                required: false,
                default: Infinity
              },
              x: {
                type: Number,
                required: true
              },
              y: {
                type: Number,
                required: true
              },
              w: {
                type: Number,
                required: true
              },
              h: {
                type: Number,
                required: true
              },
              i: {
                required: true
              },
              dragIgnoreFrom: {
                type: String,
                required: false,
                default: "a, button"
              },
              dragAllowFrom: {
                type: String,
                required: false,
                default: null
              },
              resizeIgnoreFrom: {
                type: String,
                required: false,
                default: "a, button"
              },
              preserveAspectRatio: {
                type: Boolean,
                required: false,
                default: false
              }
            },
            inject: ["eventBus", "layout"],
            data: function data() {
              return {
                cols: 1,
                containerWidth: 100,
                rowHeight: 30,
                margin: [10, 10],
                maxRows: Infinity,
                draggable: null,
                resizable: null,
                useCssTransforms: true,
                useStyleCursor: true,
                isDragging: false,
                dragging: null,
                isResizing: false,
                resizing: null,
                lastX: NaN,
                lastY: NaN,
                lastW: NaN,
                lastH: NaN,
                style: {},
                rtl: false,
                dragEventSet: false,
                resizeEventSet: false,
                previousW: null,
                previousH: null,
                previousX: null,
                previousY: null,
                innerX: this.x,
                innerY: this.y,
                innerW: this.w,
                innerH: this.h
              };
            },
            created: function created() {
              var _this = this;
              var self2 = this;
              self2.updateWidthHandler = function(width) {
                self2.updateWidth(width);
              };
              self2.compactHandler = function(layout) {
                self2.compact(layout);
              };
              self2.setDraggableHandler = function(isDraggable) {
                if (self2.isDraggable === null) {
                  self2.draggable = isDraggable;
                }
              };
              self2.setResizableHandler = function(isResizable) {
                if (self2.isResizable === null) {
                  self2.resizable = isResizable;
                }
              };
              self2.setRowHeightHandler = function(rowHeight) {
                self2.rowHeight = rowHeight;
              };
              self2.setMaxRowsHandler = function(maxRows) {
                self2.maxRows = maxRows;
              };
              self2.directionchangeHandler = function() {
                _this.rtl = getDocumentDir() === "rtl";
                _this.compact();
              };
              self2.setColNum = function(colNum) {
                self2.cols = parseInt(colNum);
              };
              this.eventBus.on("updateWidth", self2.updateWidthHandler);
              this.eventBus.on("compact", self2.compactHandler);
              this.eventBus.on("setDraggable", self2.setDraggableHandler);
              this.eventBus.on("setResizable", self2.setResizableHandler);
              this.eventBus.on("setRowHeight", self2.setRowHeightHandler);
              this.eventBus.on("setMaxRows", self2.setMaxRowsHandler);
              this.eventBus.on("directionchange", self2.directionchangeHandler);
              this.eventBus.on("setColNum", self2.setColNum);
              this.rtl = getDocumentDir() === "rtl";
            },
            beforeUnmount: function beforeUnmount() {
              var self2 = this;
              this.eventBus.off("updateWidth", self2.updateWidthHandler);
              this.eventBus.off("compact", self2.compactHandler);
              this.eventBus.off("setDraggable", self2.setDraggableHandler);
              this.eventBus.off("setResizable", self2.setResizableHandler);
              this.eventBus.off("setRowHeight", self2.setRowHeightHandler);
              this.eventBus.off("setMaxRows", self2.setMaxRowsHandler);
              this.eventBus.off("directionchange", self2.directionchangeHandler);
              this.eventBus.off("setColNum", self2.setColNum);
              if (this.interactObj) {
                this.interactObj.unset();
              }
            },
            mounted: function mounted() {
              if (this.layout.responsive && this.layout.lastBreakpoint) {
                this.cols = getColsFromBreakpoint(this.layout.lastBreakpoint, this.layout.cols);
              } else {
                this.cols = this.layout.colNum;
              }
              this.rowHeight = this.layout.rowHeight;
              this.containerWidth = this.layout.width !== null ? this.layout.width : 100;
              this.margin = this.layout.margin !== void 0 ? this.layout.margin : [10, 10];
              this.maxRows = this.layout.maxRows;
              if (this.isDraggable === null) {
                this.draggable = this.layout.isDraggable;
              } else {
                this.draggable = this.isDraggable;
              }
              if (this.isResizable === null) {
                this.resizable = this.layout.isResizable;
              } else {
                this.resizable = this.isResizable;
              }
              this.useCssTransforms = this.layout.useCssTransforms;
              this.useStyleCursor = this.layout.useStyleCursor;
              this.createStyle();
            },
            watch: {
              isDraggable: function isDraggable() {
                this.draggable = this.isDraggable;
              },
              static: function _static() {
                this.tryMakeDraggable();
                this.tryMakeResizable();
              },
              draggable: function draggable() {
                this.tryMakeDraggable();
              },
              isResizable: function isResizable() {
                this.resizable = this.isResizable;
              },
              resizable: function resizable2() {
                this.tryMakeResizable();
              },
              rowHeight: function rowHeight() {
                this.createStyle();
                this.emitContainerResized();
              },
              cols: function cols() {
                this.tryMakeResizable();
                this.createStyle();
                this.emitContainerResized();
              },
              containerWidth: function containerWidth() {
                this.tryMakeResizable();
                this.createStyle();
                this.emitContainerResized();
              },
              x: function x(newVal) {
                this.innerX = newVal;
                this.createStyle();
              },
              y: function y(newVal) {
                this.innerY = newVal;
                this.createStyle();
              },
              h: function h(newVal) {
                this.innerH = newVal;
                this.createStyle();
              },
              w: function w(newVal) {
                this.innerW = newVal;
                this.createStyle();
              },
              renderRtl: function renderRtl() {
                this.tryMakeResizable();
                this.createStyle();
              },
              minH: function minH() {
                this.tryMakeResizable();
              },
              maxH: function maxH() {
                this.tryMakeResizable();
              },
              minW: function minW() {
                this.tryMakeResizable();
              },
              maxW: function maxW() {
                this.tryMakeResizable();
              },
              "$parent.margin": function $parentMargin(margin) {
                if (!margin || margin[0] == this.margin[0] && margin[1] == this.margin[1]) {
                  return;
                }
                this.margin = margin.map(function(m) {
                  return Number(m);
                });
                this.createStyle();
                this.emitContainerResized();
              }
            },
            computed: {
              classObj: function classObj() {
                return {
                  "vue-resizable": this.resizableAndNotStatic,
                  "static": this.static,
                  "resizing": this.isResizing,
                  "vue-draggable-dragging": this.isDragging,
                  "cssTransforms": this.useCssTransforms,
                  "render-rtl": this.renderRtl,
                  "disable-userselect": this.isDragging,
                  "no-touch": this.isAndroid && this.draggableOrResizableAndNotStatic
                };
              },
              resizableAndNotStatic: function resizableAndNotStatic() {
                return this.resizable && !this.static;
              },
              draggableOrResizableAndNotStatic: function draggableOrResizableAndNotStatic() {
                return (this.draggable || this.resizable) && !this.static;
              },
              isAndroid: function isAndroid() {
                return navigator.userAgent.toLowerCase().indexOf("android") !== -1;
              },
              renderRtl: function renderRtl() {
                return this.layout.isMirrored ? !this.rtl : this.rtl;
              },
              resizableHandleClass: function resizableHandleClass() {
                if (this.renderRtl) {
                  return "vue-resizable-handle vue-rtl-resizable-handle";
                } else {
                  return "vue-resizable-handle";
                }
              }
            },
            methods: {
              createStyle: function createStyle() {
                if (this.x + this.w > this.cols) {
                  this.innerX = 0;
                  this.innerW = this.w > this.cols ? this.cols : this.w;
                } else {
                  this.innerX = this.x;
                  this.innerW = this.w;
                }
                var pos = this.calcPosition(this.innerX, this.innerY, this.innerW, this.innerH);
                if (this.isDragging) {
                  pos.top = this.dragging.top;
                  if (this.renderRtl) {
                    pos.right = this.dragging.left;
                  } else {
                    pos.left = this.dragging.left;
                  }
                }
                if (this.isResizing) {
                  pos.width = this.resizing.width;
                  pos.height = this.resizing.height;
                }
                var style;
                if (this.useCssTransforms) {
                  if (this.renderRtl) {
                    style = setTransformRtl(pos.top, pos.right, pos.width, pos.height);
                  } else {
                    style = setTransform(pos.top, pos.left, pos.width, pos.height);
                  }
                } else {
                  if (this.renderRtl) {
                    style = setTopRight(pos.top, pos.right, pos.width, pos.height);
                  } else {
                    style = setTopLeft(pos.top, pos.left, pos.width, pos.height);
                  }
                }
                this.style = style;
              },
              emitContainerResized: function emitContainerResized() {
                var styleProps = {};
                for (var _i = 0, _arr = ["width", "height"]; _i < _arr.length; _i++) {
                  var prop = _arr[_i];
                  var val = this.style[prop];
                  var matches = val.match(/^(\d+)px$/);
                  if (!matches) return;
                  styleProps[prop] = matches[1];
                }
                this.$emit("container-resized", this.i, this.h, this.w, styleProps.height, styleProps.width);
              },
              handleResize: function handleResize(event) {
                if (this.static) return;
                var position = getControlPosition(event);
                if (position == null) return;
                var x = position.x, y = position.y;
                var newSize = {
                  width: 0,
                  height: 0
                };
                var pos;
                switch (event.type) {
                  case "resizestart": {
                    this.previousW = this.innerW;
                    this.previousH = this.innerH;
                    pos = this.calcPosition(this.innerX, this.innerY, this.innerW, this.innerH);
                    newSize.width = pos.width;
                    newSize.height = pos.height;
                    this.resizing = newSize;
                    this.isResizing = true;
                    break;
                  }
                  case "resizemove": {
                    var coreEvent = createCoreData(this.lastW, this.lastH, x, y);
                    if (this.renderRtl) {
                      newSize.width = this.resizing.width - coreEvent.deltaX;
                    } else {
                      newSize.width = this.resizing.width + coreEvent.deltaX;
                    }
                    newSize.height = this.resizing.height + coreEvent.deltaY;
                    this.resizing = newSize;
                    break;
                  }
                  case "resizeend": {
                    pos = this.calcPosition(this.innerX, this.innerY, this.innerW, this.innerH);
                    newSize.width = pos.width;
                    newSize.height = pos.height;
                    this.resizing = null;
                    this.isResizing = false;
                    break;
                  }
                }
                pos = this.calcWH(newSize.height, newSize.width);
                if (pos.w < this.minW) {
                  pos.w = this.minW;
                }
                if (pos.w > this.maxW) {
                  pos.w = this.maxW;
                }
                if (pos.h < this.minH) {
                  pos.h = this.minH;
                }
                if (pos.h > this.maxH) {
                  pos.h = this.maxH;
                }
                if (pos.h < 1) {
                  pos.h = 1;
                }
                if (pos.w < 1) {
                  pos.w = 1;
                }
                this.lastW = x;
                this.lastH = y;
                if (this.innerW !== pos.w || this.innerH !== pos.h) {
                  this.$emit("resize", this.i, pos.h, pos.w, newSize.height, newSize.width);
                }
                if (event.type === "resizeend" && (this.previousW !== this.innerW || this.previousH !== this.innerH)) {
                  this.$emit("resized", this.i, pos.h, pos.w, newSize.height, newSize.width);
                }
                this.eventBus.emit("resizeEvent", {
                  eventType: event.type,
                  i: this.i,
                  x: this.innerX,
                  y: this.innerY,
                  h: pos.h,
                  w: pos.w
                });
              },
              handleDrag: function handleDrag(event) {
                if (this.static) return;
                if (this.isResizing) return;
                var position = getControlPosition(event);
                if (position === null) return;
                var x = position.x, y = position.y;
                var newPosition = {
                  top: 0,
                  left: 0
                };
                switch (event.type) {
                  case "dragstart": {
                    this.previousX = this.innerX;
                    this.previousY = this.innerY;
                    var parentRect = event.target.offsetParent.getBoundingClientRect();
                    var clientRect = event.target.getBoundingClientRect();
                    if (this.renderRtl) {
                      newPosition.left = (clientRect.right - parentRect.right) * -1;
                    } else {
                      newPosition.left = clientRect.left - parentRect.left;
                    }
                    newPosition.top = clientRect.top - parentRect.top;
                    this.dragging = newPosition;
                    this.isDragging = true;
                    break;
                  }
                  case "dragend": {
                    if (!this.isDragging) return;
                    var _parentRect = event.target.offsetParent.getBoundingClientRect();
                    var _clientRect = event.target.getBoundingClientRect();
                    if (this.renderRtl) {
                      newPosition.left = (_clientRect.right - _parentRect.right) * -1;
                    } else {
                      newPosition.left = _clientRect.left - _parentRect.left;
                    }
                    newPosition.top = _clientRect.top - _parentRect.top;
                    this.dragging = null;
                    this.isDragging = false;
                    break;
                  }
                  case "dragmove": {
                    var coreEvent = createCoreData(this.lastX, this.lastY, x, y);
                    if (this.renderRtl) {
                      newPosition.left = this.dragging.left - coreEvent.deltaX;
                    } else {
                      newPosition.left = this.dragging.left + coreEvent.deltaX;
                    }
                    newPosition.top = this.dragging.top + coreEvent.deltaY;
                    this.dragging = newPosition;
                    break;
                  }
                }
                var pos;
                if (this.renderRtl) {
                  pos = this.calcXY(newPosition.top, newPosition.left);
                } else {
                  pos = this.calcXY(newPosition.top, newPosition.left);
                }
                this.lastX = x;
                this.lastY = y;
                if (this.innerX !== pos.x || this.innerY !== pos.y) {
                  this.$emit("move", this.i, pos.x, pos.y);
                }
                if (event.type === "dragend" && (this.previousX !== this.innerX || this.previousY !== this.innerY)) {
                  this.$emit("moved", this.i, pos.x, pos.y);
                }
                this.eventBus.emit("dragEvent", {
                  eventType: event.type,
                  i: this.i,
                  x: pos.x,
                  y: pos.y,
                  h: this.innerH,
                  w: this.innerW
                });
              },
              calcPosition: function calcPosition(x, y, w, h) {
                var colWidth = this.calcColWidth();
                var out;
                if (this.renderRtl) {
                  out = {
                    right: Math.round(colWidth * x + (x + 1) * this.margin[0]),
                    top: Math.round(this.rowHeight * y + (y + 1) * this.margin[1]),
                    // 0 * Infinity === NaN, which causes problems with resize constriants;
                    // Fix this if it occurs.
                    // Note we do it here rather than later because Math.round(Infinity) causes deopt
                    width: w === Infinity ? w : Math.round(colWidth * w + Math.max(0, w - 1) * this.margin[0]),
                    height: h === Infinity ? h : Math.round(this.rowHeight * h + Math.max(0, h - 1) * this.margin[1])
                  };
                } else {
                  out = {
                    left: Math.round(colWidth * x + (x + 1) * this.margin[0]),
                    top: Math.round(this.rowHeight * y + (y + 1) * this.margin[1]),
                    // 0 * Infinity === NaN, which causes problems with resize constriants;
                    // Fix this if it occurs.
                    // Note we do it here rather than later because Math.round(Infinity) causes deopt
                    width: w === Infinity ? w : Math.round(colWidth * w + Math.max(0, w - 1) * this.margin[0]),
                    height: h === Infinity ? h : Math.round(this.rowHeight * h + Math.max(0, h - 1) * this.margin[1])
                  };
                }
                return out;
              },
              /**
               * Translate x and y coordinates from pixels to grid units.
               * @param  {Number} top  Top position (relative to parent) in pixels.
               * @param  {Number} left Left position (relative to parent) in pixels.
               * @return {Object} x and y in grid units.
               */
              // TODO check if this function needs change in order to support rtl.
              calcXY: function calcXY(top, left) {
                var colWidth = this.calcColWidth();
                var x = Math.round((left - this.margin[0]) / (colWidth + this.margin[0]));
                var y = Math.round((top - this.margin[1]) / (this.rowHeight + this.margin[1]));
                x = Math.max(Math.min(x, this.cols - this.innerW), 0);
                y = Math.max(Math.min(y, this.maxRows - this.innerH), 0);
                return {
                  x,
                  y
                };
              },
              // Helper for generating column width
              calcColWidth: function calcColWidth() {
                var colWidth = (this.containerWidth - this.margin[0] * (this.cols + 1)) / this.cols;
                return colWidth;
              },
              /**
               * Given a height and width in pixel values, calculate grid units.
               * @param  {Number} height Height in pixels.
               * @param  {Number} width  Width in pixels.
               * @param  {Boolean} autoSizeFlag  function autoSize identifier.
               * @return {Object} w, h as grid units.
               */
              calcWH: function calcWH(height, width) {
                var autoSizeFlag = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;
                var colWidth = this.calcColWidth();
                var w = Math.round((width + this.margin[0]) / (colWidth + this.margin[0]));
                var h = 0;
                if (!autoSizeFlag) {
                  h = Math.round((height + this.margin[1]) / (this.rowHeight + this.margin[1]));
                } else {
                  h = Math.ceil((height + this.margin[1]) / (this.rowHeight + this.margin[1]));
                }
                w = Math.max(Math.min(w, this.cols - this.innerX), 0);
                h = Math.max(Math.min(h, this.maxRows - this.innerY), 0);
                return {
                  w,
                  h
                };
              },
              updateWidth: function updateWidth(width, colNum) {
                this.containerWidth = width;
                if (colNum !== void 0 && colNum !== null) {
                  this.cols = colNum;
                }
              },
              compact: function compact2() {
                this.createStyle();
              },
              tryMakeDraggable: function tryMakeDraggable() {
                var self2 = this;
                if (this.interactObj === null || this.interactObj === void 0) {
                  this.interactObj = _interactjs_interact(this.$refs.item);
                  if (!this.useStyleCursor) {
                    this.interactObj.styleCursor(false);
                  }
                }
                if (this.draggable && !this.static) {
                  var opts = {
                    ignoreFrom: this.dragIgnoreFrom,
                    allowFrom: this.dragAllowFrom
                  };
                  this.interactObj.draggable(opts);
                  if (!this.dragEventSet) {
                    this.dragEventSet = true;
                    this.interactObj.on("dragstart dragmove dragend", function(event) {
                      self2.handleDrag(event);
                    });
                  }
                } else {
                  this.interactObj.draggable({
                    enabled: false
                  });
                }
              },
              tryMakeResizable: function tryMakeResizable() {
                var self2 = this;
                if (this.interactObj === null || this.interactObj === void 0) {
                  this.interactObj = _interactjs_interact(this.$refs.item);
                  if (!this.useStyleCursor) {
                    this.interactObj.styleCursor(false);
                  }
                }
                if (this.resizable && !this.static) {
                  var maximum = this.calcPosition(0, 0, this.maxW, this.maxH);
                  var minimum = this.calcPosition(0, 0, this.minW, this.minH);
                  var opts = {
                    // allowFrom: "." + this.resizableHandleClass.trim().replace(" ", "."),
                    edges: {
                      left: false,
                      right: "." + this.resizableHandleClass.trim().replace(" ", "."),
                      bottom: "." + this.resizableHandleClass.trim().replace(" ", "."),
                      top: false
                    },
                    ignoreFrom: this.resizeIgnoreFrom,
                    restrictSize: {
                      min: {
                        height: minimum.height,
                        width: minimum.width
                      },
                      max: {
                        height: maximum.height,
                        width: maximum.width
                      }
                    }
                  };
                  if (this.preserveAspectRatio) {
                    opts.modifiers = [_interactjs_interact.modifiers.aspectRatio({
                      ratio: "preserve"
                    })];
                  }
                  this.interactObj.resizable(opts);
                  if (!this.resizeEventSet) {
                    this.resizeEventSet = true;
                    this.interactObj.on("resizestart resizemove resizeend", function(event) {
                      self2.handleResize(event);
                    });
                  }
                } else {
                  this.interactObj.resizable({
                    enabled: false
                  });
                }
              },
              autoSize: function autoSize() {
                this.previousW = this.innerW;
                this.previousH = this.innerH;
                var newSize = this.$slots().default[0].elm.getBoundingClientRect();
                var pos = this.calcWH(newSize.height, newSize.width, true);
                if (pos.w < this.minW) {
                  pos.w = this.minW;
                }
                if (pos.w > this.maxW) {
                  pos.w = this.maxW;
                }
                if (pos.h < this.minH) {
                  pos.h = this.minH;
                }
                if (pos.h > this.maxH) {
                  pos.h = this.maxH;
                }
                if (pos.h < 1) {
                  pos.h = 1;
                }
                if (pos.w < 1) {
                  pos.w = 1;
                }
                if (this.innerW !== pos.w || this.innerH !== pos.h) {
                  this.$emit("resize", this.i, pos.h, pos.w, newSize.height, newSize.width);
                }
                if (this.previousW !== pos.w || this.previousH !== pos.h) {
                  this.$emit("resized", this.i, pos.h, pos.w, newSize.height, newSize.width);
                  this.eventBus.emit("resizeEvent", {
                    eventType: "resizeend",
                    i: this.i,
                    x: this.innerX,
                    y: this.innerY,
                    h: pos.h,
                    w: pos.w
                  });
                }
              }
            }
          };
          var GridItemvue_type_style_index_0_id_46ff2fc8_lang_css = __webpack_require__("083e");
          GridItemvue_type_script_lang_js.render = render;
          var GridItem = GridItemvue_type_script_lang_js;
          function GridLayoutvue_type_template_id_fc5948f6_render(_ctx, _cache, $props, $setup, $data, $options) {
            var _component_grid_item = Object(external_commonjs_vue_commonjs2_vue_root_Vue_["resolveComponent"])("grid-item");
            return Object(external_commonjs_vue_commonjs2_vue_root_Vue_["openBlock"])(), Object(external_commonjs_vue_commonjs2_vue_root_Vue_["createBlock"])("div", {
              ref: "item",
              class: "vue-grid-layout",
              style: _ctx.mergedStyle
            }, [Object(external_commonjs_vue_commonjs2_vue_root_Vue_["renderSlot"])(_ctx.$slots, "default"), Object(external_commonjs_vue_commonjs2_vue_root_Vue_["withDirectives"])(Object(external_commonjs_vue_commonjs2_vue_root_Vue_["createVNode"])(_component_grid_item, {
              class: "vue-grid-placeholder",
              x: _ctx.placeholder.x,
              y: _ctx.placeholder.y,
              w: _ctx.placeholder.w,
              h: _ctx.placeholder.h,
              i: _ctx.placeholder.i
            }, null, 8, ["x", "y", "w", "h", "i"]), [[external_commonjs_vue_commonjs2_vue_root_Vue_["vShow"], _ctx.isDragging]])], 4);
          }
          var es7_object_get_own_property_descriptors = __webpack_require__("8e6e");
          var es6_object_assign = __webpack_require__("f751");
          var es6_number_is_finite = __webpack_require__("fca0");
          function _defineProperty(obj, key, value) {
            if (key in obj) {
              Object.defineProperty(obj, key, {
                value,
                enumerable: true,
                configurable: true,
                writable: true
              });
            } else {
              obj[key] = value;
            }
            return obj;
          }
          var mitt_es = function(n) {
            return { all: n = n || /* @__PURE__ */ new Map(), on: function(t, e) {
              var i = n.get(t);
              i && i.push(e) || n.set(t, [e]);
            }, off: function(t, e) {
              var i = n.get(t);
              i && i.splice(i.indexOf(e) >>> 0, 1);
            }, emit: function(t, e) {
              (n.get(t) || []).slice().map(function(n2) {
                n2(e);
              }), (n.get("*") || []).slice().map(function(n2) {
                n2(t, e);
              });
            } };
          };
          function ownKeys(object2, enumerableOnly) {
            var keys = Object.keys(object2);
            if (Object.getOwnPropertySymbols) {
              var symbols = Object.getOwnPropertySymbols(object2);
              if (enumerableOnly) symbols = symbols.filter(function(sym) {
                return Object.getOwnPropertyDescriptor(object2, sym).enumerable;
              });
              keys.push.apply(keys, symbols);
            }
            return keys;
          }
          function _objectSpread(target) {
            for (var i = 1; i < arguments.length; i++) {
              var source = arguments[i] != null ? arguments[i] : {};
              if (i % 2) {
                ownKeys(Object(source), true).forEach(function(key) {
                  _defineProperty(target, key, source[key]);
                });
              } else if (Object.getOwnPropertyDescriptors) {
                Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
              } else {
                ownKeys(Object(source)).forEach(function(key) {
                  Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
                });
              }
            }
            return target;
          }
          var elementResizeDetectorMaker = __webpack_require__("eec4");
          var GridLayoutvue_type_script_lang_js = {
            name: "GridLayout",
            provide: function provide() {
              return {
                eventBus: this.eventBus,
                layout: this
              };
            },
            components: {
              GridItem
            },
            props: {
              // If true, the container height swells and contracts to fit contents
              autoSize: {
                type: Boolean,
                default: true
              },
              colNum: {
                type: Number,
                default: 12
              },
              rowHeight: {
                type: Number,
                default: 150
              },
              maxRows: {
                type: Number,
                default: Infinity
              },
              margin: {
                type: Array,
                default: function _default() {
                  return [10, 10];
                }
              },
              isDraggable: {
                type: Boolean,
                default: true
              },
              isResizable: {
                type: Boolean,
                default: true
              },
              isMirrored: {
                type: Boolean,
                default: false
              },
              useCssTransforms: {
                type: Boolean,
                default: true
              },
              verticalCompact: {
                type: Boolean,
                default: true
              },
              layout: {
                type: Array,
                required: true
              },
              responsive: {
                type: Boolean,
                default: false
              },
              responsiveLayouts: {
                type: Object,
                default: function _default() {
                  return {};
                }
              },
              breakpoints: {
                type: Object,
                default: function _default() {
                  return {
                    lg: 1200,
                    md: 996,
                    sm: 768,
                    xs: 480,
                    xxs: 0
                  };
                }
              },
              cols: {
                type: Object,
                default: function _default() {
                  return {
                    lg: 12,
                    md: 10,
                    sm: 6,
                    xs: 4,
                    xxs: 2
                  };
                }
              },
              preventCollision: {
                type: Boolean,
                default: false
              },
              useStyleCursor: {
                type: Boolean,
                default: true
              }
            },
            data: function data() {
              return {
                eventBus: mitt_es(),
                width: null,
                mergedStyle: {},
                lastLayoutLength: 0,
                isDragging: false,
                placeholder: {
                  x: 0,
                  y: 0,
                  w: 0,
                  h: 0,
                  i: -1
                },
                layouts: {},
                // array to store all layouts from different breakpoints
                lastBreakpoint: null,
                // store last active breakpoint
                originalLayout: null
                // store original Layout
                // layout: JSON.parse(JSON.stringify(this.value)),
              };
            },
            created: function created() {
              var self2 = this;
              self2.resizeEventHandler = function(_ref) {
                var eventType = _ref.eventType, i = _ref.i, x = _ref.x, y = _ref.y, h = _ref.h, w = _ref.w;
                self2.resizeEvent(eventType, i, x, y, h, w);
              };
              self2.dragEventHandler = function(_ref2) {
                var eventType = _ref2.eventType, i = _ref2.i, x = _ref2.x, y = _ref2.y, h = _ref2.h, w = _ref2.w;
                self2.dragEvent(eventType, i, x, y, h, w);
              };
              self2.eventBus.on("resizeEvent", self2.resizeEventHandler);
              self2.eventBus.on("dragEvent", self2.dragEventHandler);
              self2.$emit("layout-created", self2.layout);
            },
            beforeUnmount: function beforeUnmount() {
              this.eventBus.off("resizeEvent", this.resizeEventHandler);
              this.eventBus.off("dragEvent", this.dragEventHandler);
              removeWindowEventListener("resize", this.onWindowResize);
              if (this.erd) {
                this.erd.uninstall(this.$refs.item);
              }
            },
            beforeMount: function beforeMount() {
              this.$emit("layout-before-mount", this.layout);
            },
            mounted: function mounted() {
              this.$emit("layout-mounted", this.layout);
              this.$nextTick(function() {
                validateLayout(this.layout);
                this.originalLayout = this.layout;
                var self2 = this;
                this.$nextTick(function() {
                  self2.onWindowResize();
                  self2.initResponsiveFeatures();
                  addWindowEventListener("resize", self2.onWindowResize);
                  compact(self2.layout, self2.verticalCompact);
                  self2.$emit("layout-updated", self2.layout);
                  self2.updateHeight();
                  self2.$nextTick(function() {
                    this.erd = elementResizeDetectorMaker({
                      strategy: "scroll",
                      //<- For ultra performance.
                      // See https://github.com/wnr/element-resize-detector/issues/110 about callOnAdd.
                      callOnAdd: false
                    });
                    this.erd.listenTo(self2.$refs.item, function() {
                      self2.onWindowResize();
                    });
                  });
                });
              });
            },
            watch: {
              width: function width(newval, oldval) {
                var self2 = this;
                this.$nextTick(function() {
                  var _this = this;
                  this.eventBus.emit("updateWidth", this.width);
                  if (oldval === null) {
                    this.$nextTick(function() {
                      _this.$emit("layout-ready", self2.layout);
                    });
                  }
                  this.updateHeight();
                });
              },
              layout: function layout() {
                this.layoutUpdate();
              },
              colNum: function colNum(val) {
                this.eventBus.emit("setColNum", val);
              },
              rowHeight: function rowHeight() {
                this.eventBus.emit("setRowHeight", this.rowHeight);
              },
              isDraggable: function isDraggable() {
                this.eventBus.emit("setDraggable", this.isDraggable);
              },
              isResizable: function isResizable() {
                this.eventBus.emit("setResizable", this.isResizable);
              },
              responsive: function responsive() {
                if (!this.responsive) {
                  this.$emit("update:layout", this.originalLayout);
                  this.eventBus.emit("setColNum", this.colNum);
                }
                this.onWindowResize();
              },
              maxRows: function maxRows() {
                this.eventBus.emit("setMaxRows", this.maxRows);
              },
              margin: function margin() {
                this.updateHeight();
              }
            },
            methods: {
              layoutUpdate: function layoutUpdate() {
                if (this.layout !== void 0 && this.originalLayout !== null) {
                  if (this.layout.length !== this.originalLayout.length) {
                    var diff = this.findDifference(this.layout, this.originalLayout);
                    if (diff.length > 0) {
                      if (this.layout.length > this.originalLayout.length) {
                        this.originalLayout = this.originalLayout.concat(diff);
                      } else {
                        this.originalLayout = this.originalLayout.filter(function(obj) {
                          return !diff.some(function(obj2) {
                            return obj.i === obj2.i;
                          });
                        });
                      }
                    }
                    this.lastLayoutLength = this.layout.length;
                    this.initResponsiveFeatures();
                  }
                  compact(this.layout, this.verticalCompact);
                  this.eventBus.emit("updateWidth", this.width);
                  this.updateHeight();
                  this.$emit("layout-updated", this.layout);
                }
              },
              updateHeight: function updateHeight() {
                this.mergedStyle = {
                  height: this.containerHeight()
                };
              },
              onWindowResize: function onWindowResize() {
                if (this.$refs !== null && this.$refs.item !== null && this.$refs.item !== void 0) {
                  this.width = this.$refs.item.offsetWidth;
                }
                this.eventBus.emit("resizeEvent", {});
              },
              containerHeight: function containerHeight() {
                if (!this.autoSize) return;
                var containerHeight2 = bottom(this.layout) * (this.rowHeight + this.margin[1]) + this.margin[1] + "px";
                return containerHeight2;
              },
              dragEvent: function dragEvent(eventName, id, x, y, h, w) {
                var l = getLayoutItem(this.layout, id);
                if (l === void 0 || l === null) {
                  l = {
                    x: 0,
                    y: 0
                  };
                }
                if (eventName === "dragmove" || eventName === "dragstart") {
                  this.placeholder.i = id;
                  this.placeholder.x = l.x;
                  this.placeholder.y = l.y;
                  this.placeholder.w = w;
                  this.placeholder.h = h;
                  this.$nextTick(function() {
                    this.isDragging = true;
                  });
                  this.eventBus.emit("updateWidth", this.width);
                } else {
                  this.$nextTick(function() {
                    this.isDragging = false;
                  });
                }
                this.$emit("update:layout", moveElement(this.layout, l, x, y, true, this.preventCollision));
                compact(this.layout, this.verticalCompact);
                this.eventBus.emit("compact");
                this.updateHeight();
                if (eventName === "dragend") this.$emit("layout-updated", this.layout);
              },
              resizeEvent: function resizeEvent(eventName, id, x, y, h, w) {
                var l = getLayoutItem(this.layout, id);
                if (l === void 0 || l === null) {
                  l = {
                    h: 0,
                    w: 0
                  };
                }
                var hasCollisions;
                if (this.preventCollision) {
                  var collisions = getAllCollisions(this.layout, _objectSpread(_objectSpread({}, l), {}, {
                    w,
                    h
                  })).filter(function(layoutItem) {
                    return layoutItem.i !== l.i;
                  });
                  hasCollisions = collisions.length > 0;
                  if (hasCollisions) {
                    var leastX = Infinity, leastY = Infinity;
                    collisions.forEach(function(layoutItem) {
                      if (layoutItem.x > l.x) leastX = Math.min(leastX, layoutItem.x);
                      if (layoutItem.y > l.y) leastY = Math.min(leastY, layoutItem.y);
                    });
                    if (Number.isFinite(leastX)) l.w = leastX - l.x;
                    if (Number.isFinite(leastY)) l.h = leastY - l.y;
                  }
                }
                if (!hasCollisions) {
                  l.w = w;
                  l.h = h;
                }
                if (eventName === "resizestart" || eventName === "resizemove") {
                  this.placeholder.i = id;
                  this.placeholder.x = x;
                  this.placeholder.y = y;
                  this.placeholder.w = l.w;
                  this.placeholder.h = l.h;
                  this.$nextTick(function() {
                    this.isDragging = true;
                  });
                  this.eventBus.emit("updateWidth", this.width);
                } else {
                  this.$nextTick(function() {
                    this.isDragging = false;
                  });
                }
                if (this.responsive) this.responsiveGridLayout();
                compact(this.layout, this.verticalCompact);
                this.eventBus.emit("compact");
                this.updateHeight();
                if (eventName === "resizeend") this.$emit("layout-updated", this.layout);
              },
              // finds or generates new layouts for set breakpoints
              responsiveGridLayout: function responsiveGridLayout() {
                var newBreakpoint = getBreakpointFromWidth(this.breakpoints, this.width);
                var newCols = getColsFromBreakpoint(newBreakpoint, this.cols);
                if (this.lastBreakpoint != null && !this.layouts[this.lastBreakpoint]) this.layouts[this.lastBreakpoint] = cloneLayout(this.layout);
                var layout = findOrGenerateResponsiveLayout(this.originalLayout, this.layouts, this.breakpoints, newBreakpoint, this.lastBreakpoint, newCols, this.verticalCompact);
                this.layouts[newBreakpoint] = layout;
                if (this.lastBreakpoint !== newBreakpoint) {
                  this.$emit("breakpoint-changed", newBreakpoint, layout);
                }
                this.$emit("update:layout", layout);
                this.lastBreakpoint = newBreakpoint;
                this.eventBus.emit("setColNum", getColsFromBreakpoint(newBreakpoint, this.cols));
              },
              // clear all responsive layouts
              initResponsiveFeatures: function initResponsiveFeatures() {
                this.layouts = Object.assign({}, this.responsiveLayouts);
              },
              // find difference in layouts
              findDifference: function findDifference(layout, originalLayout) {
                var uniqueResultOne = layout.filter(function(obj) {
                  return !originalLayout.some(function(obj2) {
                    return obj.i === obj2.i;
                  });
                });
                var uniqueResultTwo = originalLayout.filter(function(obj) {
                  return !layout.some(function(obj2) {
                    return obj.i === obj2.i;
                  });
                });
                return uniqueResultOne.concat(uniqueResultTwo);
              }
            }
          };
          var GridLayoutvue_type_style_index_0_id_fc5948f6_lang_css = __webpack_require__("6521");
          GridLayoutvue_type_script_lang_js.render = GridLayoutvue_type_template_id_fc5948f6_render;
          var GridLayout = GridLayoutvue_type_script_lang_js;
          var components_install = function install(app) {
            app.component("grid-layout", GridLayout);
            app.component("grid-item", GridItem);
          };
          var components = components_install;
          var entry_lib = __webpack_exports__["default"] = components;
        }
      ),
      /***/
      "fca0": (
        /***/
        function(module2, exports2, __webpack_require__) {
          var $export = __webpack_require__("5ca1");
          var _isFinite = __webpack_require__("7726").isFinite;
          $export($export.S, "Number", {
            isFinite: function isFinite(it) {
              return typeof it == "number" && _isFinite(it);
            }
          });
        }
      ),
      /***/
      "fdef": (
        /***/
        function(module2, exports2) {
          module2.exports = "	\n\v\f\r   ᠎             　\u2028\u2029\uFEFF";
        }
      )
      /******/
    })["default"];
  }
});
export default require_vue_grid_layout_common();
/*! Bundled license information:

vue-grid-layout/dist/vue-grid-layout.common.js:
  (*! vue-grid-layout - 3.0.0-beta1 | (c) 2015, 2021  Gustavo Santos (JBay Solutions) <<EMAIL>> (http://www.jbaysolutions.com) | https://github.com/jbaysolutions/vue-grid-layout *)
*/
//# sourceMappingURL=vue-grid-layout.js.map
