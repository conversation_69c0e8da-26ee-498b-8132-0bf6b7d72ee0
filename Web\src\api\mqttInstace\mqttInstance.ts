﻿import {useBase<PERSON>pi} from '/@/api/base';

// MQTT 实例信息表接口服务
export const useMqttInstanceApi = () => {
	const baseApi = useBaseApi("mqttInstance");
	return {
		// 分页查询MQTT 实例信息表
		page: baseApi.page,
		// 查看MQTT 实例信息表详细
		detail: baseApi.detail,
		// 新增MQTT 实例信息表
		add: baseApi.add,
		// 更新MQTT 实例信息表
		update: baseApi.update,
		// 删除MQTT 实例信息表
		delete: baseApi.delete,
		// 批量删除MQTT 实例信息表
		batchDelete: baseApi.batchDelete,
	}
}

// MQTT 实例信息表实体
export interface MqttInstance {
	// 主键Id
	id: number;
	// 实例名称
	instanceName: string;
	// 实例编码
	instanceCode: string;
	// 实例类型（EMQX、阿里云等）
	instanceType: string;
	// 服务器地址
	serverHost: string;
	// 服务器端口
	serverPort: number;
	// API地址
	apiHost: string;
	// API端口
	apiPort: number;
	// API用户名
	apiUsername: string;
	// API密码（加密存储）
	apiPassword: string;
	// 实例状态
	status: string;
	// 是否启用SSL（0:否，1:是）
	enableSsl: boolean;
	// SSL端口
	sslPort: number;
	// 是否启用WebSocket（0:否，1:是）
	enableWebSocket: boolean;
	// WebSocket端口
	wsPort: number;
	// WebSocket SSL端口
	wssPort: number;
	// 最大连接数
	maxConnections: number;
	// 当前连接数
	currentConnections: number;
	// 阿里云产品Key
	aliyunProductKey: string;
	// 阿里云区域ID
	aliyunRegionId: string;
	// 阿里云实例ID
	aliyunInstanceId: string;
	// 设备ID前缀
	deviceIdPrefix: string;
	// 组ID前缀
	groupIdPrefix: string;
	// 是否兼容阿里云（0:否，1:是）
	aliyunCompatible: boolean;
	// 是否启用（0:否，1:是）
	isEnabled: boolean;
	// 配置JSON
	configJson: string;
	// 最后心跳时间
	lastHeartbeat: string;
	// 总消息数
	totalMessages: number;
	// 总字节数
	totalBytes: number;
	// 备注
	remark: string;
	// 创建时间
	createTime: string;
	// 更新时间
	updateTime: string;
	// 创建者Id
	createUserId: number;
	// 修改者Id
	updateUserId: number;
	// 软删除
	isDelete?: boolean;
	// 最后活动时间
	lastActivity: string;
	// 创建者姓名
	createUserName: string;
	// 修改者姓名
	updateUserName: string;
}