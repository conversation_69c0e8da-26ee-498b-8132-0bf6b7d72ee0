<script lang="ts" setup name="mqttDeviceGroup">
import { ref, reactive, onMounted } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { downloadStreamFile } from "/@/utils/download";
import { useMqttDeviceGroupApi } from '/@/api/device_group/mqttDeviceGroup';
import editDialog from '/@/views/device_group/mqttDeviceGroup/component/editDialog.vue'
import printDialog from '/@/views/system/print/component/hiprint/preview.vue'
import ModifyRecord from '/@/components/table/modifyRecord.vue';
import { 
  Search, 
  Plus, 
  Delete, 
  Edit, 
  Refresh, 
  Download, 
  Upload,
  Collection,
  Monitor,
  Connection,
  Warning,
  CircleCheck,
  CircleClose
} from '@element-plus/icons-vue';
import ImportData from "/@/components/table/importData.vue";

const mqttDeviceGroupApi = useMqttDeviceGroupApi();
const printDialogRef = ref();
const editDialogRef = ref();
const importDataRef = ref();
const state = reactive({
  exportLoading: false,
  tableLoading: false,
  stores: {},
  showAdvanceQueryUI: false,
  dropdownData: {} as any,
  selectData: [] as any[],
  tableQueryParams: {} as any,
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 0,
    field: 'createTime', // 默认的排序字段
    order: 'descending', // 排序方向
    descStr: 'descending', // 降序排序的关键字符
  },
  tableData: [],
});

// 页面加载时
onMounted(async () => {
});

// 查询操作
const handleQuery = async (params: any = {}) => {
  state.tableLoading = true;
  state.tableParams = Object.assign(state.tableParams, params);
  const result = await mqttDeviceGroupApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then(res => res.data.result);
  state.tableParams.total = result?.total;
  state.tableData = result?.items ?? [];
  state.tableLoading = false;
};

// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};

// 删除
const delMqttDeviceGroup = (row: any) => {
  ElMessageBox.confirm(`确定要删除设备组：${row.groupName}吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await mqttDeviceGroupApi.delete({ id: row.id });
    handleQuery();
    ElMessage.success("删除成功");
  }).catch(() => {});
};

// 获取设备组类型颜色
const getGroupTypeColor = (type: string) => {
  switch (type) {
    case 'Production': return 'success';
    case 'Test': return 'warning';
    case 'Development': return 'info';
    default: return 'primary';
  }
};

// 获取设备组类型图标
const getGroupTypeIcon = (type: string) => {
  switch (type) {
    case 'Production': return CircleCheck;
    case 'Test': return Warning;
    case 'Development': return Monitor;
    default: return Collection;
  }
};

// 获取设备组类型标签
const getGroupTypeLabel = (type: string) => {
  switch (type) {
    case 'Production': return '生产环境';
    case 'Test': return '测试环境';
    case 'Development': return '开发环境';
    default: return type;
  }
};

// 获取设备总数
const getTotalDeviceCount = () => {
  return state.tableData.reduce((total, group) => {
    return total + (group.currentDeviceCount || 0);
  }, 0);
};

// 查看设备组详情
const viewGroupDetails = (row: any) => {
  ElMessageBox.alert(
    `<div style="text-align: left;">
      <p><strong>实例ID:</strong> ${row.instanceId || '-'}</p>
      <p><strong>设备组标识:</strong> ${row.groupId || '-'}</p>
      <p><strong>设备组名称:</strong> ${row.groupName || '-'}</p>
      <p><strong>设备组类型:</strong> ${getGroupTypeLabel(row.groupType) || '-'}</p>
      <p><strong>产品Key:</strong> ${row.productKey || '-'}</p>
      <p><strong>当前设备数量:</strong> ${row.currentDeviceCount || 0}</p>
      <p><strong>最大设备数量:</strong> ${row.maxDeviceCount || 0}</p>
      <p><strong>描述:</strong> ${row.description || '-'}</p>
      <p><strong>创建时间:</strong> ${row.createTime || '-'}</p>
    </div>`,
    '设备组详情',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确定'
    }
  );
};

// 监控设备组
const monitorGroup = (row: any) => {
  ElMessage.info(`正在打开设备组 ${row.groupName} 的监控面板...`);
  // 这里可以跳转到监控页面或打开监控弹窗
};

// 批量删除
const batchDelMqttDeviceGroup = () => {
  ElMessageBox.confirm(`确定要删除${state.selectData.length}条记录吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    await mqttDeviceGroupApi.batchDelete(state.selectData.map(u => ({ id: u.id }) )).then(res => {
      ElMessage.success(`成功批量删除${res.data.result}条记录`);
      handleQuery();
    });
  }).catch(() => {});
};

// 导出数据
const exportMqttDeviceGroupCommand = async (command: string) => {
  try {
    state.exportLoading = true;
    if (command === 'select') {
      const params = Object.assign({}, state.tableQueryParams, state.tableParams, { selectKeyList: state.selectData.map(u => u.id) });
      await mqttDeviceGroupApi.exportData(params).then(res => downloadStreamFile(res));
    } else if (command === 'current') {
      const params = Object.assign({}, state.tableQueryParams, state.tableParams);
      await mqttDeviceGroupApi.exportData(params).then(res => downloadStreamFile(res));
    } else if (command === 'all') {
      const params = Object.assign({}, state.tableQueryParams, state.tableParams, { page: 1, pageSize: 99999999 });
      await mqttDeviceGroupApi.exportData(params).then(res => downloadStreamFile(res));
    }
  } finally {
    state.exportLoading = false;
  }
}

handleQuery();
</script>
<template>
  <div class="mqtt-device-group-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-icon class="header-icon"><Collection /></el-icon>
          <div class="header-text">
            <h2>设备组管理</h2>
            <p>管理MQTT设备分组配置和权限控制</p>
          </div>
        </div>
        <div class="header-stats">
          <div class="stat-card">
            <div class="stat-number">{{ state.tableParams.total }}</div>
            <div class="stat-label">总设备组</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ state.tableData.filter(item => item.isEnabled).length }}</div>
            <div class="stat-label">活跃设备组</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ getTotalDeviceCount() }}</div>
            <div class="stat-label">设备总数</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never" v-loading="state.exportLoading">
      <div class="search-container">
        <div class="search-form">
          <el-form :model="state.tableQueryParams" inline>
            <el-form-item label="关键字">
              <el-input 
                v-model="state.tableQueryParams.keyword" 
                placeholder="请输入模糊查询关键字" 
                :prefix-icon="Search"
                clearable
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="实例ID" v-if="state.showAdvanceQueryUI">
              <el-input 
                v-model="state.tableQueryParams.instanceId" 
                placeholder="请输入实例ID" 
                clearable
                style="width: 180px"
              />
            </el-form-item>
            <el-form-item label="组ID" v-if="state.showAdvanceQueryUI">
              <el-input 
                v-model="state.tableQueryParams.groupId" 
                placeholder="请输入组ID" 
                clearable
                style="width: 180px"
              />
            </el-form-item>
            <el-form-item label="组名称" v-if="state.showAdvanceQueryUI">
              <el-input 
                v-model="state.tableQueryParams.groupName" 
                placeholder="请输入组名称" 
                clearable
                style="width: 180px"
              />
            </el-form-item>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="设备组类型（Production、Test、Development等）">
              <el-input v-model="state.tableQueryParams.groupType" clearable placeholder="请输入设备组类型（Production、Test、Development等）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="产品Key（阿里云IoT产品标识）">
              <el-input v-model="state.tableQueryParams.productKey" clearable placeholder="请输入产品Key（阿里云IoT产品标识）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="设备组描述">
              <el-input v-model="state.tableQueryParams.description" clearable placeholder="请输入设备组描述"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="最大设备数量">
              <el-input-number v-model="state.tableQueryParams.maxDeviceCount"  clearable placeholder="请输入最大设备数量"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="当前设备数量">
              <el-input-number v-model="state.tableQueryParams.currentDeviceCount"  clearable placeholder="请输入当前设备数量"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="允许的主题模式（JSON数组）">
              <el-input v-model="state.tableQueryParams.allowedTopicPatterns" clearable placeholder="请输入允许的主题模式（JSON数组）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="禁止的主题模式（JSON数组）">
              <el-input v-model="state.tableQueryParams.deniedTopicPatterns" clearable placeholder="请输入禁止的主题模式（JSON数组）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="默认QoS等级（0,1,2）">
                  <el-select clearable filterable v-model="state.tableQueryParams.defaultQosLevel" placeholder="请选择默认QoS等级（0,1,2）"> 
                    <el-option     value="true" label="是" /> 
                    <el-option     value="false" label="否" />  
                  </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="最大消息大小（字节）">
              <el-input-number v-model="state.tableQueryParams.maxMessageSize"  clearable placeholder="请输入最大消息大小（字节）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="消息速率限制（消息/秒）">
              <el-input-number v-model="state.tableQueryParams.messageRateLimit"  clearable placeholder="请输入消息速率限制（消息/秒）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="字节速率限制（字节/秒）">
              <el-input-number v-model="state.tableQueryParams.byteRateLimit"  clearable placeholder="请输入字节速率限制（字节/秒）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="最大连接数">
              <el-input-number v-model="state.tableQueryParams.maxConnections"  clearable placeholder="请输入最大连接数"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="连接超时时间（秒）">
              <el-input-number v-model="state.tableQueryParams.connectionTimeout"  clearable placeholder="请输入连接超时时间（秒）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="心跳超时时间（秒）">
              <el-input-number v-model="state.tableQueryParams.keepAliveTimeout"  clearable placeholder="请输入心跳超时时间（秒）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="是否允许保留消息">
                  <el-select clearable filterable v-model="state.tableQueryParams.enableRetainMessage" placeholder="请选择是否允许保留消息"> 
                    <el-option     value="true" label="是" /> 
                    <el-option     value="false" label="否" />  
                  </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="是否允许通配符订阅">
                  <el-select clearable filterable v-model="state.tableQueryParams.enableWildcardSubscription" placeholder="请选择是否允许通配符订阅"> 
                    <el-option     value="true" label="是" /> 
                    <el-option     value="false" label="否" />  
                  </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="是否允许共享订阅">
                  <el-select clearable filterable v-model="state.tableQueryParams.enableSharedSubscription" placeholder="请选择是否允许共享订阅"> 
                    <el-option     value="true" label="是" /> 
                    <el-option     value="false" label="否" />  
                  </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="IP白名单（JSON数组）">
              <el-input v-model="state.tableQueryParams.ipWhitelist" clearable placeholder="请输入IP白名单（JSON数组）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="IP黑名单（JSON数组）">
              <el-input v-model="state.tableQueryParams.ipBlacklist" clearable placeholder="请输入IP黑名单（JSON数组）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="允许连接的时间范围（JSON）">
              <el-input v-model="state.tableQueryParams.allowedTimeRanges" clearable placeholder="请输入允许连接的时间范围（JSON）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="安全级别（Low、Medium、High）">
              <el-input v-model="state.tableQueryParams.securityLevel" clearable placeholder="请输入安全级别（Low、Medium、High）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="是否要求加密连接">
                  <el-select clearable filterable v-model="state.tableQueryParams.encryptionRequired" placeholder="请选择是否要求加密连接"> 
                    <el-option     value="true" label="是" /> 
                    <el-option     value="false" label="否" />  
                  </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="是否要求客户端证书">
                  <el-select clearable filterable v-model="state.tableQueryParams.certificateRequired" placeholder="请选择是否要求客户端证书"> 
                    <el-option     value="true" label="是" /> 
                    <el-option     value="false" label="否" />  
                  </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="认证模式（Signature、Username、JWT、Certificate）">
              <el-input v-model="state.tableQueryParams.authMode" clearable placeholder="请输入认证模式（Signature、Username、JWT、Certificate）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="签名算法（hmacsha1、hmacsha256、hmacmd5）">
              <el-input v-model="state.tableQueryParams.signMethod" clearable placeholder="请输入签名算法（hmacsha1、hmacsha256、hmacmd5）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="AccessKey ID（阿里云认证）">
              <el-input v-model="state.tableQueryParams.accessKeyId" clearable placeholder="请输入AccessKey ID（阿里云认证）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="AccessKey Secret（加密存储）">
              <el-input v-model="state.tableQueryParams.accessKeySecret" clearable placeholder="请输入AccessKey Secret（加密存储）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="JWT密钥">
              <el-input v-model="state.tableQueryParams.jwtSecret" clearable placeholder="请输入JWT密钥"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="JWT过期时间（秒）">
              <el-input-number v-model="state.tableQueryParams.jwtExpiry"  clearable placeholder="请输入JWT过期时间（秒）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="CA证书">
              <el-input v-model="state.tableQueryParams.caCertificate" clearable placeholder="请输入CA证书"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="是否启用">
                  <el-select clearable filterable v-model="state.tableQueryParams.isEnabled" placeholder="请选择是否启用"> 
                    <el-option     value="true" label="是" /> 
                    <el-option     value="false" label="否" />  
                  </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="过期时间">
              <el-date-picker type="daterange" v-model="state.tableQueryParams.expireTimeRange"  value-format="YYYY-MM-DD HH:mm:ss" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="最后活动时间">
              <el-date-picker type="daterange" v-model="state.tableQueryParams.lastActivityRange"  value-format="YYYY-MM-DD HH:mm:ss" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="已创建设备数">
              <el-input-number v-model="state.tableQueryParams.createdDeviceCount"  clearable placeholder="请输入已创建设备数"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="在线设备数">
              <el-input-number v-model="state.tableQueryParams.onlineDeviceCount"  clearable placeholder="请输入在线设备数"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="总消息数">
              <el-input v-model="state.tableQueryParams.totalMessageCount" clearable placeholder="请输入总消息数"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="总字节数">
              <el-input v-model="state.tableQueryParams.totalByteCount" clearable placeholder="请输入总字节数"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="最后消息时间">
              <el-date-picker type="daterange" v-model="state.tableQueryParams.lastMessageTimeRange"  value-format="YYYY-MM-DD HH:mm:ss" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="告警规则（JSON）">
              <el-input v-model="state.tableQueryParams.alertRules" clearable placeholder="请输入告警规则（JSON）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="监控配置（JSON）">
              <el-input v-model="state.tableQueryParams.monitorConfig" clearable placeholder="请输入监控配置（JSON）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="扩展属性（JSON）">
              <el-input v-model="state.tableQueryParams.extendedProperties" clearable placeholder="请输入扩展属性（JSON）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="标签（JSON）">
              <el-input v-model="state.tableQueryParams.tags" clearable placeholder="请输入标签（JSON）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="备注">
              <el-input v-model="state.tableQueryParams.remark" clearable placeholder="请输入备注"/>
            </el-form-item>
          </el-col>
            <el-form-item>
              <el-button type="primary" :icon="Search" @click="handleQuery" v-auth="'mqttDeviceGroup:page'" v-reclick="1000">
                查询
              </el-button>
              <el-button :icon="Refresh" @click="() => state.tableQueryParams = {}">
                重置
              </el-button>
              <el-button :icon="state.showAdvanceQueryUI ? 'ele-ZoomOut' : 'ele-ZoomIn'" @click="() => state.showAdvanceQueryUI = !state.showAdvanceQueryUI">
                {{ state.showAdvanceQueryUI ? '隐藏' : '高级查询' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="action-buttons">
          <el-button 
            type="primary" 
            :icon="Plus" 
            @click="editDialogRef.openDialog(null, '新增MQTT设备组信息表')" 
            v-auth="'mqttDeviceGroup:add'"
          >
            新增设备组
          </el-button>
          <el-button 
            type="danger" 
            :icon="Delete" 
            @click="batchDelMqttDeviceGroup" 
            v-auth="'mqttDeviceGroup:batchDelete'"
            :disabled="state.selectData.length == 0"
          >
            批量删除
          </el-button>
          <el-button 
            type="success" 
            :icon="Download" 
            @click="exportMqttDeviceGroupCommand('all')"
          >
            导出
          </el-button>
          <el-button 
            type="warning" 
            :icon="Upload" 
            @click="importDataRef.openDialog()"
          >
            导入
          </el-button>
        </div>
      </div>
    </el-card>

     <!-- 高级查询面板 -->
     <el-card v-if="state.showAdvanceQueryUI" class="advanced-search-card" shadow="never">
       <el-form :model="state.tableQueryParams" ref="queryForm" labelWidth="120px">
         <el-row :gutter="20">
           <el-col :xs="24" :sm="12" :md="8" :lg="6" class="mb10">
             <el-form-item label="设备组类型">
               <el-select v-model="state.tableQueryParams.groupType" placeholder="请选择设备组类型" clearable style="width: 100%">
                 <el-option label="生产环境" value="Production">
                   <div class="group-type-option">
                     <el-icon><CircleCheck /></el-icon>
                     <span>生产环境</span>
                   </div>
                 </el-option>
                 <el-option label="测试环境" value="Test">
                   <div class="group-type-option">
                     <el-icon><Warning /></el-icon>
                     <span>测试环境</span>
                   </div>
                 </el-option>
                 <el-option label="开发环境" value="Development">
                   <div class="group-type-option">
                     <el-icon><Monitor /></el-icon>
                     <span>开发环境</span>
                   </div>
                 </el-option>
               </el-select>
             </el-form-item>
           </el-col>
           <!-- 其他高级查询字段保持原有结构 -->
         </el-row>
       </el-form>
     </el-card>

     <!-- 数据表格 -->
     <el-card class="table-card" shadow="never">
       <el-table :data="state.tableData" @selection-change="(val: any[]) => { state.selectData = val; }" style="width: 100%" v-loading="state.tableLoading" tooltip-effect="light" row-key="id" @sort-change="sortChange" border>
         <el-table-column type="selection" width="40" align="center" v-if="auth('mqttDeviceGroup:batchDelete') || auth('mqttDeviceGroup:export')" />
         <el-table-column type="index" label="序号" width="55" align="center"/>
         <el-table-column prop='instanceId' label='实例ID' show-overflow-tooltip width="120" />
         <el-table-column prop='groupId' label='设备组标识' show-overflow-tooltip width="150" />
         <el-table-column prop='groupName' label='设备组名称' show-overflow-tooltip width="150" />
         <el-table-column prop='groupType' label='设备组类型' show-overflow-tooltip width="120">
           <template #default="{ row }">
             <el-tag 
               :type="getGroupTypeColor(row.groupType)" 
               :icon="getGroupTypeIcon(row.groupType)"
               size="small"
             >
               {{ getGroupTypeLabel(row.groupType) }}
             </el-tag>
           </template>
         </el-table-column>
        <el-table-column prop='productKey' label='产品Key（阿里云IoT产品标识）' show-overflow-tooltip />
        <el-table-column prop='description' label='设备组描述' show-overflow-tooltip />
        <el-table-column prop='maxDeviceCount' label='最大设备数量' show-overflow-tooltip />
        <el-table-column prop='currentDeviceCount' label='当前设备数量' show-overflow-tooltip />
        <el-table-column prop='allowedTopicPatterns' label='允许的主题模式（JSON数组）' show-overflow-tooltip />
        <el-table-column prop='deniedTopicPatterns' label='禁止的主题模式（JSON数组）' show-overflow-tooltip />
        <el-table-column prop='defaultQosLevel' label='默认QoS等级（0,1,2）' show-overflow-tooltip>
          <template #default="scope">
            <el-tag v-if="scope.row.defaultQosLevel"> 是 </el-tag>
            <el-tag type="danger" v-else> 否 </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop='maxMessageSize' label='最大消息大小（字节）' show-overflow-tooltip />
        <el-table-column prop='messageRateLimit' label='消息速率限制（消息/秒）' show-overflow-tooltip />
        <el-table-column prop='byteRateLimit' label='字节速率限制（字节/秒）' show-overflow-tooltip />
        <el-table-column prop='maxConnections' label='最大连接数' show-overflow-tooltip />
        <el-table-column prop='connectionTimeout' label='连接超时时间（秒）' show-overflow-tooltip />
        <el-table-column prop='keepAliveTimeout' label='心跳超时时间（秒）' show-overflow-tooltip />
        <el-table-column prop='enableRetainMessage' label='是否允许保留消息' show-overflow-tooltip>
          <template #default="scope">
            <el-tag v-if="scope.row.enableRetainMessage"> 是 </el-tag>
            <el-tag type="danger" v-else> 否 </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop='enableWildcardSubscription' label='是否允许通配符订阅' show-overflow-tooltip>
          <template #default="scope">
            <el-tag v-if="scope.row.enableWildcardSubscription"> 是 </el-tag>
            <el-tag type="danger" v-else> 否 </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop='enableSharedSubscription' label='是否允许共享订阅' show-overflow-tooltip>
          <template #default="scope">
            <el-tag v-if="scope.row.enableSharedSubscription"> 是 </el-tag>
            <el-tag type="danger" v-else> 否 </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop='ipWhitelist' label='IP白名单（JSON数组）' show-overflow-tooltip />
        <el-table-column prop='ipBlacklist' label='IP黑名单（JSON数组）' show-overflow-tooltip />
        <el-table-column prop='allowedTimeRanges' label='允许连接的时间范围（JSON）' show-overflow-tooltip />
        <el-table-column prop='securityLevel' label='安全级别（Low、Medium、High）' show-overflow-tooltip />
        <el-table-column prop='encryptionRequired' label='是否要求加密连接' show-overflow-tooltip>
          <template #default="scope">
            <el-tag v-if="scope.row.encryptionRequired"> 是 </el-tag>
            <el-tag type="danger" v-else> 否 </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop='certificateRequired' label='是否要求客户端证书' show-overflow-tooltip>
          <template #default="scope">
            <el-tag v-if="scope.row.certificateRequired"> 是 </el-tag>
            <el-tag type="danger" v-else> 否 </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop='authMode' label='认证模式（Signature、Username、JWT、Certificate）' show-overflow-tooltip />
        <el-table-column prop='signMethod' label='签名算法（hmacsha1、hmacsha256、hmacmd5）' show-overflow-tooltip />
        <el-table-column prop='accessKeyId' label='AccessKey ID（阿里云认证）' show-overflow-tooltip />
        <el-table-column prop='accessKeySecret' label='AccessKey Secret（加密存储）' show-overflow-tooltip />
        <el-table-column prop='jwtSecret' label='JWT密钥' show-overflow-tooltip />
        <el-table-column prop='jwtExpiry' label='JWT过期时间（秒）' show-overflow-tooltip />
        <el-table-column prop='caCertificate' label='CA证书' show-overflow-tooltip />
        <el-table-column prop='isEnabled' label='是否启用' show-overflow-tooltip>
          <template #default="scope">
            <el-tag v-if="scope.row.isEnabled"> 是 </el-tag>
            <el-tag type="danger" v-else> 否 </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop='expireTime' label='过期时间' show-overflow-tooltip />
        <el-table-column prop='lastActivity' label='最后活动时间' show-overflow-tooltip />
        <el-table-column prop='createdDeviceCount' label='已创建设备数' show-overflow-tooltip />
        <el-table-column prop='onlineDeviceCount' label='在线设备数' show-overflow-tooltip />
        <el-table-column prop='totalMessageCount' label='总消息数' show-overflow-tooltip />
        <el-table-column prop='totalByteCount' label='总字节数' show-overflow-tooltip />
        <el-table-column prop='lastMessageTime' label='最后消息时间' show-overflow-tooltip />
        <el-table-column prop='alertRules' label='告警规则（JSON）' show-overflow-tooltip />
        <el-table-column prop='monitorConfig' label='监控配置（JSON）' show-overflow-tooltip />
        <el-table-column prop='extendedProperties' label='扩展属性（JSON）' show-overflow-tooltip />
        <el-table-column prop='tags' label='标签（JSON）' show-overflow-tooltip />
        <el-table-column prop='remark' label='备注' show-overflow-tooltip />
        <el-table-column label="修改记录" width="100" align="center" show-overflow-tooltip>
          <template #default="scope">
            <ModifyRecord :data="scope.row" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right" show-overflow-tooltip v-if="auth('mqttDeviceGroup:update') || auth('mqttDeviceGroup:delete')">
           <template #default="scope">
             <div class="action-buttons-group">
               <el-tooltip content="编辑设备组" placement="top">
                 <el-button 
                   size="small" 
                   type="primary" 
                   :icon="Edit" 
                   circle 
                   @click="editDialogRef.openDialog(scope.row, '编辑MQTT设备组管理表')" 
                   v-auth="'mqttDeviceGroup:update'"
                 />
               </el-tooltip>
               <el-tooltip content="删除设备组" placement="top">
                 <el-button 
                   size="small" 
                   type="danger" 
                   :icon="Delete" 
                   circle 
                   @click="delMqttDeviceGroup(scope.row)" 
                   v-auth="'mqttDeviceGroup:delete'"
                 />
               </el-tooltip>
               <el-tooltip content="查看详情" placement="top">
                 <el-button 
                   size="small" 
                   type="info" 
                   icon="ele-View" 
                   circle 
                   @click="viewGroupDetails(scope.row)"
                 />
               </el-tooltip>
               <el-tooltip content="监控状态" placement="top">
                 <el-button 
                   size="small" 
                   type="success" 
                   :icon="Monitor" 
                   circle 
                   @click="monitorGroup(scope.row)"
                 />
               </el-tooltip>
             </div>
           </template>
         </el-table-column>
      </el-table>
      <el-pagination 
              v-model:currentPage="state.tableParams.page"
              v-model:page-size="state.tableParams.pageSize"
              @size-change="(val: any) => handleQuery({ pageSize: val })"
              @current-change="(val: any) => handleQuery({ page: val })"
              layout="total, sizes, prev, pager, next, jumper"
              :page-sizes="[10, 20, 50, 100, 200, 500]"
              :total="state.tableParams.total"
              size="small"
              background />
      <ImportData ref="importDataRef" :import="mqttDeviceGroupApi.importData" :download="mqttDeviceGroupApi.downloadTemplate" v-auth="'mqttDeviceGroup:import'" @refresh="handleQuery"/>
      <printDialog ref="printDialogRef" :title="'打印MQTT设备组管理表'" @reloadTable="handleQuery" />
      <editDialog ref="editDialogRef" @reloadTable="handleQuery" />
    </el-card>
  </div>
</template>
<style scoped>
.mqtt-device-group-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  border-radius: 12px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-icon {
  font-size: 48px;
  opacity: 0.9;
}

.header-text h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.header-text p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

.header-stats {
  display: flex;
  gap: 20px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.15);
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.search-form {
  flex: 1;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.advanced-search-card {
  margin-bottom: 20px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.table-card {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.group-type-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-buttons-group {
  display: flex;
  gap: 5px;
  justify-content: center;
}

:deep(.el-input), :deep(.el-select), :deep(.el-input-number) {
  width: 100%;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background: #f8f9fa;
}

:deep(.el-table th) {
  background: #f8f9fa !important;
  color: #606266;
  font-weight: 600;
}

:deep(.el-pagination) {
  margin-top: 20px;
  justify-content: center;
}

.mb10 {
  margin-bottom: 10px;
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .header-stats {
    justify-content: center;
  }
  
  .search-container {
    flex-direction: column;
  }
  
  .action-buttons {
    justify-content: center;
  }
}
</style>