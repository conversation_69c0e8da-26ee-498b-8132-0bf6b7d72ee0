/* tslint:disable */
 
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { AccountTypeEnum } from './account-type-enum';
 /**
 * 注册方案更新输入参数
 *
 * @export
 * @interface UpdateUserRegWayInput
 */
export interface UpdateUserRegWayInput {

    /**
     * 创建时间
     *
     * @type {Date}
     * @memberof UpdateUserRegWayInput
     */
    createTime?: Date;

    /**
     * 更新时间
     *
     * @type {Date}
     * @memberof UpdateUserRegWayInput
     */
    updateTime?: Date | null;

    /**
     * 创建者Id
     *
     * @type {number}
     * @memberof UpdateUserRegWayInput
     */
    createUserId?: number | null;

    /**
     * 创建者姓名
     *
     * @type {string}
     * @memberof UpdateUserRegWayInput
     */
    createUserName?: string | null;

    /**
     * 修改者Id
     *
     * @type {number}
     * @memberof UpdateUserRegWayInput
     */
    updateUserId?: number | null;

    /**
     * 修改者姓名
     *
     * @type {string}
     * @memberof UpdateUserRegWayInput
     */
    updateUserName?: string | null;

    /**
     * 租户Id
     *
     * @type {number}
     * @memberof UpdateUserRegWayInput
     */
    tenantId?: number | null;

    /**
     * 排序
     *
     * @type {number}
     * @memberof UpdateUserRegWayInput
     */
    orderNo?: number;

    /**
     * 备注
     *
     * @type {string}
     * @memberof UpdateUserRegWayInput
     */
    remark?: string | null;

    /**
     * 方案名称
     *
     * @type {string}
     * @memberof UpdateUserRegWayInput
     */
    name: string;

    /**
     * @type {AccountTypeEnum}
     * @memberof UpdateUserRegWayInput
     */
    accountType?: AccountTypeEnum;

    /**
     * 角色
     *
     * @type {number}
     * @memberof UpdateUserRegWayInput
     */
    roleId: number;

    /**
     * 机构
     *
     * @type {number}
     * @memberof UpdateUserRegWayInput
     */
    orgId: number;

    /**
     * 职位
     *
     * @type {number}
     * @memberof UpdateUserRegWayInput
     */
    posId: number;

    /**
     * 主键Id
     *
     * @type {number}
     * @memberof UpdateUserRegWayInput
     */
    id: number;
}
