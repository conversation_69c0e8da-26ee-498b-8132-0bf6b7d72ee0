/* tslint:disable */
 
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

 /**
 * 数据库表列
 *
 * @export
 * @interface ColumnOuput
 */
export interface ColumnOuput {

    /**
     * 字段名
     *
     * @type {string}
     * @memberof ColumnOuput
     */
    columnName?: string | null;

    /**
     * 实体的Property名
     *
     * @type {string}
     * @memberof ColumnOuput
     */
    propertyName?: string | null;

    /**
     * 字段数据长度
     *
     * @type {number}
     * @memberof ColumnOuput
     */
    columnLength?: number;

    /**
     * 数据库中类型
     *
     * @type {string}
     * @memberof ColumnOuput
     */
    dataType?: string | null;

    /**
     * 字段数据默认值
     *
     * @type {string}
     * @memberof ColumnOuput
     */
    defaultValue?: string | null;

    /**
     * 是否为主键
     *
     * @type {boolean}
     * @memberof ColumnOuput
     */
    isPrimarykey?: boolean;

    /**
     * 是否允许为空
     *
     * @type {boolean}
     * @memberof ColumnOuput
     */
    isNullable?: boolean;

    /**
     * .NET字段类型
     *
     * @type {string}
     * @memberof ColumnOuput
     */
    netType?: string | null;

    /**
     * 字典编码
     *
     * @type {string}
     * @memberof ColumnOuput
     */
    dictTypeCode?: string | null;

    /**
     * 字段描述
     *
     * @type {string}
     * @memberof ColumnOuput
     */
    columnComment?: string | null;

    /**
     * 主外键
     *
     * @type {string}
     * @memberof ColumnOuput
     */
    columnKey?: string | null;
}
