import "./chunk-BQAX7FSC.js";
import {
  Fragment,
  Teleport,
  Transition,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createSlots,
  createVNode,
  defineComponent,
  h,
  nextTick,
  normalizeClass,
  normalizeStyle,
  onBeforeMount,
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  openBlock,
  reactive,
  ref,
  renderList,
  renderSlot,
  resolveComponent,
  resolveDirective,
  toDisplayString,
  vModelText,
  vShow,
  warn,
  watch,
  withCtx,
  withDirectives
} from "./chunk-YRXSRAMJ.js";
import "./chunk-PLDDJCW6.js";

// node_modules/vue3-tree-org/lib/index.esm.js
var R = "vue3-tree-org";
var F = "4.2.2";
var A = function(e) {
  return "[object Object]" === Object.prototype.toString.call(e);
};
var B = function(e) {
  return "string" == typeof e;
};
var L = function(e) {
  return "number" == typeof e;
};
var T = { print: (e, n, t) => B(n) || "boolean" == typeof t, pretty: (e, n, t) => B(n) && B(e) || "string" == typeof t, primary: (e, n) => B(e) || "boolean" == typeof n, success: (e, n) => B(e) || "boolean" == typeof n, info: (e, n) => B(e) || "boolean" == typeof n, warning: (e, n) => B(e) || "boolean" == typeof n, danger: (e, n) => B(e) || "boolean" == typeof n };
function j(e = "default") {
  let n = "";
  switch (e) {
    case "primary":
      n = "#2d8cf0";
      break;
    case "success":
      n = "#19be6b";
      break;
    case "info":
      n = "#909399";
      break;
    case "warning":
      n = "#ff9900";
      break;
    case "danger":
      n = "#ff4d4f";
      break;
    case "default":
      n = "#35495E";
      break;
    default:
      n = e;
  }
  return n;
}
T.print = function(e, n = "default", t = false) {
  return "object" == typeof e ? (console.dir(e), true) : (t ? console.log(`%c ${e} `, `background:${j(n)}; padding: 2px; border-radius: 4px;color: #fff;`) : console.log(`%c ${e} `, `color: ${j(n)};`), true);
}, T.pretty = function(e, n, t = "primary") {
  return console.log(`%c ${e} %c ${n} %c`, `background:${j(t)};border:1px solid ${j(t)}; padding: 1px; border-radius: 4px 0 0 4px; color: #fff;`, `border:1px solid ${j(t)}; padding: 1px; border-radius: 0 4px 4px 0; color: ${j(t)};`, "background:transparent"), true;
}, T.primary = function(e, n = false) {
  return this.print && this.print(e, "primary", n), true;
}, T.success = function(e, n = false) {
  return this.print && this.print(e, "success", n), true;
}, T.info = function(e, n = false) {
  return this.print && this.print(e, "info", n), true;
}, T.warning = function(e, n = false) {
  return this.print && this.print(e, "warning", n), true;
}, T.danger = function(e, n = false) {
  return this.print && this.print(e, "danger", n), true;
};
var V = { async install(e) {
  e.config.globalProperties.$log = T, T.pretty("[" + R + "] " + F, "success");
} };
var Y = /* @__PURE__ */ function() {
  let e = false;
  return { get: () => e, set: (n) => {
    e = n;
  } };
}();
var P = function(e, n, t) {
  const { id: o, children: a } = n;
  if (e[o] === t) return e;
  if (Array.isArray(e[a])) {
    const o2 = e[a];
    for (let e2 = 0, a2 = o2.length; e2 < a2; e2++) {
      const a3 = o2[e2], l = P(a3, n, t);
      if (l) return l;
    }
  }
};
var X = function(e, n) {
  const { parenNode: t, onlyOneNode: o, cloneNodeDrag: a } = n;
  if (t.value) {
    const { keys: l } = n, { id: r, pid: d, children: s } = l, c = t.value.$$data, u = JSON.parse(JSON.stringify(e.$$data));
    a ? (!function(e2, n2, t2) {
      const { children: o2 } = n2;
      if (A(e2)) a2(e2);
      else if (Array.isArray(e2)) for (let n3 = 0, t3 = e2.length; n3 < t3; n3++) a2(e2[n3]);
      function a2(e3) {
        if (t2(e3), Array.isArray(e3[o2])) {
          const n3 = e3[o2];
          for (let e4 = 0, t3 = n3.length; e4 < t3; e4++) a2(n3[e4]);
        }
      }
    }(u, l, function(e2) {
      "string" == typeof e2[r] && -1 !== e2[r].indexOf("clone-node") && (e2[r] = `clone-node-${e2[r]}`);
    }), o && Array.isArray(u[s]) && (u[s] = []), u[l.pid] = c[l.id], c[s] ? c[s].push(u) : c[s] = [u]) : (!function(e2, n2) {
      const { keys: t2, data: o2, onlyOneNode: a2 } = n2, { id: l2, pid: r2, children: d2 } = t2, s2 = P(o2, t2, e2[r2]), c2 = s2[d2];
      let u2;
      for (let n3 = 0, t3 = c2.length; n3 < t3; n3++) if (c2[n3][l2] === e2[l2]) {
        c2.splice(n3, 1), u2 = n3;
        break;
      }
      const i = e2[d2];
      a2 && void 0 !== u2 && i && (e2[d2] = [], i.forEach((e3) => {
        e3[r2] = s2[l2];
      }), s2[d2].splice(u2, 0, ...i));
    }(u, n), u[d] = c[r], c[s] ? c[s].push(u) : c[s] = [].concat(u));
  }
};
var K = { beforeMount(e, n) {
  const { l: t, t: o } = n.modifiers, { drag: a, dragData: l, node: r, emit: d, beforeDragEnd: s, initNodes: c } = n.value, { value: u } = n, i = { ...l };
  e.addEventListener("mousedown", function(e2) {
    const n2 = e2.target;
    if (false === a || 0 !== e2.button || r.focused || r.$$data.noDragging || n2.className.indexOf("tree-org-node-btn") > -1) return false;
    (function(e3) {
      e3.stopPropagation(), v = e3.screenX, g = e3.screenY, f = 0, i.contextmenu.value = false;
      const { keys: n3, onlyOneNode: t2 } = i;
      if (t2) {
        const { children: e4 } = n3, t3 = { ...r.$$data };
        t3[e4] = [], i.cloneData.value = c(t3);
      } else i.cloneData.value = c(r.$$data);
    })(e2), document.addEventListener("mousemove", y), document.addEventListener("mouseup", h2), x("start");
  });
  let p, f = 0, m = false, v = 0, g = 0;
  function y(n2) {
    return n2.preventDefault(), !(Math.abs(n2.screenX - v) < 5 && Math.abs(n2.screenY - g) < 5) && (m || (m = true, function(n3) {
      Y.set(true), i.nodeMoving.value = true, r.moving = true;
      let t2 = e;
      for (; !t2.classList.contains("tree-org-node"); ) f += t2.offsetLeft, t2 = t2.offsetParent;
      f += 2, p = document.querySelector("#clone-tree-org"), p && (p.style.width = `${t2.clientWidth}px`, p.style.opacity = "0.8", p.style.left = n3.clientX - f + "px", p.style.top = n3.clientY + 2 + "px");
    }(n2)), t && o && u ? (p && (p.style.left = n2.clientX - f + "px", p.style.top = n2.clientY + 2 + "px"), void x("move")) : t && u ? (e.style.left = n2.clientX - f + "px", void x("move")) : void (o && u && (e.style.top = n2.clientY + "px", x("move"))));
  }
  function h2(e2) {
    if (document.removeEventListener("mousemove", y), document.removeEventListener("mouseup", h2), m) {
      if ("function" == typeof s) {
        const n2 = s(r, i.parenNode.value);
        n2 && n2.then ? n2.then(() => {
          b(e2);
        }, () => {
        }) : false !== n2 && b(e2);
      } else b(e2);
      m = false, p = null, r.moving = false, i.nodeMoving.value = false, setTimeout(() => {
        Y.set(false);
      }, 200);
    }
  }
  function b(e2) {
    const n2 = document.querySelector(".tree-org-node__moving");
    if (n2 && n2.contains(e2.target)) return x("end"), false;
    X(r, i), x("end");
  }
  function x(e2) {
    "start" !== e2 ? "move" !== e2 ? "end" === e2 && d("on-node-drag-end", r, i.parenNode.value) : d("on-node-drag", r) : d("on-node-drag-start", r);
  }
} };
var W = { onClick: "onNodeClick", onDblclick: "onNodeDblclick", onContextmenu: "onNodeContextmenu", onMouseenter: "onNodeMouseenter", onMouseleave: "onNodeMouseleave" };
function q(e, n) {
  if ("function" == typeof e) return function(t) {
    t.target.className.indexOf("org-tree-node-btn") > -1 || e(t, n);
  };
}
var U = (e, n, t) => !(Array.isArray(e[n]) && e[n].length > 0) && !t || e.isLeaf;
var I = (e, o, a) => {
  const { attrs: l } = a, r = ["tree-org-node"], d = [], { expand: s, children: c, id: u } = o;
  return U(o, "children", l.lazy) ? r.push("is-leaf") : l.collapsable && !s && r.push("collapsed"), o.moving && r.push("tree-org-node__moving"), d.push(J(e, o, a)), l.collapsable && !s || d.push(H(e, c, a)), withDirectives(e("div", { class: r, key: u }, d), [[vShow, !o.hidden]]);
};
var J = (e, a, l) => {
  const { attrs: r } = l, d = r.props, s = r.renderContent, { label: c } = a, u = [];
  if (l.slots.default) u.push(l.slots.default({ node: a }));
  else if ("function" == typeof s) {
    T.warning("scoped-slot header is easier to use. We recommend users to use scoped-slot header.");
    const n = s(e, a);
    n && u.push(n);
  } else u.push(e("div", { class: "tree-org-node__text" }, c));
  r.collapsable && !U(a, "children", r.lazy) && u.push(((e2, n, t) => {
    const { attrs: o } = t, a2 = o.onOnExpand, l2 = ["tree-org-node__expand"];
    n.expand && l2.push("expanded");
    const r2 = [];
    return t.slots.expand ? r2.push(t.slots.expand({ node: n })) : r2.push(e2("span", { class: "tree-org-node__expand-btn" })), e2("span", { class: l2, onMousedown: (e3) => {
      e3.stopPropagation();
    }, onDblclick: (e3) => {
      e3.stopPropagation();
    }, onClick: (e3) => {
      e3.stopPropagation(), a2 && a2(e3, n);
    } }, r2);
  })(e, a, l));
  const i = ["tree-org-node__inner"];
  let { labelStyle: p, labelClassName: f, selectedClassName: m, selectedKey: v } = r;
  "function" == typeof f && (f = f(a)), f && i.push(f), a.className && i.push(a.className), "function" == typeof m && (m = m(a)), void 0 !== v && (v = Array.isArray(v) ? v : [v]), m && v && v.includes(a.id) && i.push(m);
  const g = ["tree-org-node__content"];
  a.$$root && g.push(`is-root_${r.suffix}`), a.label || g.push("is-empty"), a.$$focused && g.push("is-edit");
  const y = resolveDirective("nodedrag"), h2 = [];
  r.vNodedrag && y && !a.$$root && h2.push([y, Object.assign({ node: a }, r.vNodedrag), "", { l: true, t: true }]);
  const b = {};
  for (const e2 in W) if (Object.prototype.hasOwnProperty.call(W, e2)) {
    const n = r[W[e2]];
    n && (b[e2] = q(n, a));
  }
  const x = r.onNodeFocus, $ = r.onNodeBlur, N = resolveDirective("focus"), S = [[vShow, a.$$focused]];
  return N && S.push([N, a.$$focused]), e("div", { class: g }, [withDirectives(e("div", { class: i, style: a.style ? a.style : p, ...b }, u), h2), withDirectives(e("textarea", { class: "tree-org-node__textarea", placeholder: "请输入节点名称", value: a.label, onFocus: (e2) => {
    x && x(e2, a.$$data, a);
  }, onInput: (e2) => {
    a.label = e2.target.value;
  }, onBlur: (e2) => {
    void 0 !== a.$$data.focused && (a.$$data.focused = false), a.$$data[d.label] = e2.target.value, a.$$focused = false, $ && $(e2, a.$$data, a);
  }, onClick: (e2) => e2.stopPropagation() }), S)]);
};
var H = (e, n, t) => {
  if (Array.isArray(n) && n.length) {
    const o = n.filter((e2) => !e2.$$hidden).map((n2) => I(e, n2, t));
    return e("div", { class: "tree-org-node__children" }, o);
  }
  return "";
};
var G = (n, t) => n.data && 0 !== Object.keys(n.data).length ? (n.data.$$root = !n.isClone, I(h, n.data, t)) : "";
G.directives = { focus: { mounted(e, { value: n }) {
  n && e.focus();
}, updated(e, { value: n }) {
  n && e.focus();
} }, nodedrag: K };
var Q = defineComponent({ props: { scale: String, tools: Object }, setup(e, { emit: n }) {
  const t = ref(false), o = ref(false);
  function a(e2) {
    o.value = !o.value, n("onFullscreen", e2);
  }
  function c() {
    document.isFullScreen || document.mozIsFullScreen || document.webkitIsFullScreen || !o.value || a("esc");
  }
  const u = computed(() => t.value ? "全部收起" : "全部展开"), i = computed(() => o.value ? "退出全屏" : "全屏");
  return onMounted(() => {
    window.addEventListener("resize", c);
  }), onBeforeUnmount(() => {
    window.removeEventListener("resize", c);
  }), { expanded: t, fullscreen: o, expandTitle: u, fullTiltle: i, handleExpand: function() {
    t.value = !t.value, n("onExpand");
  }, handleScale: function(e2) {
    "out" === e2 ? n("onScale", 0.1) : "in" === e2 ? n("onScale", -0.1) : n("onRestore");
  }, handleFullscreen: a };
} });
var Z = { class: "zm-tree-handle" };
var ee = { key: 0, class: "zm-tree-percent" };
var ne = ["title"];
var te = { class: "zm-tree-svg" };
var oe = [createBaseVNode("span", { class: "zm-tree-icon" }, "+", -1)];
var ae = [createBaseVNode("span", { class: "zm-tree-icon" }, "-", -1)];
var le = [createBaseVNode("span", { class: "zm-tree-restore" }, null, -1)];
var re = ["title"];
var de = { class: "zm-tree-svg" };
function se(e) {
  return "function" == typeof e || "[object Function]" === Object.prototype.toString.call(e);
}
function ce(e, n, t, o = 1) {
  return { deltaX: Math.round(n / o / e[0]) * e[0], deltaY: Math.round(t / o / e[1]) * e[1] };
}
function ue(e, n, t) {
  return e < n ? n : t < e ? t : e;
}
Q.render = function(e, n, t, o, a, l) {
  return openBlock(), createElementBlock("div", Z, [e.tools.scale ? (openBlock(), createElementBlock("div", ee, toDisplayString(e.scale), 1)) : createCommentVNode("v-if", true), e.tools.expand ? (openBlock(), createElementBlock("div", { key: 1, onClick: n[0] || (n[0] = (...n2) => e.handleExpand && e.handleExpand(...n2)), title: e.expandTitle, class: "zm-tree-handle-item" }, [createBaseVNode("span", te, [createBaseVNode("i", { class: normalizeClass(["treefont", e.expanded ? "icon-collapse" : "icon-expand"]) }, null, 2)])], 8, ne)) : createCommentVNode("v-if", true), e.tools.zoom ? (openBlock(), createElementBlock("div", { key: 2, onClick: n[1] || (n[1] = (n2) => e.handleScale("out")), title: "放大", class: "zm-tree-handle-item zoom-out" }, oe)) : createCommentVNode("v-if", true), e.tools.zoom ? (openBlock(), createElementBlock("div", { key: 3, onClick: n[2] || (n[2] = (n2) => e.handleScale("in")), title: "缩小", class: "zm-tree-handle-item zoom-in" }, ae)) : createCommentVNode("v-if", true), e.tools.restore ? (openBlock(), createElementBlock("div", { key: 4, onClick: n[3] || (n[3] = (n2) => e.handleScale("restore")), title: "还原", class: "zm-tree-handle-item" }, le)) : createCommentVNode("v-if", true), e.tools.fullscreen ? (openBlock(), createElementBlock("div", { key: 5, onClick: n[4] || (n[4] = (...n2) => e.handleFullscreen && e.handleFullscreen(...n2)), title: e.fullTiltle, class: "zm-tree-handle-item" }, [createBaseVNode("span", de, [createBaseVNode("i", { class: normalizeClass(["treefont", e.fullscreen ? "icon-unfullscreen" : "icon-fullscreen"]) }, null, 2)])], 8, re)) : createCommentVNode("v-if", true)]);
}, Q.__file = "src/components/tools/tools.vue";
var ie = function(e, n, t, o = false) {
  e && n && t && e.addEventListener(n, t, o);
};
var pe = function(e, n, t, o = false) {
  e && n && t && e.removeEventListener(n, t, o);
};
var fe = (e, { emit: n }, t) => {
  const o = { start: "mousedown", move: "mousemove", stop: "mouseup" }, a = { start: "touchstart", move: "touchmove", stop: "touchend" }, s = { userSelect: "none", MozUserSelect: "none", WebkitUserSelect: "none", MsUserSelect: "none" }, c = { userSelect: "auto", MozUserSelect: "auto", WebkitUserSelect: "auto", MsUserSelect: "auto" };
  let u = o;
  const i = ref(e.x), p = ref(e.y), f = ref(e.z), m = ref(e.active), h2 = ref(false), b = ref(0), x = ref(0), $ = ref(0), N = ref(0), S = ref(0), z = ref(0);
  let E = {}, D = {};
  function C() {
    E = { mouseX: 0, mouseY: 0, x: 0, y: 0, w: 0, h: 0 }, D = { minLeft: -1 / 0, maxLeft: 1 / 0, minRight: -1 / 0, maxRight: 1 / 0, minTop: -1 / 0, maxTop: 1 / 0, minBottom: -1 / 0, maxBottom: 1 / 0 };
  }
  function k() {
    if (e.parent) {
      const [e2, n2] = M();
      S.value = e2 || 0, z.value = n2 || 0;
    }
  }
  function M() {
    if (e.parent && t.eleRef.value) {
      const e2 = window.getComputedStyle(t.eleRef.value.parentNode, null);
      return [parseInt(e2.getPropertyValue("width"), 10), parseInt(e2.getPropertyValue("height"), 10)];
    }
    return [0, 0];
  }
  function O(o2) {
    if (o2 instanceof MouseEvent && 1 !== o2.which) return;
    const a2 = o2.target || o2.srcElement;
    if (a2 && t.eleRef.value && t.eleRef.value.contains(a2)) {
      if (e.dragCancel && function(e2, n2, t2) {
        let o3 = e2;
        const a3 = ["matches", "webkitMatchesSelector", "mozMatchesSelector", "msMatchesSelector", "oMatchesSelector"].find((e3) => o3 && se(o3[e3])) || "";
        if (!se(o3[a3])) return false;
        do {
          if (o3[a3](n2)) return true;
          if (o3 === t2) return false;
          o3 = o3.parentNode;
        } while (o3);
        return false;
      }(a2, e.dragCancel, t.eleRef.value)) return void (h2.value = false);
      m.value || (m.value = true, n("activated"), n("update:active", true)), e.draggable && (h2.value = true), o2 instanceof MouseEvent ? (E.mouseX = o2.pageX, E.mouseY = o2.pageY) : (E.mouseX = o2.touches[0].pageX, E.mouseY = o2.touches[0].pageY), E.left = i.value, E.right = b.value, E.top = p.value, E.bottom = x.value, e.parent && (D = w()), ie(document.documentElement, u.move, R2), ie(document.documentElement, u.stop, F2);
    }
  }
  function w() {
    const n2 = e.grid;
    return { minLeft: i.value % n2[0], maxLeft: Math.floor((S.value - $.value - i.value) / n2[0]) * n2[0] + i.value, minRight: b.value % n2[0], maxRight: Math.floor((S.value - $.value - b.value) / n2[0]) * n2[0] + b.value, minTop: p.value % n2[1], maxTop: Math.floor((z.value - N.value - p.value) / n2[1]) * n2[1] + p.value, minBottom: x.value % n2[1], maxBottom: Math.floor((z.value - N.value - x.value) / n2[1]) * n2[1] + x.value };
  }
  function _(o2) {
    const a2 = o2.target || o2.srcElement;
    t.eleRef.value && !t.eleRef.value.contains(a2) && m.value && !e.preventDeactivation && (m.value = false, n("deactivated"), n("update:active", false));
  }
  function R2(t2) {
    h2.value && function(t3) {
      const o2 = e.axis, a2 = e.grid;
      let l = 0, r = 0;
      t3 instanceof MouseEvent ? (l = t3.pageX, r = t3.pageY) : (l = t3.touches[0].pageX, r = t3.touches[0].pageY);
      const d = o2 && "y" !== o2 ? E.mouseX - l : 0, s2 = o2 && "x" !== o2 ? E.mouseY - r : 0, { deltaX: c2, deltaY: u2 } = ce(a2, d, s2, e.scale), f2 = ue(E.left - c2, D.minLeft, D.maxLeft), m2 = ue(E.top - u2, D.minTop, D.maxTop), v = ue(E.right + c2, D.minRight, D.maxRight), g = ue(E.bottom + u2, D.minBottom, D.maxBottom);
      i.value = f2, p.value = m2, b.value = v, x.value = g, n("dragging", i.value, p.value);
    }(t2);
  }
  function F2() {
    C(), h2.value && (h2.value = false, n("dragstop", i.value, p.value));
  }
  onBeforeMount(() => {
    C();
  }), onMounted(() => {
    !e.enableNativeDrag && t.eleRef.value && (t.eleRef.value.ondragstart = () => false);
    const [n2, o2] = M();
    if (S.value = n2, z.value = o2, t.eleRef.value) {
      const [e2, n3] = function(e3) {
        const n4 = window.getComputedStyle(e3);
        return [parseFloat(n4.getPropertyValue("width")), parseFloat(n4.getPropertyValue("height"))];
      }(t.eleRef.value);
      $.value = e2, N.value = n3, b.value = S.value - $.value - i.value, x.value = z.value - N.value - p.value;
    }
    ie(document.documentElement, "mousedown", _), ie(document.documentElement, "touchend touchcancel", _), ie(window, "resize", k);
  }), onUnmounted(() => {
    pe(document.documentElement, "mousedown", _), pe(document.documentElement, "touchstart", F2), pe(document.documentElement, "mousemove", R2), pe(document.documentElement, "touchmove", R2), pe(document.documentElement, "mouseup", F2), pe(document.documentElement, "touchend touchcancel", _), pe(window, "resize", k);
  });
  const A2 = computed(() => ({ transform: `translate(${i.value}px, ${p.value}px)`, zIndex: f.value, ...h2.value && e.disableUserSelect ? s : c }));
  return watch(() => e.active, (e2) => {
    m.value = e2, n(e2 ? "activated" : "deactivated");
  }), watch(() => e.z, (e2) => {
    (e2 >= 0 || "auto" === e2) && (f.value = e2);
  }), watch(() => e.x, (n2) => {
    h2.value || (e.parent && (D = w()), function(n3) {
      const { deltaX: t2 } = ce(e.grid, n3, p.value, e.scale), o2 = ue(t2, D.minLeft, D.maxLeft);
      i.value = o2, b.value = S.value - $.value - o2;
    }(n2));
  }), watch(() => e.y, (n2) => {
    h2.value || (e.parent && (D = w()), function(n3) {
      const { deltaY: t2 } = ce(e.grid, i.value, n3, e.scale), o2 = ue(t2, D.minTop, D.maxTop);
      p.value = o2, x.value = z.value - N.value - o2;
    }(n2));
  }), { enabled: m, dragging: h2, style: A2, resetBoundsAndMouseState: C, elementTouchDown: function(e2) {
    u = a, O(e2);
  }, elementMouseDown: function(e2) {
    u = o, O(e2);
  } };
};
Object.freeze({}), Object.freeze([]);
var me = Symbol("wrapper");
function ve(e, n) {
  if (null === (t = e) || "object" != typeof t || e.__elPropsReservedKey) return e;
  var t;
  const { values: o, required: a, default: l, type: r, validator: d } = e, s = o || d ? (e2) => {
    let t2 = false, a2 = [];
    if (o && (a2 = [...o, l], t2 = t2 || a2.includes(e2)), d && (t2 = t2 || d(e2)), !t2 && a2.length > 0) {
      const t3 = [...new Set(a2)].map((e3) => JSON.stringify(e3)).join(", ");
      warn(`Invalid prop: validation failed${n ? ` for prop "${n}"` : ""}. Expected one of [${t3}], got value ${JSON.stringify(e2)}.`);
    }
    return t2;
  } : void 0;
  return { type: "object" == typeof r && Object.getOwnPropertySymbols(r).includes(me) ? r[me] : r, validator: s, __elPropsReservedKey: true, default: l, required: !!a };
}
var ge = (e) => function(e2) {
  let n = -1;
  const t = e2 ? e2.length : 0, o = {};
  for (; ++n < t; ) {
    const t2 = e2[n];
    o[t2[0]] = t2[1];
  }
  return o;
}(Object.entries(e).map(([e2, n]) => [e2, ve(n, e2)]));
var ye = (e) => ({ [me]: e });
var he = defineComponent({ replace: true, name: "Draggable", props: ge({ className: { type: String, default: "zm-draggable" }, classNameDraggable: { type: String, default: "draggable" }, classNameDragging: { type: String, default: "dragging" }, classNameActive: { type: String, default: "active" }, disableUserSelect: { type: Boolean, default: true }, enableNativeDrag: { type: Boolean, default: false }, preventDeactivation: { type: Boolean, default: false }, active: { type: Boolean, default: false }, draggable: { type: Boolean, default: true }, x: { type: Number, default: 0 }, y: { type: Number, default: 0 }, z: { type: [String, Number], default: "auto", validator: (e) => "string" == typeof e ? "auto" === e : e >= 0 }, dragCancel: String, axis: { type: String, default: "both", validator: (e) => ["x", "y", "both"].includes(e) }, grid: { type: Array, default: () => [1, 1] }, parent: { type: Boolean, default: false }, scale: { type: Number, default: 1, validator: (e) => e > 0 } }), emits: { activated: () => true, deactivated: () => true, dragging: (e, n) => L(e) && L(n), dragstop: (e, n) => L(e) && L(n), "update:active": (e) => "boolean" == typeof e }, setup(e, n) {
  const t = ref(), o = fe(e, n, { eleRef: t });
  return { top, eleRef: t, ...o };
} });
he.render = function(e, n, t, o, a, l) {
  return openBlock(), createElementBlock("div", { style: normalizeStyle(e.style), class: normalizeClass([{ [e.classNameActive]: e.enabled, [e.classNameDragging]: e.dragging, [e.classNameDraggable]: e.draggable }, e.className]), ref: "eleRef", onMousedown: n[0] || (n[0] = (...n2) => e.elementMouseDown && e.elementMouseDown(...n2)), onTouchstart: n[1] || (n[1] = (...n2) => e.elementTouchDown && e.elementTouchDown(...n2)) }, [renderSlot(e.$slots, "default")], 38);
}, he.__file = "src/components/draggable/src/draggable.vue";
var be = defineComponent({ components: { TreeOrgNode: G }, props: ge({ data: { type: Object, required: true }, props: { type: ye(Object) }, modelValue: Boolean, horizontal: Boolean, selectedKey: String, collapsable: Boolean, renderContent: Function, labelStyle: Object, labelClassName: { type: [Function, String] } }), setup: (e, n) => ({ defaultSlot: !!n.slots.default, expandSlot: !!n.slots.expand }) });
be.render = function(e, o, a, l, r, d) {
  const s = resolveComponent("tree-org-node");
  return openBlock(), createBlock(Teleport, { to: "body" }, [withDirectives(createBaseVNode("div", { id: "clone-tree-org", class: normalizeClass(["clone-tree-org tree-org", { horizontal: e.horizontal, collapsable: e.collapsable }]) }, [createVNode(s, { data: e.data, props: e.props, isClone: false, horizontal: e.horizontal, labelStyle: e.labelStyle, collapsable: e.collapsable, renderContent: e.renderContent, labelClassName: e.labelClassName }, createSlots({ _: 2 }, [e.defaultSlot ? { name: "default", fn: withCtx(({ node: n }) => [renderSlot(e.$slots, "default", { node: n })]) } : void 0, e.expandSlot ? { name: "expand", fn: withCtx(({ node: n }) => [renderSlot(e.$slots, "expand", { node: n })]) } : void 0]), 1032, ["data", "props", "horizontal", "labelStyle", "collapsable", "renderContent", "labelClassName"])], 2), [[vShow, e.modelValue]])]);
}, be.__file = "src/components/clone-org/clone-org.vue";
var xe = (e, { emit: n }, t) => {
  const o = ref("");
  function a(e2, n2, t2) {
    if (e2[n2.id] === t2) return e2;
    if (Array.isArray(e2[n2.children])) {
      const o2 = e2[n2.children];
      for (let e3 = 0, l = o2.length; e3 < l; e3++) {
        const l2 = a(o2[e3], n2, t2);
        if (l2) return l2;
      }
    }
  }
  function s() {
    const { props: t2, data: o2, node: l } = e;
    if (e.nodeDelete) return void e.nodeDelete(l.$$data);
    if (l.$$root) return void T.pretty("[提示] ", "根节点不允许删除", "danger");
    const { id: r, children: d } = t2, s2 = a(o2, t2, l.pid);
    if (s2) {
      const e2 = s2[d];
      for (let t3 = 0, o3 = e2.length; t3 < o3; t3++) if (e2[t3][r] === l.id) {
        e2.splice(t3, 1), n("onNodeDelete", l.$$data, l);
        break;
      }
    }
  }
  function c() {
    const { nodeEdit: t2, node: o2 } = e;
    t2 ? t2(o2.$$data) : (o2.$$focused = true, n("onNodeFocus", o2.$$data));
  }
  function u(o2) {
    if (e.modelValue) {
      if (t.eleRef.value && t.eleRef.value.contains(o2.target)) return false;
      n("update:modelValue", false);
    }
  }
  const i = computed(() => ({ left: `${e.x}px`, top: `${e.y}px` })), p = computed(() => !e.disabled && !e.node.disabled);
  return onMounted(() => {
    document.addEventListener("mousedown", u);
  }), onUnmounted(() => {
    document.removeEventListener("mousedown", u);
  }), { position: i, editable: p, handleMenu: function(a2) {
    const l = a2.target;
    if ("zm-tree-menu-item" === l.className) {
      const a3 = l.getAttribute("action");
      switch (a3) {
        case "copy":
          !function() {
            if (e.nodeCopy) return void e.nodeCopy(e.node.$$data);
            o.value = e.node.label, nextTick(() => {
              t.inputRef && t.inputRef.value && (t.inputRef && t.inputRef.value.select(), navigator.clipboard.writeText(o.value).then(function() {
                T.pretty("[提示] ", "文本复制成功", "success");
              }, function() {
                T.pretty("[错误] ", "浏览器不支持", "danger");
              }), n("onNodeCopy", o.value));
            });
          }();
          break;
        case "add":
          !function() {
            if (e.nodeAdd) return void e.nodeAdd(e.node.$$data);
            const { id: t2, pid: o2, label: a4, expand: l2, children: r } = e.props, { node: d } = e, s2 = { [t2]: String((/* @__PURE__ */ new Date()).getTime()), [o2]: d.id, [a4]: "", [l2]: false, [r]: [], newNode: true, focused: true };
            Array.isArray(d.children) ? d.$$data[r].push(s2) : d.$$data[r] = [s2];
            n("onNodeFocus", s2);
          }();
          break;
        case "edit":
          c();
          break;
        case "delete":
          s();
      }
      n("contextmenu", { command: a3, node: e.node, data: e.node.$$data }), n("update:modelValue", false);
    }
  }, handleEdit: c, handleClose: u, afterEnter: function() {
    n("opened");
  }, afterLeave: function() {
    n("closed");
  } };
};
var $e = defineComponent({ name: "Contextmenu", props: ge({ modelValue: { type: Boolean, required: true }, data: { type: Object, required: true }, node: { type: ye(Object), required: true }, props: { type: ye(Object) }, x: Number, y: Number, menus: { type: ye(Array) }, nodeAdd: Function, nodeDelete: Function, nodeEdit: Function, nodeCopy: Function, disabled: Boolean }), emits: { onNodeDelete: (e, n) => A(e) && A(n), onNodeCopy: (e) => B(e), contextmenu: (e) => A(e), onNodeFocus: (e) => A(e), opened: () => true, closed: () => true, "update:modelValue": (e) => "boolean" == typeof e }, setup(e, n) {
  const t = ref(), o = ref();
  return { eleRef: o, inputRef: t, copyText: ref(""), oldData: reactive({}), ...xe(e, n, { inputRef: t, eleRef: o }) };
} });
var Ne = ["action"];
$e.render = function(e, o, a, l, r, d) {
  return openBlock(), createBlock(Teleport, { to: "body" }, [createVNode(Transition, { name: "dialog-fade", onAfterEnter: e.afterEnter, onAfterLeave: e.afterLeave, persisted: "" }, { default: withCtx(() => [withDirectives(createBaseVNode("div", { style: normalizeStyle(e.position), onClick: o[1] || (o[1] = (...n) => e.handleMenu && e.handleMenu(...n)), ref: "eleRef", class: "zm-tree-contextmenu" }, [createBaseVNode("ul", null, [(openBlock(true), createElementBlock(Fragment, null, renderList(e.menus, (n) => (openBlock(), createElementBlock(Fragment, null, [e.editable || !["add", "edit", "delete"].includes(n.command) ? (openBlock(), createElementBlock("li", { class: "zm-tree-menu-item", action: n.command, key: n.command }, toDisplayString(n.name), 9, Ne)) : createCommentVNode("v-if", true)], 64))), 256))]), withDirectives(createBaseVNode("textarea", { class: "copy-textarea", ref: "inputRef", "onUpdate:modelValue": o[0] || (o[0] = (n) => e.copyText = n) }, null, 512), [[vModelText, e.copyText]])], 4), [[vShow, e.modelValue]])]), _: 1 }, 8, ["onAfterEnter", "onAfterLeave"])]);
}, $e.__file = "src/components/contextmenu/src/contextmenu.vue";
var Se = [{ name: "复制文本", command: "copy" }, { name: "新增节点", command: "add" }, { name: "编辑节点", command: "edit" }, { name: "删除节点", command: "delete" }];
var ze = ge({ data: { type: Object, required: true }, center: Boolean, props: { type: ye(Object), default: () => ({ id: "id", pid: "pid", label: "label", expand: "expand", children: "children" }) }, toolBar: { type: [Object, Boolean], default: () => ({ expand: true, scale: true, zoom: true, restore: true, fullscreen: true }) }, disabled: { type: Boolean, default: false }, scalable: { type: Boolean, default: true }, draggable: { type: Boolean, default: true }, draggableOnNode: { type: Boolean, default: false }, nodeDraggable: { type: Boolean, default: true }, cloneNodeDrag: { type: Boolean, default: true }, onlyOneNode: { type: Boolean, default: true }, clickDelay: { type: Number, default: 260 }, lazy: Boolean, load: { type: ye(Function) }, defaultExpandLevel: Number, defaultExpandKeys: { type: Array, default: () => [] }, beforeDragEnd: Function, horizontal: Boolean, selectedKey: { type: [Array, String, Number] }, collapsable: Boolean, renderContent: Function, labelStyle: Object, labelClassName: { type: [Function, String] }, selectedClassName: { type: [Function, String] }, defineMenus: { type: ye([Array, Function]), default: () => Se }, nodeAdd: Function, nodeDelete: Function, nodeEdit: Function, nodeCopy: Function, filterNodeMethod: Function });
var Ee = (e, { emit: n }, t) => {
  const o = ref(0), a = ref(0), d = ref(false);
  function s(n2, l) {
    const r = t.zoomRef.value, d2 = t.treeRef.value;
    let s2 = r.clientWidth / 2;
    const c2 = r.clientHeight / 2;
    let u2 = r.clientHeight - d2.clientHeight, i2 = r.clientWidth - d2.clientWidth;
    if (u2 > 0 && (u2 = 0), i2 > 0 && (i2 = 0), e.center) {
      const e2 = (r.clientWidth - d2.clientWidth) / 2;
      i2 -= e2, s2 -= e2;
    }
    o.value = n2 > s2 ? s2 : n2 < i2 ? i2 : n2, a.value = l < u2 ? u2 : l > c2 ? c2 : l;
  }
  function c(e2, t2) {
    s(e2, t2), setTimeout(() => {
      Y.set(false);
    }, 200), n("on-drag-stop", { x: e2, y: t2 });
  }
  const u = ref(false), i = reactive({ value: {} });
  const p = ref(false), f = ref(0), m = ref(0), g = ref({}), h2 = ref([]);
  const b = ref(1);
  function x(n2) {
    if (!e.scalable) return;
    const t2 = Number((Number(b.value) + n2).toFixed(1));
    b.value = n2 > 0 ? Math.min(3, t2) : Math.max(0.3, t2);
  }
  function $(n2, t2, l) {
    if (d.value = true, console.log(e.center, e.horizontal, n2.offsetLeft, t2), !e.center || e.horizontal) {
      const e2 = n2.offsetLeft - t2;
      o.value -= e2;
    }
    const r = n2.offsetTop - l;
    a.value -= r, s(o.value, a.value);
  }
  let N;
  const S = reactive(Object.assign({ id: "id", pid: "pid", label: "label", expand: "expand", children: "children", isLeaf: "isLeaf" }, e.props));
  const z = ref(false);
  function E(e2) {
    e2.forEach((e3) => {
      e3.expand && (e3.expand = false, T2.delete(e3.id)), e3.children && E(e3.children);
    });
  }
  const D = ref(false);
  function M(e2, n2) {
    Array.isArray(e2) ? e2.forEach((e3) => {
      n2 && T2.add(e3.id), e3.expand = n2, n2 && T2.add(e3.id), e3.children && M(e3.children, n2);
    }) : (n2 && T2.add(e2.id), e2.expand = n2, n2 && T2.add(e2.id), e2.children && M(e2.children, n2));
  }
  function O(e2) {
    V2.value = j2(e2);
  }
  const w = computed(() => ({ width: 100 / b.value + "%", height: 100 / b.value + "%", transform: `scale(${b.value})` })), _ = computed(() => `${Math.round(100 * b.value)}%`), R2 = computed(() => e.draggableOnNode && !e.nodeDraggable ? "" : `.tree-org-node__content:not(.is-root_${X2})>.tree-org-node__inner`), F2 = computed(() => D.value ? "收起全部节点" : "展开全部节点"), A2 = computed(() => D.value ? "收起全部节点" : "展开全部节点"), B2 = ref({}), L2 = computed(() => {
    const { cloneNodeDrag: t2, onlyOneNode: o2, data: a2 } = e;
    return { drag: e.nodeDraggable, dragData: { keys: S, nodeMoving: u, stopClick: Y, parenNode: i, cloneNodeDrag: t2, onlyOneNode: o2, contextmenu: p, cloneData: B2, data: a2 }, beforeDragEnd: e.beforeDragEnd, initNodes: j2, emit: n };
  });
  watch(() => e.horizontal, () => {
    nextTick(() => {
      c(o.value, a.value);
    });
  });
  let T2 = new Set(e.defaultExpandKeys);
  function j2(n2) {
    const { defaultExpandLevel: t2 = 0 } = e, o2 = (e2, n3) => {
      const { id: a2, label: l, pid: r, expand: d2, children: s2, isLeaf: c2 } = S, u2 = {};
      Object.keys(e2).map((n4) => {
        ["hidden", "disabled", "className", "style"].includes(n4) && (u2[n4] = e2[n4]);
      });
      const i2 = e2[s2], p2 = n3 + 1, f2 = e2[d2], m2 = e2[a2];
      return (f2 || void 0 === f2 && n3 < t2) && T2.add(m2), { ...u2, id: m2, label: e2[l], pid: e2[r], expand: T2.has(m2), children: i2 ? i2.map((e3) => o2(e3, p2)) : void 0, isLeaf: e2[c2], $$level: n3, $$data: e2, $$focused: e2.focused || false };
    };
    return o2(n2, 0);
  }
  const V2 = ref(j2(e.data));
  watch(() => e.data, (n2, t2) => {
    n2 !== t2 && (T2 = new Set(e.defaultExpandKeys)), O(e.data);
  }, { deep: true });
  const P2 = reactive({ visible: true, data: { expand: true, scale: true, zoom: true, restore: true, fullscreen: true } });
  onBeforeMount(() => {
    "object" == typeof e.toolBar ? Object.assign(P2.data, e.toolBar) : e.toolBar || (P2.visible = false);
  });
  const X2 = function(e2) {
    e2 = e2 || 32;
    const n2 = "ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678", t2 = n2.length;
    let o2 = "";
    for (let a2 = 0; a2 < e2; a2++) o2 += n2.charAt(Math.floor(Math.random() * t2));
    return o2;
  }(6);
  return { keys: S, left: o, top: a, menuX: f, menuY: m, suffix: X2, nodeMoving: u, zoomStyle: w, tools: P2, zoomPercent: _, dragCancel: R2, expandTitle: F2, fullTiltle: A2, nodeargs: L2, expanded: D, fullscreen: z, treeData: V2, autoDragging: d, contextmenu: p, nodeMenus: h2, menuData: g, cloneData: B2, filter: function(n2) {
    const t2 = e.filterNodeMethod;
    if (!t2) throw new Error("[Tree] filterNodeMethod is required when filter");
    const o2 = function(e2) {
      const a2 = e2.children || [];
      if (a2.forEach((e3) => {
        e3.$$hidden = !t2.call(e3, n2, e3), o2(e3);
      }), e2.$$hidden && a2.length) {
        let n3 = true;
        n3 = a2.some((e3) => !e3.$$hidden), e2.$$hidden = !n3;
      }
      n2 && !e2.$$hidden && e2.children && (e2.expand = true);
    };
    o2(V2.value);
  }, setData: O, zoomWheel: function(t2) {
    e.scalable && (t2.preventDefault(), t2.deltaY < 0 ? x(0.1) : x(-0.1), n("on-zoom", b.value));
  }, onDrag: function(e2, t2) {
    Y.set(true), d.value = false, o.value = e2, a.value = t2, n("on-drag", { x: e2, y: t2 });
  }, onDragStop: c, expandChange: function() {
    D.value = !D.value, D.value || (T2.clear(), nextTick(() => {
      c(o.value, a.value);
    })), M(V2.value, D.value), n("on-expand-all", D.value);
  }, handleFullscreen: function(e2) {
    z.value = !z.value, "esc" !== e2 && (z.value ? function() {
      const e3 = t.eleRef.value;
      e3.requestFullscreen && e3.requestFullscreen();
    }() : document.exitFullscreen && document.exitFullscreen());
  }, zoomOrgchart: x, restoreOrgchart: function() {
    b.value = 1, o.value = 0, a.value = 0, n("on-restore");
  }, handleExpand: function(t2, o2) {
    t2.stopPropagation();
    const a2 = document.querySelector(`.is-root_${X2}`);
    if (a2) {
      const l = a2.offsetLeft, r = a2.offsetTop;
      o2.expand = !o2.expand;
      let d2 = true;
      o2.expand ? (T2.add(o2.id), e.lazy && e.load && (d2 = false, function(e2, n2, t3) {
        n2(e2, (n3, o3) => {
          const { children: a3 } = S;
          e2.isLeaf = !n3.length, n3.length && (e2.$$data[a3] = n3, o3 && t3());
        });
      }(o2, e.load, () => {
        nextTick(() => {
          $(a2, l, r);
        });
      }))) : !o2.expand && o2.children && (T2.delete(o2.id), E(o2.children)), nextTick(() => {
        d2 && $(a2, l, r);
      }), n("on-expand", t2, o2.$$data, o2);
    }
  }, getExpandKeys: function() {
    return [...T2];
  }, setExpandKeys: function(n2) {
    T2 = new Set(n2), O(e.data);
  }, nodeMouseenter: function(e2, t2) {
    return u.value && (i.value = t2), n("on-node-mouseenter", e2, t2.$$data, t2), true;
  }, nodeMouseleave: function(e2, t2) {
    return u.value && (i.value = null), n("on-node-mouseleave", e2, t2.$$data, t2), true;
  }, nodeContextmenu: function(n2, t2) {
    n2.stopPropagation(), n2.preventDefault();
    const { defineMenus: o2 } = e;
    Array.isArray(o2) ? h2.value = o2 : "function" == typeof o2 && (h2.value = o2(n2, t2) || []), p.value = true, f.value = n2.clientX, m.value = n2.clientY, g.value = t2;
  }, handleFocus: function(e2, t2, o2) {
    n("on-node-focus", e2, t2, o2);
  }, handleBlur: function(e2, t2, o2) {
    const { id: a2, label: l } = S, r = g.value.children || [];
    for (let e3 = r.length; e3 > 0; e3--) {
      const n2 = r[e3 - 1];
      if ("" === n2[a2] && "" === n2[l]) {
        r.splice(e3 - 1, 1);
        break;
      }
    }
    n("on-node-blur", e2, t2, o2);
  }, handleClick: function(t2, o2) {
    Y.get() || (clearTimeout(N), N = setTimeout(() => {
      n("on-node-click", t2, o2.$$data, o2);
    }, e.clickDelay));
  }, handleDblclick: function(e2, t2) {
    clearTimeout(N), n("on-node-dblclick", e2, t2.$$data, t2);
  } };
};
var De = defineComponent({ name: "vue3TreeOrg", components: { Tools: Q, CloneOrg: be, Draggable: he, Contextmenu: $e, TreeOrgNode: G }, directives: { nodedrag: K }, props: ze, emits: { "on-drag": ({ x: e, y: n }) => L(e) && L(n), "on-drag-stop": ({ x: e, y: n }) => L(e) && L(n), "on-restore": () => true, "on-zoom": (e) => L(e), "on-expand": (e, n, t) => e instanceof MouseEvent && A(t) && A(n), "on-expand-all": (e) => "boolean" == typeof e, "on-node-blur": (e, n, t) => e instanceof FocusEvent && A(t) && A(n), "on-node-click": (e, n, t) => e instanceof MouseEvent && A(t) && A(n), "on-node-dblclick": (e, n, t) => e instanceof MouseEvent && A(t) && A(n), "on-node-mouseenter": (e, n, t) => e instanceof MouseEvent && A(t) && A(n), "on-node-mouseleave": (e, n, t) => e instanceof MouseEvent && A(t) && A(n), "on-contextmenu": (e) => A(e), "on-node-copy": (e) => B(e), "on-node-delete": (e) => A(e), "on-node-drag-start": (e) => A(e), "on-node-drag": (e) => A(e), "on-node-drag-end": (e, n) => A(e) && A(n), "on-node-focus": (e, n, t) => e instanceof FocusEvent && A(t) && A(n) }, setup(e, n) {
  const t = !!n.slots.default, o = !!n.slots.expand, a = ref(), r = ref(), d = ref();
  return { eleRef: a, treeRef: r, zoomRef: d, defaultSlot: t, expandSlot: o, ...Ee(e, n, { eleRef: a, treeRef: r, zoomRef: d }) };
} });
var Ce = { ref: "eleRef", class: "zm-tree-org" };
var ke = { class: "tree-org-node__text" };
var Me = createBaseVNode("span", { class: "tree-org-node__expand-btn" }, null, -1);
var Oe = { class: "tree-org-node__text" };
var we = createBaseVNode("span", { class: "tree-org-node__expand-btn" }, null, -1);
De.render = function(e, n, t, o, a, l) {
  const r = resolveComponent("tree-org-node"), d = resolveComponent("Draggable"), s = resolveComponent("Tools"), v = resolveComponent("clone-org"), g = resolveComponent("Contextmenu");
  return openBlock(), createElementBlock("div", Ce, [createBaseVNode("div", { ref: "zoomRef", class: normalizeClass(["zoom-container", { "is-center": e.center && !e.horizontal }]), style: normalizeStyle(e.zoomStyle), onWheel: n[0] || (n[0] = (...n2) => e.zoomWheel && e.zoomWheel(...n2)) }, [createVNode(d, { x: e.left, y: e.top, class: normalizeClass({ dragging: e.autoDragging }), onDragging: e.onDrag, onDragstop: e.onDragStop, draggable: e.draggable, "drag-cancel": e.dragCancel }, { default: withCtx(() => [createBaseVNode("div", { ref: "treeRef", class: normalizeClass(["tree-org", { horizontal: e.horizontal, collapsable: e.collapsable }]) }, [createVNode(r, { data: e.treeData, props: e.keys, lazy: e.lazy, suffix: e.suffix, horizontal: e.horizontal, labelStyle: e.labelStyle, collapsable: e.collapsable, renderContent: e.renderContent, selectedKey: e.selectedKey, defaultExpandLevel: e.defaultExpandLevel, selectedClassName: e.selectedClassName, labelClassName: e.labelClassName, vNodedrag: e.nodeargs, onOnExpand: e.handleExpand, onNodeClick: e.handleClick, onNodeDblclick: e.handleDblclick, onNodeMouseenter: e.nodeMouseenter, onNodeMouseleave: e.nodeMouseleave, onNodeContextmenu: e.nodeContextmenu, onNodeFocus: e.handleFocus, onNodeBlur: e.handleBlur }, createSlots({ _: 2 }, [e.defaultSlot ? { name: "default", fn: withCtx(({ node: n2 }) => [renderSlot(e.$slots, "default", { node: n2 }, () => [createBaseVNode("div", ke, [createBaseVNode("span", null, toDisplayString(n2.label), 1)])])]) } : void 0, e.expandSlot ? { name: "expand", fn: withCtx(({ node: n2 }) => [renderSlot(e.$slots, "expand", { node: n2 }, () => [Me])]) } : void 0]), 1032, ["data", "props", "lazy", "suffix", "horizontal", "labelStyle", "collapsable", "renderContent", "selectedKey", "defaultExpandLevel", "selectedClassName", "labelClassName", "vNodedrag", "onOnExpand", "onNodeClick", "onNodeDblclick", "onNodeMouseenter", "onNodeMouseleave", "onNodeContextmenu", "onNodeFocus", "onNodeBlur"])], 2)]), _: 3 }, 8, ["x", "y", "class", "onDragging", "onDragstop", "draggable", "drag-cancel"])], 38), e.tools.visible ? (openBlock(), createBlock(s, { key: 0, tools: e.tools.data, scale: e.zoomPercent, onOnExpand: e.expandChange, onOnScale: e.zoomOrgchart, onOnRestore: e.restoreOrgchart, onOnFullscreen: e.handleFullscreen }, null, 8, ["tools", "scale", "onOnExpand", "onOnScale", "onOnRestore", "onOnFullscreen"])) : createCommentVNode("v-if", true), e.nodeDraggable ? (openBlock(), createBlock(v, { key: 1, modelValue: e.nodeMoving, "onUpdate:modelValue": n[1] || (n[1] = (n2) => e.nodeMoving = n2), props: e.keys, data: e.cloneData, horizontal: e.horizontal, "label-style": e.labelStyle, collapsable: e.collapsable, "render-content": e.renderContent, "label-class-name": e.labelClassName }, createSlots({ _: 2 }, [e.defaultSlot ? { name: "default", fn: withCtx(({ node: n2 }) => [renderSlot(e.$slots, "default", { node: n2 }, () => [createBaseVNode("div", Oe, [createBaseVNode("span", null, toDisplayString(n2[e.keys.label]), 1)])])]) } : void 0, e.expandSlot ? { name: "expand", fn: withCtx(({ node: n2 }) => [renderSlot(e.$slots, "expand", { node: n2 }, () => [we])]) } : void 0]), 1032, ["modelValue", "props", "data", "horizontal", "label-style", "collapsable", "render-content", "label-class-name"])) : createCommentVNode("v-if", true), e.nodeMenus.length ? (openBlock(), createBlock(g, { key: 2, modelValue: e.contextmenu, "onUpdate:modelValue": n[2] || (n[2] = (n2) => e.contextmenu = n2), x: e.menuX, y: e.menuY, node: e.menuData, data: e.data, props: e.keys, menus: e.nodeMenus, disabled: e.disabled, "node-add": e.nodeAdd, "node-delete": e.nodeDelete, "node-edit": e.nodeEdit, "node-copy": e.nodeCopy, onContextmenu: n[3] || (n[3] = (n2) => {
    e.$emit("on-contextmenu", n2);
  }), onOnNodeCopy: n[4] || (n[4] = (n2) => {
    e.$emit("on-node-copy", n2);
  }), onOnNodeDelete: n[5] || (n[5] = (n2) => {
    e.$emit("on-node-delete", n2);
  }) }, null, 8, ["modelValue", "x", "y", "node", "data", "props", "menus", "disabled", "node-add", "node-delete", "node-edit", "node-copy"])) : createCommentVNode("v-if", true)], 512);
}, De.__file = "src/components/tree-org/src/tree.vue", De.install = function(e) {
  e.component(De.name, De);
};
var _e = [De];
var Re = { install: function(e) {
  _e.forEach((n) => {
    e.component(n.name, n);
  }), e.use(V);
}, Vue3TreeOrg: De };
export {
  De as Vue3TreeOrg,
  Re as default
};
//# sourceMappingURL=vue3-tree-org.js.map
