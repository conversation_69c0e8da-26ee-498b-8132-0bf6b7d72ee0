/* tslint:disable */
 
/**
 * Admin.NET 通用权限开发平台
 * 让 .NET 开发更简单、更通用、更流行。整合最新技术，模块插件式开发，前后端分离，开箱即用。<br/><u><b><font color='FF0000'> 👮不得利用本项目从事危害国家安全、扰乱社会秩序、侵犯他人合法权益等法律法规禁止的活动！任何基于本项目二次开发而产生的一切法律纠纷和责任，我们不承担任何责任！</font></b></u>
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

import { CallingConventions } from './calling-conventions';
import { CustomAttributeData } from './custom-attribute-data';
import { MemberTypes } from './member-types';
import { MethodAttributes } from './method-attributes';
import { MethodImplAttributes } from './method-impl-attributes';
import { Module } from './module';
import { RuntimeMethodHandle } from './runtime-method-handle';
import { Type } from './type';
 /**
 * 
 *
 * @export
 * @interface MethodBase
 */
export interface MethodBase {

    /**
     * @type {MemberTypes}
     * @memberof MethodBase
     */
    memberType?: MemberTypes;

    /**
     * @type {string}
     * @memberof MethodBase
     */
    name?: string | null;

    /**
     * @type {Type}
     * @memberof MethodBase
     */
    declaringType?: Type;

    /**
     * @type {Type}
     * @memberof MethodBase
     */
    reflectedType?: Type;

    /**
     * @type {Module}
     * @memberof MethodBase
     */
    module?: Module;

    /**
     * @type {Array<CustomAttributeData>}
     * @memberof MethodBase
     */
    customAttributes?: Array<CustomAttributeData> | null;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isCollectible?: boolean;

    /**
     * @type {number}
     * @memberof MethodBase
     */
    metadataToken?: number;

    /**
     * @type {MethodAttributes}
     * @memberof MethodBase
     */
    attributes?: MethodAttributes;

    /**
     * @type {MethodImplAttributes}
     * @memberof MethodBase
     */
    methodImplementationFlags?: MethodImplAttributes;

    /**
     * @type {CallingConventions}
     * @memberof MethodBase
     */
    callingConvention?: CallingConventions;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isAbstract?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isConstructor?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isFinal?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isHideBySig?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isSpecialName?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isStatic?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isVirtual?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isAssembly?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isFamily?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isFamilyAndAssembly?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isFamilyOrAssembly?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isPrivate?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isPublic?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isConstructedGenericMethod?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isGenericMethod?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isGenericMethodDefinition?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    containsGenericParameters?: boolean;

    /**
     * @type {RuntimeMethodHandle}
     * @memberof MethodBase
     */
    methodHandle?: RuntimeMethodHandle;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isSecurityCritical?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isSecuritySafeCritical?: boolean;

    /**
     * @type {boolean}
     * @memberof MethodBase
     */
    isSecurityTransparent?: boolean;
}
