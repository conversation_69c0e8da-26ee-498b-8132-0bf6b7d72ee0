<template>
  <div class="dashboard-container">
    <!-- 概览卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-content">
            <div class="card-icon instances">
              <el-icon size="32"><Monitor /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">MQTT实例</div>
              <div class="card-value">{{ state.overview.totalInstances }}</div>
              <div class="card-subtitle">在线: {{ state.overview.onlineInstances }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-content">
            <div class="card-icon clients">
              <el-icon size="32"><User /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">活跃连接</div>
              <div class="card-value">{{ state.overview.activeConnections }}</div>
              <div class="card-subtitle">总连接: {{ state.overview.totalConnections }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-content">
            <div class="card-icon topics">
              <el-icon size="32"><ChatDotRound /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">主题数量</div>
              <div class="card-value">{{ state.overview.totalTopics }}</div>
              <div class="card-subtitle">活跃: {{ state.overview.activeTopics }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
        <el-card shadow="hover" class="overview-card">
          <div class="card-content">
            <div class="card-icon messages">
              <el-icon size="32"><Message /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">今日消息</div>
              <div class="card-value">{{ formatNumber(state.overview.todayMessages) }}</div>
              <div class="card-subtitle">总计: {{ formatNumber(state.overview.totalMessages) }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb20">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>连接数趋势</span>
              <el-button-group size="small">
                <el-button :type="state.connectionChart.timeRange === '1h' ? 'primary' : ''" @click="changeTimeRange('connection', '1h')">1小时</el-button>
                <el-button :type="state.connectionChart.timeRange === '24h' ? 'primary' : ''" @click="changeTimeRange('connection', '24h')">24小时</el-button>
                <el-button :type="state.connectionChart.timeRange === '7d' ? 'primary' : ''" @click="changeTimeRange('connection', '7d')">7天</el-button>
              </el-button-group>
            </div>
          </template>
          <div ref="connectionChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="card-header">
              <span>消息流量</span>
              <el-button-group size="small">
                <el-button :type="state.messageChart.timeRange === '1h' ? 'primary' : ''" @click="changeTimeRange('message', '1h')">1小时</el-button>
                <el-button :type="state.messageChart.timeRange === '24h' ? 'primary' : ''" @click="changeTimeRange('message', '24h')">24小时</el-button>
                <el-button :type="state.messageChart.timeRange === '7d' ? 'primary' : ''" @click="changeTimeRange('message', '7d')">7天</el-button>
              </el-button-group>
            </div>
          </template>
          <div ref="messageChartRef" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实例状态和活跃连接 -->
    <el-row :gutter="20" class="mb20">
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>实例状态</span>
              <el-button size="small" icon="Refresh" @click="refreshInstanceStatus">刷新</el-button>
            </div>
          </template>
          <el-table :data="state.instanceStatus" style="width: 100%" max-height="400">
            <el-table-column prop="instanceName" label="实例名称" show-overflow-tooltip />
            <el-table-column prop="instanceType" label="类型" width="80" />
            <el-table-column label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.isConnected ? 'success' : 'danger'" size="small">
                  {{ scope.row.isConnected ? '在线' : '离线' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="currentConnections" label="连接数" width="80" align="center" />
            <el-table-column prop="lastActivity" label="最后活动" width="120" show-overflow-tooltip />
          </el-table>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>活跃连接</span>
              <el-button size="small" icon="Refresh" @click="refreshActiveConnections">刷新</el-button>
            </div>
          </template>
          <el-table :data="state.activeConnections" style="width: 100%" max-height="400">
            <el-table-column prop="clientId" label="客户端ID" show-overflow-tooltip />
            <el-table-column prop="instanceName" label="实例" width="100" show-overflow-tooltip />
            <el-table-column prop="connectedAt" label="连接时间" width="120" show-overflow-tooltip />
            <el-table-column prop="lastActivity" label="最后活动" width="120" show-overflow-tooltip />
            <el-table-column label="状态" width="80" align="center">
              <template #default="scope">
                <el-tag type="success" size="small">在线</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统信息 -->
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <el-card shadow="hover">
          <template #header>
            <div class="card-header">
              <span>系统信息</span>
              <el-button size="small" icon="Refresh" @click="refreshSystemInfo">刷新</el-button>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
              <div class="system-info-item">
                <div class="info-label">系统运行时间</div>
                <div class="info-value">{{ state.systemInfo.uptime }}</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
              <div class="system-info-item">
                <div class="info-label">CPU使用率</div>
                <div class="info-value">{{ state.systemInfo.cpuUsage }}%</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
              <div class="system-info-item">
                <div class="info-label">内存使用率</div>
                <div class="info-value">{{ state.systemInfo.memoryUsage }}%</div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="6" :lg="6" :xl="6">
              <div class="system-info-item">
                <div class="info-label">网络流量</div>
                <div class="info-value">{{ state.systemInfo.networkTraffic }}</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup name="dashboard">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Monitor, User, ChatDotRound, Message } from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import { useEmqxRealtimeApi } from '/@/api/emqxRealtime/emqxRealtime';
import { useMqttInstanceApi } from '/@/api/mqttInstace/mqttInstance';

const emqxRealtimeApi = useEmqxRealtimeApi();
const mqttInstanceApi = useMqttInstanceApi();

const connectionChartRef = ref();
const messageChartRef = ref();
let connectionChart: echarts.ECharts | null = null;
let messageChart: echarts.ECharts | null = null;
let refreshTimer: NodeJS.Timeout | null = null;

const state = reactive({
  overview: {
    totalInstances: 0,
    onlineInstances: 0,
    activeConnections: 0,
    totalConnections: 0,
    totalTopics: 0,
    activeTopics: 0,
    todayMessages: 0,
    totalMessages: 0,
  },
  connectionChart: {
    timeRange: '1h',
    data: [] as any[],
  },
  messageChart: {
    timeRange: '1h',
    data: [] as any[],
  },
  instanceStatus: [] as any[],
  activeConnections: [] as any[],
  systemInfo: {
    uptime: '0天0小时0分钟',
    cpuUsage: 0,
    memoryUsage: 0,
    networkTraffic: '0 MB/s',
  },
});

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

// 初始化图表
const initCharts = async () => {
  await nextTick();
  
  // 连接数趋势图
  if (connectionChartRef.value) {
    connectionChart = echarts.init(connectionChartRef.value);
    updateConnectionChart();
  }
  
  // 消息流量图
  if (messageChartRef.value) {
    messageChart = echarts.init(messageChartRef.value);
    updateMessageChart();
  }
};

// 更新连接数图表
const updateConnectionChart = () => {
  if (!connectionChart) return;
  
  const option = {
    title: {
      show: false,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
    legend: {
      data: ['活跃连接', '总连接'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: generateTimeLabels(state.connectionChart.timeRange),
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '活跃连接',
        type: 'line',
        stack: 'Total',
        smooth: true,
        data: generateMockData(state.connectionChart.timeRange, 100, 500),
        itemStyle: {
          color: '#67C23A',
        },
      },
      {
        name: '总连接',
        type: 'line',
        stack: 'Total',
        smooth: true,
        data: generateMockData(state.connectionChart.timeRange, 200, 800),
        itemStyle: {
          color: '#409EFF',
        },
      },
    ],
  };
  
  connectionChart.setOption(option);
};

// 更新消息流量图表
const updateMessageChart = () => {
  if (!messageChart) return;
  
  const option = {
    title: {
      show: false,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
    },
    legend: {
      data: ['发布消息', '接收消息'],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: generateTimeLabels(state.messageChart.timeRange),
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '发布消息',
        type: 'line',
        smooth: true,
        data: generateMockData(state.messageChart.timeRange, 50, 200),
        itemStyle: {
          color: '#E6A23C',
        },
      },
      {
        name: '接收消息',
        type: 'line',
        smooth: true,
        data: generateMockData(state.messageChart.timeRange, 30, 150),
        itemStyle: {
          color: '#F56C6C',
        },
      },
    ],
  };
  
  messageChart.setOption(option);
};

// 生成时间标签
const generateTimeLabels = (timeRange: string) => {
  const labels = [];
  const now = new Date();
  
  if (timeRange === '1h') {
    for (let i = 59; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 1000);
      labels.push(time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }));
    }
  } else if (timeRange === '24h') {
    for (let i = 23; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 60 * 60 * 1000);
      labels.push(time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }));
    }
  } else if (timeRange === '7d') {
    for (let i = 6; i >= 0; i--) {
      const time = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      labels.push(time.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }));
    }
  }
  
  return labels;
};

// 生成模拟数据
const generateMockData = (timeRange: string, min: number, max: number) => {
  const data = [];
  let count = 0;
  
  if (timeRange === '1h') count = 60;
  else if (timeRange === '24h') count = 24;
  else if (timeRange === '7d') count = 7;
  
  for (let i = 0; i < count; i++) {
    data.push(Math.floor(Math.random() * (max - min + 1)) + min);
  }
  
  return data;
};

// 切换时间范围
const changeTimeRange = (chartType: string, timeRange: string) => {
  if (chartType === 'connection') {
    state.connectionChart.timeRange = timeRange;
    updateConnectionChart();
  } else if (chartType === 'message') {
    state.messageChart.timeRange = timeRange;
    updateMessageChart();
  }
};

// 加载概览数据
const loadOverviewData = async () => {
  try {
    // 模拟数据，实际应该调用API
    state.overview = {
      totalInstances: 12,
      onlineInstances: 10,
      activeConnections: 1248,
      totalConnections: 1500,
      totalTopics: 156,
      activeTopics: 89,
      todayMessages: 125680,
      totalMessages: 5678900,
    };
  } catch (error) {
    console.error('加载概览数据失败:', error);
  }
};

// 刷新实例状态
const refreshInstanceStatus = async () => {
  try {
    // 模拟数据，实际应该调用API
    state.instanceStatus = [
      { instanceName: 'EMQX-生产环境', instanceType: 'EMQX', isConnected: true, currentConnections: 856, lastActivity: '2分钟前' },
      { instanceName: 'EMQX-测试环境', instanceType: 'EMQX', isConnected: true, currentConnections: 234, lastActivity: '5分钟前' },
      { instanceName: '阿里云IoT-华东', instanceType: '阿里云', isConnected: true, currentConnections: 158, lastActivity: '1分钟前' },
      { instanceName: 'EMQX-开发环境', instanceType: 'EMQX', isConnected: false, currentConnections: 0, lastActivity: '2小时前' },
    ];
  } catch (error) {
    console.error('刷新实例状态失败:', error);
    ElMessage.error('刷新实例状态失败');
  }
};

// 刷新活跃连接
const refreshActiveConnections = async () => {
  try {
    // 模拟数据，实际应该调用API
    state.activeConnections = [
      { clientId: 'device_001', instanceName: 'EMQX-生产环境', connectedAt: '10:30:25', lastActivity: '刚刚' },
      { clientId: 'sensor_temp_01', instanceName: 'EMQX-生产环境', connectedAt: '09:15:42', lastActivity: '30秒前' },
      { clientId: 'gateway_hub_02', instanceName: 'EMQX-测试环境', connectedAt: '08:45:18', lastActivity: '1分钟前' },
      { clientId: 'mobile_app_user123', instanceName: '阿里云IoT-华东', connectedAt: '11:20:33', lastActivity: '5秒前' },
      { clientId: 'iot_device_456', instanceName: 'EMQX-生产环境', connectedAt: '07:30:15', lastActivity: '2分钟前' },
    ];
  } catch (error) {
    console.error('刷新活跃连接失败:', error);
    ElMessage.error('刷新活跃连接失败');
  }
};

// 刷新系统信息
const refreshSystemInfo = async () => {
  try {
    // 模拟数据，实际应该调用API
    state.systemInfo = {
      uptime: '15天8小时32分钟',
      cpuUsage: Math.floor(Math.random() * 30) + 10,
      memoryUsage: Math.floor(Math.random() * 40) + 30,
      networkTraffic: (Math.random() * 10 + 5).toFixed(1) + ' MB/s',
    };
  } catch (error) {
    console.error('刷新系统信息失败:', error);
    ElMessage.error('刷新系统信息失败');
  }
};

// 自动刷新数据
const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    loadOverviewData();
    refreshSystemInfo();
    updateConnectionChart();
    updateMessageChart();
  }, 30000); // 30秒刷新一次
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
};

// 页面加载时
onMounted(async () => {
  await loadOverviewData();
  await refreshInstanceStatus();
  await refreshActiveConnections();
  await refreshSystemInfo();
  await initCharts();
  startAutoRefresh();
});

// 页面卸载时
onUnmounted(() => {
  stopAutoRefresh();
  if (connectionChart) {
    connectionChart.dispose();
  }
  if (messageChart) {
    messageChart.dispose();
  }
});
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.overview-card {
  height: 120px;
  margin-bottom: 20px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.card-icon.instances {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.card-icon.clients {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.card-icon.topics {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.card-icon.messages {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.card-value {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin-bottom: 2px;
}

.card-subtitle {
  font-size: 12px;
  color: #999;
}

.chart-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.system-info-item {
  text-align: center;
  padding: 15px;
  border-radius: 8px;
  background: #f8f9fa;
  margin-bottom: 10px;
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.info-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.mb20 {
  margin-bottom: 20px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-card__body) {
  padding: 20px;
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }
  
  .overview-card {
    margin-bottom: 10px;
  }
  
  .card-value {
    font-size: 24px;
  }
  
  .chart-card {
    margin-bottom: 10px;
  }
}
</style>