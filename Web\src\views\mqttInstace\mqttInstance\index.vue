<script lang="ts" setup name="mqttInstance">
import { ref, reactive, onMounted, computed, watch } from "vue";
import { auth } from '/@/utils/authFunction';
import { ElMessageBox, ElMessage } from "element-plus";
import { 
  Search, Plus, Delete, Edit, Refresh, Download, Upload, 
  Connection, SwitchButton, Monitor, View, Warning, 
  CircleCheck, CircleClose, Link, Unlock, Bell, 
  Promotion, Message, DataAnalysis, Setting, Timer,
  ZoomIn, ZoomOut, Service, Close
} from '@element-plus/icons-vue';
import { downloadStreamFile } from "/@/utils/download";
import { useMqttInstanceApi } from '/@/api/mqttInstace/mqttInstance';
import { useEmqxRealtimeApi } from '/@/api/emqxRealtime/emqxRealtime';
import { useMqttRealtimeStatus } from '/@/composables/useMqttRealtimeStatus';
import editDialog from '/@/views/mqttInstace/mqttInstance/component/editDialog.vue'
import printDialog from '/@/views/system/print/component/hiprint/preview.vue'
import ModifyRecord from '/@/components/table/modifyRecord.vue';

const mqttInstanceApi = useMqttInstanceApi();
const emqxRealtimeApi = useEmqxRealtimeApi();
const printDialogRef = ref();
const editDialogRef = ref();

// 使用实时状态管理
const {
  connectionStatus,
  signalrConnected,
  signalrConnecting,
  statistics: realtimeStatistics,
  subscribeToInstanceUpdates,
  unsubscribeFromInstanceUpdates,
  getInstanceStatus,
  getOnlineInstancesCount,
  getOfflineInstancesCount,
  refreshInstanceStatus
} = useMqttRealtimeStatus();

const state = reactive({
  exportLoading: false,
  tableLoading: false,
  stores: {},
  showAdvanceQueryUI: false,
  dropdownData: {} as any,
  selectData: [] as any[],
  tableQueryParams: {} as any,
  tableParams: {
    page: 1,
    pageSize: 20,
    total: 0,
    field: 'createTime', // 默认的排序字段
    order: 'descending', // 排序方向
    descStr: 'descending', // 降序排序的关键字符
  },
  tableData: [],
  testingConnections: new Set(), // 正在测试的连接
});

// 页面加载时
onMounted(async () => {
  await handleQuery();
});

// 监听表格数据变化，自动订阅实例状态更新
watch(() => state.tableData, (newData) => {
  if (newData && newData.length > 0 && signalrConnected.value) {
    const instanceIds = newData.map((item: any) => item.id);
    subscribeToInstanceUpdates(instanceIds);
  }
}, { immediate: true });

// 监听SignalR连接状态
watch(signalrConnected, (connected) => {
  if (connected && state.tableData.length > 0) {
    const instanceIds = state.tableData.map((item: any) => item.id);
    subscribeToInstanceUpdates(instanceIds);
  }
});

// 查询操作
const handleQuery = async (params: any = {}) => {
  try {
    state.tableLoading = true;
    state.tableParams = Object.assign(state.tableParams, params);
    const result = await mqttInstanceApi.page(Object.assign(state.tableQueryParams, state.tableParams)).then(res => res.data.result);
    state.tableParams.total = result?.total;
    state.tableData = result?.items ?? [];
    
    // 如果SignalR已连接，自动订阅新的实例状态
    if (signalrConnected.value && state.tableData.length > 0) {
      const instanceIds = state.tableData.map((item: any) => item.id);
      try {
        await subscribeToInstanceUpdates(instanceIds);
      } catch (error) {
        console.warn('订阅实例状态更新失败:', error);
        // 降级到手动获取状态
        await fallbackRefreshStatus(instanceIds);
      }
    } else if (state.tableData.length > 0) {
      // SignalR未连接时，手动获取状态
      const instanceIds = state.tableData.map((item: any) => item.id);
      await fallbackRefreshStatus(instanceIds);
    }
  } catch (error: any) {
    console.error('查询数据失败:', error);
    ElMessage.error(`查询数据失败: ${error.message || '网络错误'}`);
    state.tableData = [];
    state.tableParams.total = 0;
  } finally {
    state.tableLoading = false;
  }
};

// 列排序
const sortChange = async (column: any) => {
  state.tableParams.field = column.prop;
  state.tableParams.order = column.order;
  await handleQuery();
};

// 删除
const delMqttInstance = (row: any) => {
  ElMessageBox.confirm(`确定要删除实例 "${row.instanceName}" 吗?`, "删除确认", {
    confirmButtonText: "确定删除",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      await mqttInstanceApi.delete({ id: row.id });
      // 清理本地状态
      connectionStatus.delete(row.id);
      // 取消订阅
      if (signalrConnected.value) {
        unsubscribeFromInstanceUpdates([row.id]);
      }
      await handleQuery();
      ElMessage.success(`实例 "${row.instanceName}" 删除成功`);
    } catch (error: any) {
      console.error('删除实例失败:', error);
      ElMessage.error(`删除实例失败: ${error.message || '网络错误'}`);
    }
  }).catch(() => {});
};

// 批量删除
const batchDelMqttInstance = () => {
  ElMessageBox.confirm(`确定要删除选中的 ${state.selectData.length} 个实例吗?`, "批量删除确认", {
    confirmButtonText: "确定删除",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      const result = await mqttInstanceApi.batchDelete(state.selectData.map(u => ({ id: u.id })));
      
      // 清理本地状态
      const deletedIds = state.selectData.map(u => u.id);
      deletedIds.forEach(id => connectionStatus.delete(id));
      
      // 取消订阅
      if (signalrConnected.value) {
        unsubscribeFromInstanceUpdates(deletedIds);
      }
      
      ElMessage.success(`成功批量删除 ${result.data.result} 条记录`);
      await handleQuery();
    } catch (error: any) {
      console.error('批量删除失败:', error);
      ElMessage.error(`批量删除失败: ${error.message || '网络错误'}`);
    }
  }).catch(() => {});
};

// 测试连接
const testConnection = async (row: any) => {
  if (state.testingConnections.has(row.id)) {
    return;
  }
  
  state.testingConnections.add(row.id);
  try {
    console.log('开始连接测试，实例ID:', row.id);
    console.log('emqxRealtimeApi:', emqxRealtimeApi);
    const result = await emqxRealtimeApi.connect({ instanceId: row.id });
    console.log('连接测试结果:', result);
    if (result.data.success) {
      ElMessage.success(`实例 "${row.instanceName}" 连接测试成功`);
      // 实时状态会通过SignalR自动更新，如果SignalR未连接则手动更新状态
      if (!signalrConnected.value) {
        setTimeout(async () => {
          try {
            const statusResult = await emqxRealtimeApi.getConnectionStatus({ instanceId: row.id });
            if (statusResult.data.success) {
              const status = statusResult.data.result;
              connectionStatus.set(row.id, {
                isConnected: status.isConnected,
                status: status.status,
                lastHeartbeat: status.lastHeartbeat,
                connectionTime: status.connectionTime,
                checkTime: new Date().toLocaleTimeString()
              });
            }
          } catch (error) {
            console.warn('获取连接状态失败:', error);
          }
        }, 1000);
      }
    } else {
      ElMessage.error(`实例 "${row.instanceName}" 连接测试失败: ${result.data.message || '未知错误'}`);
    }
  } catch (error: any) {
    console.error('连接测试异常:', error);
    console.error('错误详情:', {
      message: error.message,
      response: error.response,
      request: error.request,
      stack: error.stack
    });
    ElMessage.error(`实例 "${row.instanceName}" 连接测试异常: ${error.message || '网络错误'}`);
  } finally {
    state.testingConnections.delete(row.id);
  }
};

// 断开连接
const disconnectInstance = async (row: any) => {
  try {
    const result = await emqxRealtimeApi.disconnect({ instanceId: row.id });
    if (result.data.success) {
      ElMessage.success(`实例 "${row.instanceName}" 断开连接成功`);
      // 实时状态会通过SignalR自动更新，如果SignalR未连接则手动更新状态
      if (!signalrConnected.value) {
        connectionStatus.set(row.id, {
          isConnected: false,
          status: '已断开',
          lastHeartbeat: null,
          connectionTime: null,
          checkTime: new Date().toLocaleTimeString()
        });
      }
    } else {
      ElMessage.error(`实例 "${row.instanceName}" 断开连接失败: ${result.data.message || '未知错误'}`);
    }
  } catch (error: any) {
    console.error('断开连接异常:', error);
    ElMessage.error(`实例 "${row.instanceName}" 断开连接异常: ${error.message || '网络错误'}`);
  }
};

// 获取连接状态（已由实时订阅服务处理）
// const getConnectionStatus = async (instanceId: number) => {
//   实时状态通过SignalR自动更新，无需手动调用
// };

// 批量连接
const batchConnect = async () => {
  if (state.selectData.length === 0) {
    ElMessage.warning('请选择要连接的实例');
    return;
  }
  
  try {
    state.tableLoading = true;
    const instanceIds = state.selectData.map(item => item.id);
    const result = await emqxRealtimeApi.batchConnect({ instanceIds });
    if (result.data.success) {
      const { successCount, failureCount } = result.data.result;
      ElMessage.success(`批量连接操作完成，成功: ${successCount}，失败: ${failureCount}`);
      
      // 如果SignalR未连接，延迟刷新状态
      if (!signalrConnected.value && successCount > 0) {
        setTimeout(async () => {
          await fallbackRefreshStatus(instanceIds);
        }, 2000);
      }
    } else {
      ElMessage.error(`批量连接操作失败: ${result.data.message || '未知错误'}`);
    }
  } catch (error: any) {
    console.error('批量连接操作失败:', error);
    ElMessage.error(`批量连接操作失败: ${error.message || '网络错误'}`);
  } finally {
    state.tableLoading = false;
  }
};

// 批量断开
const batchDisconnect = async () => {
  if (state.selectData.length === 0) {
    ElMessage.warning('请选择要断开的实例');
    return;
  }
  
  try {
    state.tableLoading = true;
    const instanceIds = state.selectData.map(item => item.id);
    const result = await emqxRealtimeApi.batchDisconnect({ instanceIds });
    if (result.data.success) {
      const { successCount, failureCount } = result.data.result;
      ElMessage.success(`批量断开操作完成，成功: ${successCount}，失败: ${failureCount}`);
      
      // 如果SignalR未连接，手动更新状态
      if (!signalrConnected.value && successCount > 0) {
        instanceIds.forEach(id => {
          connectionStatus.set(id, {
            isConnected: false,
            status: '已断开',
            lastHeartbeat: null,
            connectionTime: null,
            checkTime: new Date().toLocaleTimeString()
          });
        });
      }
    } else {
      ElMessage.error(`批量断开操作失败: ${result.data.message || '未知错误'}`);
    }
  } catch (error: any) {
    console.error('批量断开操作失败:', error);
    ElMessage.error(`批量断开操作失败: ${error.message || '网络错误'}`);
  } finally {
    state.tableLoading = false;
  }
};

// 刷新所有连接状态
const refreshAllConnectionStatus = async () => {
  if (state.tableData.length === 0) {
    ElMessage.warning('没有实例数据需要刷新');
    return;
  }
  
  const instanceIds = state.tableData.map((item: any) => item.id);
  
  if (signalrConnected.value) {
    // 如果SignalR已连接，使用实时订阅服务刷新
    try {
      await refreshInstanceStatus(instanceIds);
      ElMessage.success('状态刷新完成');
    } catch (error) {
      console.error('SignalR刷新状态失败:', error);
      ElMessage.error('实时刷新失败，正在尝试降级模式...');
      // 降级到API模式
      await fallbackRefreshStatus(instanceIds);
    }
  } else {
    // 如果SignalR未连接，通过API直接获取状态
    await fallbackRefreshStatus(instanceIds);
  }
};

// 降级刷新状态方法
const fallbackRefreshStatus = async (instanceIds: number[]) => {
  try {
    state.tableLoading = true;
    let successCount = 0;
    let failureCount = 0;
    
    for (const instanceId of instanceIds) {
      try {
        const result = await emqxRealtimeApi.getConnectionStatus({ instanceId });
        if (result.data.success) {
          // 手动更新连接状态到本地状态管理
          const status = result.data.result;
          connectionStatus.set(instanceId, {
            isConnected: status.isConnected,
            status: status.status,
            lastHeartbeat: status.lastHeartbeat,
            connectionTime: status.connectionTime,
            checkTime: new Date().toLocaleTimeString()
          });
          successCount++;
        } else {
          failureCount++;
        }
      } catch (error) {
        console.warn(`获取实例 ${instanceId} 状态失败:`, error);
        failureCount++;
      }
    }
    
    if (successCount > 0) {
      ElMessage.success(`状态刷新完成（降级模式）- 成功: ${successCount}, 失败: ${failureCount}`);
    } else {
      ElMessage.error('状态刷新失败，所有实例状态获取失败');
    }
  } catch (error) {
    console.error('降级刷新状态失败:', error);
    ElMessage.error('状态刷新失败，请检查网络连接');
  } finally {
    state.tableLoading = false;
  }
};

// 统计函数（使用实时订阅服务）
const getTotalInstancesCount = () => {
  return state.tableData.length;
};

// 在线和离线实例数量由实时订阅服务提供
// getOnlineInstancesCount 和 getOfflineInstancesCount 已在 useMqttRealtimeStatus 中定义

// 实例类型颜色
const getInstanceTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'EMQX': 'success',
    '阿里云': 'warning',
    '其他': 'info'
  };
  return colorMap[type] || 'info';
};


// 同步版本的连接状态函数（用于模板中的同步调用）
const getConnectionStatusTypeSync = (instanceId: number) => {
  const status = getInstanceStatus(instanceId);
  if (!status) return 'info';
  return status.isConnected ? 'success' : 'danger';
};

const getConnectionStatusIconSync = (instanceId: number) => {
  const status = getInstanceStatus(instanceId);
  if (!status) return Warning;
  return status.isConnected ? CircleCheck : CircleClose;
};

const getConnectionStatusTextSync = (instanceId: number) => {
  const status = getInstanceStatus(instanceId);
  if (!status) return '未检测';
  return status.status || '未知状态';
};

// 获取SignalR连接状态显示
const getSignalRStatusIcon = computed(() => {
  if (signalrConnecting.value) return Timer;
  return signalrConnected.value ? Service : Close;
});

const getSignalRStatusType = computed(() => {
  if (signalrConnecting.value) return 'warning';
  return signalrConnected.value ? 'success' : 'danger';
});

const getSignalRStatusText = computed(() => {
  if (signalrConnecting.value) return '连接中';
  return signalrConnected.value ? '已连接' : '未连接';
});

// 批量更新连接状态（已移除，由实时订阅自动处理）

// 格式化数字
const formatNumber = (num: number | string | null | undefined) => {
  if (!num) return '0';
  const number = typeof num === 'string' ? parseInt(num) : num;
  if (number >= 1000000) {
    return (number / 1000000).toFixed(1) + 'M';
  } else if (number >= 1000) {
    return (number / 1000).toFixed(1) + 'K';
  }
  return number.toString();
};

// 格式化日期
const formatDate = (dateStr: string | null | undefined) => {
  if (!dateStr) return null;
  return new Date(dateStr).toLocaleString();
};

// 查看实例详情
const viewInstanceDetails = (instance: any) => {
  const details = `
    <div style="text-align: left; line-height: 1.8;">
      <h3 style="margin-bottom: 15px; color: #409EFF;">📊 ${instance.instanceName}</h3>
      
      <div style="margin-bottom: 12px;">
        <strong>🏷️ 基本信息:</strong><br/>
        实例编码: ${instance.instanceCode || '未设置'}<br/>
        实例类型: ${instance.instanceType || '未设置'}<br/>
        启用状态: ${instance.isEnabled ? '✅ 已启用' : '❌ 已禁用'}
      </div>
      
      <div style="margin-bottom: 12px;">
        <strong>🔗 连接配置:</strong><br/>
        服务器: ${instance.serverHost}:${instance.serverPort}<br/>
        API地址: ${instance.apiHost}:${instance.apiPort}<br/>
        SSL: ${instance.enableSsl ? '✅ 已启用' : '❌ 未启用'}<br/>
        WebSocket: ${instance.enableWebSocket ? '✅ 已启用' : '❌ 未启用'}
      </div>
      
      <div style="margin-bottom: 12px;">
        <strong>📈 连接统计:</strong><br/>
        最大连接数: ${instance.maxConnections || 0}<br/>
        当前连接数: ${instance.currentConnections || 0}<br/>
        总消息数: ${formatNumber(instance.totalMessages)}<br/>
        总字节数: ${formatNumber(instance.totalBytes)}
      </div>
      
      <div style="margin-bottom: 12px;">
        <strong>⏰ 时间信息:</strong><br/>
        最后心跳: ${instance.lastHeartbeat || '无记录'}<br/>
        最后活动: ${instance.lastActivity || '无记录'}
      </div>
      
      ${instance.remark ? `<div><strong>📝 备注:</strong><br/>${instance.remark}</div>` : ''}
    </div>
  `;
  
  ElMessageBox.alert(details, '实例详情', {
    dangerouslyUseHTMLString: true,
    customClass: 'instance-details-dialog'
  });
};

// 重置搜索参数
const resetSearchParams = () => {
  state.tableQueryParams = {};
  handleQuery();
};

// 导出数据
const exportData = async () => {
  if (state.tableData.length === 0) {
    ElMessage.warning('没有数据可以导出');
    return;
  }
  
  try {
    state.exportLoading = true;
    ElMessage.info('正在准备导出数据...');
    
    // 这里可以调用实际的导出API
    // const result = await mqttInstanceApi.export(state.tableQueryParams);
    // downloadStreamFile(result, 'mqtt-instances.xlsx');
    
    // 临时提示
    setTimeout(() => {
      ElMessage.success('导出功能即将上线，敬请期待');
    }, 1000);
  } catch (error: any) {
    console.error('导出失败:', error);
    ElMessage.error(`导出失败: ${error.message || '网络错误'}`);
  } finally {
    state.exportLoading = false;
  }
};

handleQuery();
</script>
<template>
  <div class="mqtt-instance-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon class="title-icon"><DataAnalysis /></el-icon>
            MQTT实例管理
          </h1>
          <p class="page-description">管理和监控MQTT实例的连接状态、配置信息和运行统计</p>
        </div>
        <div class="header-stats">
          <div class="stat-card online">
            <div class="stat-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getOnlineInstancesCount() }}</div>
              <div class="stat-label">在线实例</div>
            </div>
          </div>
          <div class="stat-card offline">
            <div class="stat-icon">
              <el-icon><CircleClose /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getOfflineInstancesCount() }}</div>
              <div class="stat-label">离线实例</div>
            </div>
          </div>
          <div class="stat-card total">
            <div class="stat-icon">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getTotalInstancesCount() }}</div>
              <div class="stat-label">总实例数</div>
            </div>
          </div>
          <div class="stat-card realtime">
            <div class="stat-icon">
              <el-icon><component :is="getSignalRStatusIcon" /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ getSignalRStatusText }}</div>
              <div class="stat-label">实时订阅</div>
            </div>
          </div>
          <div class="stat-card statistics">
            <div class="stat-icon">
              <el-icon><Message /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ formatNumber(realtimeStatistics.totalMessages || 0) }}</div>
              <div class="stat-label">总消息数</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="hover">
      <el-form :model="state.tableQueryParams" ref="queryForm" :inline="true">
        <el-row :gutter="16">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="关键词">
              <el-input 
                v-model="state.tableQueryParams.keyword" 
                placeholder="实例名称/编码" 
                clearable
                :prefix-icon="Search"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="实例类型">
              <el-select v-model="state.tableQueryParams.instanceType" placeholder="选择实例类型" clearable>
                <el-option label="EMQX" value="EMQX" />
                <el-option label="阿里云" value="阿里云" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="连接状态">
              <el-select v-model="state.tableQueryParams.status" placeholder="选择连接状态" clearable>
                <el-option label="在线" value="online" />
                <el-option label="离线" value="offline" />
                <el-option label="未知" value="unknown" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-form-item label="启用状态">
              <el-select v-model="state.tableQueryParams.isEnabled" placeholder="选择启用状态" clearable>
                <el-option label="已启用" value="true" />
                <el-option label="已禁用" value="false" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row class="search-actions">
          <el-col :span="24">
            <div class="action-buttons">
              <div class="search-buttons">
                <el-button type="primary" :icon="Search" @click="handleQuery">查询</el-button>
                <el-button :icon="Refresh" @click="resetSearchParams">重置</el-button>
                <el-button 
                  :icon="state.showAdvanceQueryUI ? ZoomOut : ZoomIn" 
                  @click="state.showAdvanceQueryUI = !state.showAdvanceQueryUI"
                >
                  {{ state.showAdvanceQueryUI ? '收起' : '高级搜索' }}
                </el-button>
              </div>
              <div class="operation-buttons">
                <el-button 
                  type="danger" 
                  :icon="Delete" 
                  @click="batchDelMqttInstance" 
                  :disabled="state.selectData.length === 0"
                >
                  批量删除
                </el-button>
                <el-button 
                  type="success" 
                  :icon="Connection" 
                  @click="batchConnect" 
                  :disabled="state.selectData.length === 0"
                >
                  批量连接
                </el-button>
                <el-button 
                  type="warning" 
                  :icon="SwitchButton" 
                  @click="batchDisconnect" 
                  :disabled="state.selectData.length === 0"
                >
                  批量断开
                </el-button>
                <el-button type="info" :icon="Refresh" @click="refreshAllConnectionStatus">刷新状态</el-button>
                <el-button type="primary" :icon="Plus" @click="editDialogRef.openDialog(null, '新增MQTT实例')">新增实例</el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 高级搜索区域 -->
    <el-card v-if="state.showAdvanceQueryUI" class="advanced-search-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span><el-icon><Setting /></el-icon> 高级搜索</span>
        </div>
      </template>
      <el-form :model="state.tableQueryParams" :inline="true">
        <el-row :gutter="16">
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
            <el-form-item label="实例名称">
              <el-input v-model="state.tableQueryParams.instanceName" clearable placeholder="请输入实例名称"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10">
            <el-form-item label="实例编码">
              <el-input v-model="state.tableQueryParams.instanceCode" clearable placeholder="请输入实例编码"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" class="mb10">
            <el-form-item label="服务器地址">
              <el-input v-model="state.tableQueryParams.serverHost" clearable placeholder="请输入服务器地址"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="8" :lg="6" class="mb10">
            <el-form-item label="服务器端口">
              <el-input-number v-model="state.tableQueryParams.serverPort" clearable placeholder="请输入服务器端口"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="实例类型（EMQX、阿里云等）">
              <el-input v-model="state.tableQueryParams.instanceType" clearable placeholder="请输入实例类型（EMQX、阿里云等）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="服务器地址">
              <el-input v-model="state.tableQueryParams.serverHost" clearable placeholder="请输入服务器地址"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="服务器端口">
              <el-input-number v-model="state.tableQueryParams.serverPort"  clearable placeholder="请输入服务器端口"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="API地址">
              <el-input v-model="state.tableQueryParams.apiHost" clearable placeholder="请输入API地址"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="API端口">
              <el-input-number v-model="state.tableQueryParams.apiPort"  clearable placeholder="请输入API端口"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="API用户名">
              <el-input v-model="state.tableQueryParams.apiUsername" clearable placeholder="请输入API用户名"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="API密码（加密存储）">
              <el-input v-model="state.tableQueryParams.apiPassword" clearable placeholder="请输入API密码（加密存储）"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="实例状态">
              <el-input v-model="state.tableQueryParams.status" clearable placeholder="请输入实例状态"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="是否启用SSL（0:否，1:是）">
                  <el-select clearable filterable v-model="state.tableQueryParams.enableSsl" placeholder="请选择是否启用SSL（0:否，1:是）"> 
                    <el-option     value="true" label="是" /> 
                    <el-option     value="false" label="否" />  
                  </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="SSL端口">
              <el-input-number v-model="state.tableQueryParams.sslPort"  clearable placeholder="请输入SSL端口"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="是否启用WebSocket（0:否，1:是）">
                  <el-select clearable filterable v-model="state.tableQueryParams.enableWebSocket" placeholder="请选择是否启用WebSocket（0:否，1:是）"> 
                    <el-option     value="true" label="是" /> 
                    <el-option     value="false" label="否" />  
                  </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="WebSocket端口">
              <el-input-number v-model="state.tableQueryParams.wsPort"  clearable placeholder="请输入WebSocket端口"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="WebSocket SSL端口">
              <el-input-number v-model="state.tableQueryParams.wssPort"  clearable placeholder="请输入WebSocket SSL端口"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="最大连接数">
              <el-input-number v-model="state.tableQueryParams.maxConnections"  clearable placeholder="请输入最大连接数"/>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="12" :md="12" :lg="8" :xl="4" class="mb10" v-if="state.showAdvanceQueryUI">
            <el-form-item label="当前连接数">
              <el-input-number v-model="state.tableQueryParams.currentConnections"  clearable placeholder="请输入当前连接数"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <!-- 实例列表 -->
    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <span><el-icon><DataAnalysis /></el-icon> 实例列表</span>
          <div class="header-actions">
            <el-button :icon="Download" size="small" @click="exportData" :loading="state.exportLoading">导出</el-button>
          </div>
        </div>
      </template>
      
      <el-table 
        :data="state.tableData" 
        @selection-change="(val: any[]) => { state.selectData = val; }" 
        v-loading="state.tableLoading" 
        row-key="id" 
        @sort-change="sortChange"
        class="modern-table"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column type="index" label="#" width="60" align="center" />
        
        <el-table-column prop="instanceName" label="实例信息" min-width="200" show-overflow-tooltip>
          <template #default="scope">
            <div class="instance-info">
              <div class="instance-name">
                <el-icon class="instance-icon"><DataAnalysis /></el-icon>
                {{ scope.row.instanceName }}
              </div>
              <div class="instance-details">
                <span class="instance-code">{{ scope.row.instanceCode }}</span>
                <el-tag 
                  :type="getInstanceTypeColor(scope.row.instanceType)" 
                  size="small" 
                  class="instance-type-tag"
                >
                  <el-icon><Setting /></el-icon>
                  {{ scope.row.instanceType }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="连接信息" min-width="180" show-overflow-tooltip>
          <template #default="scope">
            <div class="connection-info">
              <div class="server-info">
                <el-icon><Link /></el-icon>
                {{ scope.row.serverHost }}:{{ scope.row.serverPort }}
              </div>
              <div class="api-info">
                <el-icon><Setting /></el-icon>
                API: {{ scope.row.apiHost }}:{{ scope.row.apiPort }}
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="连接状态" width="120" align="center">
          <template #default="scope">
            <div class="connection-status">
              <el-tag 
                :type="getConnectionStatusTypeSync(scope.row.id)" 
                :icon="getConnectionStatusIconSync(scope.row.id)"
                class="status-tag"
              >
                {{ getConnectionStatusTextSync(scope.row.id) }}
              </el-tag>
              <div class="status-time" v-if="connectionStatus.has(scope.row.id)">
                {{ connectionStatus.get(scope.row.id).checkTime }}
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="连接统计" min-width="140" align="center">
          <template #default="scope">
            <div class="connection-stats">
              <div class="stat-item">
                <span class="stat-label">当前/最大:</span>
                <span class="stat-value">{{ scope.row.currentConnections || 0 }}/{{ scope.row.maxConnections || 0 }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">消息数:</span>
                <span class="stat-value">{{ formatNumber(scope.row.totalMessages) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="配置状态" width="120" align="center">
          <template #default="scope">
            <div class="config-status">
              <el-tag 
                :type="scope.row.enableSsl ? 'success' : 'info'" 
                size="small" 
                class="config-tag"
              >
                <el-icon><Lock /></el-icon>
                {{ scope.row.enableSsl ? 'SSL' : '普通' }}
              </el-tag>
              <el-tag 
                :type="scope.row.enableWebSocket ? 'success' : 'info'" 
                size="small" 
                class="config-tag"
              >
                <el-icon><Connection /></el-icon>
                {{ scope.row.enableWebSocket ? 'WS' : '标准' }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="启用状态" width="100" align="center">
          <template #default="scope">
            <el-tag 
              :type="scope.row.isEnabled ? 'success' : 'danger'" 
              :icon="scope.row.isEnabled ? CircleCheck : CircleClose"
            >
              {{ scope.row.isEnabled ? '已启用' : '已禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="最后活动" width="120" show-overflow-tooltip>
          <template #default="scope">
            <div class="activity-time">
              <el-icon><Timer /></el-icon>
              {{ formatDate(scope.row.lastActivity) || '无记录' }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <div class="operation-buttons">
              <div class="button-row">
                <el-button 
                  :icon="Connection" 
                  size="small" 
                  type="success" 
                  circle 
                  @click="testConnection(scope.row)" 
                  :loading="state.testingConnections.has(scope.row.id)"
                  title="测试连接"
                />
                <el-button 
                  :icon="SwitchButton" 
                  size="small" 
                  type="warning" 
                  circle 
                  @click="disconnectInstance(scope.row)" 
                  v-if="connectionStatus.get(scope.row.id)?.isConnected"
                  title="断开连接"
                />
                <el-button 
                  :icon="View" 
                  size="small" 
                  type="info" 
                  circle 
                  @click="viewInstanceDetails(scope.row)"
                  title="查看详情"
                />
                <el-button 
                  :icon="Edit" 
                  size="small" 
                  type="primary" 
                  circle 
                  @click="editDialogRef.openDialog(scope.row, '编辑MQTT实例')"
                  title="编辑"
                />
                <el-button 
                  :icon="Delete" 
                  size="small" 
                  type="danger" 
                  circle 
                  @click="delMqttInstance(scope.row)"
                  title="删除"
                />
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination 
          v-model:currentPage="state.tableParams.page"
          v-model:page-size="state.tableParams.pageSize"
          @size-change="(val: any) => handleQuery({ pageSize: val })"
          @current-change="(val: any) => handleQuery({ page: val })"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[10, 20, 50, 100]"
          :total="state.tableParams.total"
          background
        />
      </div>
    </el-card>
    
    <!-- 对话框 -->
    <printDialog ref="printDialogRef" :title="'打印MQTT实例'" @reloadTable="handleQuery" />
    <editDialog ref="editDialogRef" @reloadTable="handleQuery" />
  </div>
</template>
<style scoped>
/* 主容器 */
.mqtt-instance-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 32px;
}

.page-description {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.header-stats {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.stat-card {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  min-width: 120px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 24px;
  margin-bottom: 8px;
  opacity: 0.9;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.8;
}

.stat-card.online .stat-icon {
  color: #67c23a;
}

.stat-card.offline .stat-icon {
  color: #f56c6c;
}

.stat-card.total .stat-icon {
  color: #409eff;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-actions {
  margin-top: 16px;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.search-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.operation-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 高级搜索卡片 */
.advanced-search-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.card-header span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 表格卡片 */
.table-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.modern-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 实例信息 */
.instance-info {
  padding: 8px 0;
}

.instance-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 6px;
}

.instance-icon {
  color: #409eff;
  font-size: 16px;
}

.instance-details {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.instance-code {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.instance-type-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 连接信息 */
.connection-info {
  padding: 8px 0;
}

.server-info,
.api-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #606266;
  margin-bottom: 4px;
}

.server-info {
  font-weight: 600;
  color: #303133;
}

/* 连接状态 */
.connection-status {
  text-align: center;
}

.status-tag {
  margin-bottom: 4px;
}

.status-time {
  font-size: 11px;
  color: #909399;
  margin-top: 4px;
}

/* 连接统计 */
.connection-stats {
  text-align: center;
}

.stat-item {
  margin-bottom: 6px;
  font-size: 12px;
}

.stat-label {
  color: #909399;
  margin-right: 4px;
}

.stat-value {
  font-weight: 600;
  color: #303133;
}

/* 配置状态 */
.config-status {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.config-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 活动时间 */
.activity-time {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #606266;
}

/* 操作按钮 */
.operation-buttons {
  display: flex;
  justify-content: center;
}

.button-row {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: center;
}

/* 分页容器 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 表单样式 */
:deep(.el-input), 
:deep(.el-select), 
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

/* 按钮样式 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button.is-circle) {
  width: 32px;
  height: 32px;
}

/* 标签样式 */
:deep(.el-tag) {
  border-radius: 6px;
  font-weight: 500;
}

/* 表格样式 */
:deep(.el-table) {
  border-radius: 8px;
}

:deep(.el-table th) {
  background: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-table tr:hover td) {
  background: #f8f9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mqtt-instance-container {
    padding: 12px;
  }
  
  .page-header {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-stats {
    width: 100%;
    justify-content: space-between;
  }
  
  .stat-card {
    flex: 1;
    min-width: 100px;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-buttons,
  .operation-buttons {
    justify-content: center;
  }
  
  .button-row {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 24px;
  }
  
  .stat-card {
    padding: 12px;
  }
  
  .stat-value {
    font-size: 20px;
  }
}

/* 实例详情对话框样式 */
:deep(.instance-details-dialog) {
  border-radius: 12px;
}

:deep(.instance-details-dialog .el-message-box__content) {
  padding: 20px;
}
</style>